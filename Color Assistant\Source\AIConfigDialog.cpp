/*
  ==============================================================================

    AIConfigDialog.cpp
    Created: AI Chord Assistant Plugin - Stage 3
    Author:  AI Assistant

    Implementation of the AI configuration dialog and enhanced chat interface.

  ==============================================================================
*/

#include "AIConfigDialog.h"

//==============================================================================
// AIConfigDialog Implementation
//==============================================================================

AIConfigDialog::AIConfigDialog()
{
    setupComponents();
    setSize(500, 600);
}

AIConfigDialog::~AIConfigDialog()
{
}

void AIConfigDialog::paint(juce::Graphics& g)
{
    g.fillAll(juce::Colours::lightgrey);
    g.setColour(juce::Colours::black);
    g.drawRect(getLocalBounds(), 2);
}

void AIConfigDialog::resized()
{
    auto bounds = getLocalBounds().reduced(20);
    
    // 标题
    titleLabel.setBounds(bounds.removeFromTop(30));
    bounds.removeFromTop(10);
    
    // 提供商选择
    auto providerArea = bounds.removeFromTop(25);
    providerLabel.setBounds(providerArea.removeFromLeft(100));
    providerComboBox.setBounds(providerArea);
    bounds.removeFromTop(10);
    
    // API配置
    auto apiKeyArea = bounds.removeFromTop(25);
    apiKeyLabel.setBounds(apiKeyArea.removeFromLeft(100));
    apiKeyEditor.setBounds(apiKeyArea);
    bounds.removeFromTop(5);
    
    auto baseUrlArea = bounds.removeFromTop(25);
    baseUrlLabel.setBounds(baseUrlArea.removeFromLeft(100));
    baseUrlEditor.setBounds(baseUrlArea);
    bounds.removeFromTop(5);
    
    auto modelArea = bounds.removeFromTop(25);
    modelLabel.setBounds(modelArea.removeFromLeft(100));
    modelEditor.setBounds(modelArea);
    bounds.removeFromTop(15);
    
    // 参数配置
    auto tempArea = bounds.removeFromTop(50);
    temperatureLabel.setBounds(tempArea.removeFromTop(20));
    temperatureSlider.setBounds(tempArea);
    bounds.removeFromTop(5);
    
    auto tokensArea = bounds.removeFromTop(50);
    maxTokensLabel.setBounds(tokensArea.removeFromTop(20));
    maxTokensSlider.setBounds(tokensArea);
    bounds.removeFromTop(5);
    
    auto timeoutArea = bounds.removeFromTop(50);
    timeoutLabel.setBounds(timeoutArea.removeFromTop(20));
    timeoutSlider.setBounds(timeoutArea);
    bounds.removeFromTop(15);
    
    // 状态显示
    statusLabel.setBounds(bounds.removeFromTop(30));
    bounds.removeFromTop(10);
    
    // 按钮
    auto buttonArea = bounds.removeFromBottom(35);
    auto buttonWidth = buttonArea.getWidth() / 4 - 5;
    
    testButton.setBounds(buttonArea.removeFromLeft(buttonWidth));
    buttonArea.removeFromLeft(5);
    resetButton.setBounds(buttonArea.removeFromLeft(buttonWidth));
    buttonArea.removeFromLeft(5);
    saveButton.setBounds(buttonArea.removeFromLeft(buttonWidth));
    buttonArea.removeFromLeft(5);
    cancelButton.setBounds(buttonArea);
}

void AIConfigDialog::showDialog(const AIConfig& config)
{
    currentConfig = config;
    updateUIFromConfig();
    setVisible(true);
}

void AIConfigDialog::buttonClicked(juce::Button* button)
{
    if (button == &saveButton)
    {
        updateConfigFromUI();
        if (AIConfigManager::validateConfig(currentConfig))
        {
            if (onConfigChanged)
                onConfigChanged(currentConfig);
            setVisible(false);
        }
        else
        {
            statusLabel.setText("配置验证失败，请检查输入", juce::dontSendNotification);
            statusLabel.setColour(juce::Label::textColourId, juce::Colours::red);
        }
    }
    else if (button == &cancelButton)
    {
        if (onCancelled)
            onCancelled();
        setVisible(false);
    }
    else if (button == &resetButton)
    {
        resetToDefaults();
    }
    else if (button == &testButton)
    {
        testConnection();
    }
}

void AIConfigDialog::comboBoxChanged(juce::ComboBox* comboBoxThatHasChanged)
{
    if (comboBoxThatHasChanged == &providerComboBox)
    {
        updateProviderDefaults();
    }
}

void AIConfigDialog::setupComponents()
{
    // 标题
    addAndMakeVisible(titleLabel);
    titleLabel.setText("AI配置", juce::dontSendNotification);
    titleLabel.setFont(juce::Font(18.0f, juce::Font::bold));
    titleLabel.setJustificationType(juce::Justification::centred);
    
    // 提供商选择
    addAndMakeVisible(providerLabel);
    providerLabel.setText("AI提供商:", juce::dontSendNotification);
    
    addAndMakeVisible(providerComboBox);
    providerComboBox.addItem("OpenAI", 1);
    providerComboBox.addItem("Groq", 2);
    providerComboBox.addItem("Claude", 3);
    providerComboBox.addListener(this);
    
    // API配置
    addAndMakeVisible(apiKeyLabel);
    apiKeyLabel.setText("API密钥:", juce::dontSendNotification);
    
    addAndMakeVisible(apiKeyEditor);
    apiKeyEditor.setPasswordCharacter('*');
    
    addAndMakeVisible(baseUrlLabel);
    baseUrlLabel.setText("API地址:", juce::dontSendNotification);
    
    addAndMakeVisible(baseUrlEditor);
    
    addAndMakeVisible(modelLabel);
    modelLabel.setText("模型名称:", juce::dontSendNotification);
    
    addAndMakeVisible(modelEditor);
    
    // 参数配置
    addAndMakeVisible(temperatureLabel);
    temperatureLabel.setText("创造性 (0.0-1.0):", juce::dontSendNotification);
    
    addAndMakeVisible(temperatureSlider);
    temperatureSlider.setRange(0.0, 1.0, 0.1);
    temperatureSlider.setValue(0.7);
    temperatureSlider.setTextBoxStyle(juce::Slider::TextBoxLeft, false, 50, 20);
    
    addAndMakeVisible(maxTokensLabel);
    maxTokensLabel.setText("最大Token数:", juce::dontSendNotification);
    
    addAndMakeVisible(maxTokensSlider);
    maxTokensSlider.setRange(100, 4000, 100);
    maxTokensSlider.setValue(1000);
    maxTokensSlider.setTextBoxStyle(juce::Slider::TextBoxLeft, false, 60, 20);
    
    addAndMakeVisible(timeoutLabel);
    timeoutLabel.setText("超时时间(秒):", juce::dontSendNotification);
    
    addAndMakeVisible(timeoutSlider);
    timeoutSlider.setRange(10, 120, 5);
    timeoutSlider.setValue(30);
    timeoutSlider.setTextBoxStyle(juce::Slider::TextBoxLeft, false, 50, 20);
    
    // 状态显示
    addAndMakeVisible(statusLabel);
    statusLabel.setText("请配置AI提供商信息", juce::dontSendNotification);
    statusLabel.setColour(juce::Label::textColourId, juce::Colours::blue);
    
    // 按钮
    addAndMakeVisible(testButton);
    testButton.setButtonText("测试连接");
    testButton.addListener(this);
    
    addAndMakeVisible(resetButton);
    resetButton.setButtonText("重置");
    resetButton.addListener(this);
    
    addAndMakeVisible(saveButton);
    saveButton.setButtonText("保存");
    saveButton.addListener(this);
    
    addAndMakeVisible(cancelButton);
    cancelButton.setButtonText("取消");
    cancelButton.addListener(this);
}

void AIConfigDialog::updateUIFromConfig()
{
    providerComboBox.setSelectedId((int)currentConfig.provider + 1, juce::dontSendNotification);
    apiKeyEditor.setText(currentConfig.apiKey);
    baseUrlEditor.setText(currentConfig.baseUrl);
    modelEditor.setText(currentConfig.modelName);
    temperatureSlider.setValue(currentConfig.temperature, juce::dontSendNotification);
    maxTokensSlider.setValue(currentConfig.maxTokens, juce::dontSendNotification);
    timeoutSlider.setValue(currentConfig.timeoutSeconds, juce::dontSendNotification);
}

void AIConfigDialog::updateConfigFromUI()
{
    currentConfig.provider = (AIProvider)(providerComboBox.getSelectedId() - 1);
    currentConfig.apiKey = apiKeyEditor.getText();
    currentConfig.baseUrl = baseUrlEditor.getText();
    currentConfig.modelName = modelEditor.getText();
    currentConfig.temperature = (float)temperatureSlider.getValue();
    currentConfig.maxTokens = (int)maxTokensSlider.getValue();
    currentConfig.timeoutSeconds = (int)timeoutSlider.getValue();
}

void AIConfigDialog::resetToDefaults()
{
    auto provider = (AIProvider)(providerComboBox.getSelectedId() - 1);
    
    switch (provider)
    {
        case AIProvider::OpenAI:
            currentConfig = AIConfigManager::getOpenAIConfig("");
            break;
        case AIProvider::Groq:
            currentConfig = AIConfigManager::getGroqConfig("");
            break;
        case AIProvider::Claude:
            currentConfig = AIConfigManager::getClaudeConfig("");
            break;
        default:
            currentConfig = AIConfigManager::getGroqConfig("");
            break;
    }
    
    updateUIFromConfig();
    statusLabel.setText("已重置为默认配置", juce::dontSendNotification);
    statusLabel.setColour(juce::Label::textColourId, juce::Colours::green);
}

void AIConfigDialog::testConnection()
{
    updateConfigFromUI();
    
    if (currentConfig.apiKey.isEmpty())
    {
        statusLabel.setText("请先输入API密钥", juce::dontSendNotification);
        statusLabel.setColour(juce::Label::textColourId, juce::Colours::red);
        return;
    }
    
    statusLabel.setText("正在测试连接...", juce::dontSendNotification);
    statusLabel.setColour(juce::Label::textColourId, juce::Colours::blue);
    
    // 创建临时AI管理器进行测试
    auto testManager = std::make_unique<AIManager>();
    testManager->setConfig(currentConfig);
    
    testManager->chatRequest("Hello", nullptr, [this](const AIResponse& response)
    {
        if (response.success)
        {
            statusLabel.setText("连接测试成功！", juce::dontSendNotification);
            statusLabel.setColour(juce::Label::textColourId, juce::Colours::green);
        }
        else
        {
            statusLabel.setText("连接失败: " + response.errorMessage, juce::dontSendNotification);
            statusLabel.setColour(juce::Label::textColourId, juce::Colours::red);
        }
    });
}

void AIConfigDialog::updateProviderDefaults()
{
    auto provider = (AIProvider)(providerComboBox.getSelectedId() - 1);
    
    switch (provider)
    {
        case AIProvider::OpenAI:
            baseUrlEditor.setText("https://api.openai.com/v1/chat/completions");
            modelEditor.setText("gpt-3.5-turbo");
            break;
        case AIProvider::Groq:
            baseUrlEditor.setText("https://api.groq.com/openai/v1/chat/completions");
            modelEditor.setText("llama3-8b-8192");
            break;
        case AIProvider::Claude:
            baseUrlEditor.setText("https://api.anthropic.com/v1/messages");
            modelEditor.setText("claude-3-sonnet-20240229");
            break;
    }
}

//==============================================================================
// EnhancedChatInterface Implementation
//==============================================================================

EnhancedChatInterface::EnhancedChatInterface()
    : aiManager(nullptr), musicSection(nullptr), isProcessing(false)
{
    setupComponents();
}

EnhancedChatInterface::~EnhancedChatInterface()
{
}

void EnhancedChatInterface::paint(juce::Graphics& g)
{
    g.fillAll(juce::Colours::darkgrey);
}

void EnhancedChatInterface::resized()
{
    auto bounds = getLocalBounds().reduced(5);

    // 快捷按钮区域
    auto quickButtonArea = bounds.removeFromTop(35);
    auto buttonWidth = quickButtonArea.getWidth() / 4 - 3;

    popButton.setBounds(quickButtonArea.removeFromLeft(buttonWidth));
    quickButtonArea.removeFromLeft(4);
    jazzButton.setBounds(quickButtonArea.removeFromLeft(buttonWidth));
    quickButtonArea.removeFromLeft(4);
    classicalButton.setBounds(quickButtonArea.removeFromLeft(buttonWidth));
    quickButtonArea.removeFromLeft(4);
    bluesButton.setBounds(quickButtonArea);

    bounds.removeFromTop(5);

    // 控制按钮区域
    auto controlArea = bounds.removeFromTop(30);
    auto controlButtonWidth = controlArea.getWidth() / 4 - 3;

    generateButton.setBounds(controlArea.removeFromLeft(controlButtonWidth));
    controlArea.removeFromLeft(4);
    analyzeButton.setBounds(controlArea.removeFromLeft(controlButtonWidth));
    controlArea.removeFromLeft(4);
    configButton.setBounds(controlArea.removeFromLeft(controlButtonWidth));
    controlArea.removeFromLeft(4);
    clearButton.setBounds(controlArea);

    bounds.removeFromTop(5);

    // 输入区域
    auto inputArea = bounds.removeFromBottom(80);
    sendButton.setBounds(inputArea.removeFromRight(60));
    inputArea.removeFromRight(5);
    userInput.setBounds(inputArea);

    bounds.removeFromBottom(5);

    // 对话显示区域
    chatDisplay.setBounds(bounds);
}

void EnhancedChatInterface::setAIManager(AIManager* manager)
{
    aiManager = manager;
}

void EnhancedChatInterface::setMusicSection(const MusicSection* section)
{
    musicSection = section;
}

void EnhancedChatInterface::addUserMessage(const juce::String& message)
{
    conversationHistory += "用户: " + message + "\n\n";
    updateChatDisplay();
}

void EnhancedChatInterface::addAIMessage(const AIResponse& response)
{
    conversationHistory += "AI: " + response.explanation + "\n";

    if (!response.suggestedChords.isEmpty())
    {
        conversationHistory += "\n建议的和弦:\n";
        for (const auto& chord : response.suggestedChords)
        {
            conversationHistory += "- " + chord.toString() + "\n";
        }
    }

    if (!response.reasoning.isEmpty())
    {
        conversationHistory += "\n推理过程: " + response.reasoning + "\n";
    }

    conversationHistory += "\n";
    updateChatDisplay();
}

void EnhancedChatInterface::addSystemMessage(const juce::String& message)
{
    conversationHistory += "系统: " + message + "\n\n";
    updateChatDisplay();
}

void EnhancedChatInterface::clearChat()
{
    conversationHistory.clear();
    updateChatDisplay();
}

void EnhancedChatInterface::buttonClicked(juce::Button* button)
{
    if (button == &sendButton)
    {
        sendMessage();
    }
    else if (button == &generateButton)
    {
        generateChords();
    }
    else if (button == &analyzeButton)
    {
        analyzeProgression();
    }
    else if (button == &configButton)
    {
        if (onConfigRequested)
            onConfigRequested();
    }
    else if (button == &clearButton)
    {
        clearChat();
    }
    else if (button == &popButton)
    {
        sendQuickRequest("请为我生成一个流行音乐风格的和弦进行");
    }
    else if (button == &jazzButton)
    {
        sendQuickRequest("请为我生成一个爵士风格的和弦进行");
    }
    else if (button == &classicalButton)
    {
        sendQuickRequest("请为我生成一个古典音乐风格的和弦进行");
    }
    else if (button == &bluesButton)
    {
        sendQuickRequest("请为我生成一个蓝调风格的和弦进行");
    }
}

void EnhancedChatInterface::textEditorReturnKeyPressed(juce::TextEditor& editor)
{
    if (&editor == &userInput)
    {
        sendMessage();
    }
}

void EnhancedChatInterface::setupComponents()
{
    // 对话显示
    addAndMakeVisible(chatDisplay);
    chatDisplay.setMultiLine(true);
    chatDisplay.setReadOnly(true);
    chatDisplay.setScrollbarsShown(true);
    chatDisplay.setFont(juce::Font(14.0f));

    // 用户输入
    addAndMakeVisible(userInput);
    userInput.setMultiLine(true);
    userInput.setReturnKeyStartsNewLine(false);
    userInput.setTextToShowWhenEmpty("输入您的问题或要求...", juce::Colours::grey);
    userInput.addListener(this);

    // 发送按钮
    addAndMakeVisible(sendButton);
    sendButton.setButtonText("发送");
    sendButton.addListener(this);

    // 功能按钮
    addAndMakeVisible(generateButton);
    generateButton.setButtonText("生成和弦");
    generateButton.addListener(this);

    addAndMakeVisible(analyzeButton);
    analyzeButton.setButtonText("分析进行");
    analyzeButton.addListener(this);

    addAndMakeVisible(configButton);
    configButton.setButtonText("AI配置");
    configButton.addListener(this);

    addAndMakeVisible(clearButton);
    clearButton.setButtonText("清空对话");
    clearButton.addListener(this);

    // 快捷按钮
    addAndMakeVisible(popButton);
    popButton.setButtonText("流行");
    popButton.addListener(this);

    addAndMakeVisible(jazzButton);
    jazzButton.setButtonText("爵士");
    jazzButton.addListener(this);

    addAndMakeVisible(classicalButton);
    classicalButton.setButtonText("古典");
    classicalButton.addListener(this);

    addAndMakeVisible(bluesButton);
    bluesButton.setButtonText("蓝调");
    bluesButton.addListener(this);
}

void EnhancedChatInterface::sendMessage()
{
    auto message = userInput.getText();
    if (message.isEmpty() || !aiManager || isProcessing)
        return;

    addUserMessage(message);
    userInput.clear();
    setProcessing(true);

    aiManager->chatRequest(message, musicSection, [this](const AIResponse& response)
    {
        setProcessing(false);
        handleAIResponse(response);
    });
}

void EnhancedChatInterface::generateChords()
{
    if (!aiManager || isProcessing)
        return;

    auto request = "请为当前的音乐段落生成合适的和弦进行";
    addUserMessage(request);
    setProcessing(true);

    if (musicSection)
    {
        aiManager->generateChords(*musicSection, request, [this](const AIResponse& response)
        {
            setProcessing(false);
            handleAIResponse(response);

            if (response.success && !response.suggestedChords.isEmpty() && onChordsGenerated)
            {
                onChordsGenerated(response.suggestedChords);
            }
        });
    }
    else
    {
        setProcessing(false);
        addSystemMessage("请先创建一个音乐段落");
    }
}

void EnhancedChatInterface::analyzeProgression()
{
    if (!aiManager || isProcessing)
        return;

    addUserMessage("请分析当前的和弦进行");
    setProcessing(true);

    if (musicSection && !musicSection->chords.isEmpty())
    {
        aiManager->analyzeProgression(*musicSection, [this](const AIResponse& response)
        {
            setProcessing(false);
            handleAIResponse(response);
        });
    }
    else
    {
        setProcessing(false);
        addSystemMessage("当前没有和弦可以分析");
    }
}

void EnhancedChatInterface::sendQuickRequest(const juce::String& request)
{
    if (!aiManager || isProcessing)
        return;

    addUserMessage(request);
    setProcessing(true);

    if (musicSection)
    {
        aiManager->generateChords(*musicSection, request, [this](const AIResponse& response)
        {
            setProcessing(false);
            handleAIResponse(response);

            if (response.success && !response.suggestedChords.isEmpty() && onChordsGenerated)
            {
                onChordsGenerated(response.suggestedChords);
            }
        });
    }
    else
    {
        setProcessing(false);
        addSystemMessage("请先创建一个音乐段落");
    }
}

void EnhancedChatInterface::handleAIResponse(const AIResponse& response)
{
    if (response.success)
    {
        addAIMessage(response);
    }
    else
    {
        addSystemMessage("AI请求失败: " + response.errorMessage);
    }
}

void EnhancedChatInterface::updateChatDisplay()
{
    chatDisplay.setText(conversationHistory);
    chatDisplay.moveCaretToEnd();
}

void EnhancedChatInterface::setProcessing(bool processing)
{
    isProcessing = processing;
    sendButton.setEnabled(!processing);
    generateButton.setEnabled(!processing);
    analyzeButton.setEnabled(!processing);

    if (processing)
    {
        addSystemMessage("正在处理请求...");
    }
}

//==============================================================================
// AIStatusIndicator Implementation
//==============================================================================

AIStatusIndicator::AIStatusIndicator()
    : aiManager(nullptr), statusColour(juce::Colours::green),
      isProcessing(false), animationPhase(0.0f)
{
    setSize(200, 25);
    startTimerHz(10); // 10 FPS for animation
}

AIStatusIndicator::~AIStatusIndicator()
{
    stopTimer();
}

void AIStatusIndicator::paint(juce::Graphics& g)
{
    auto bounds = getLocalBounds().toFloat();

    // 背景
    g.setColour(juce::Colours::black);
    g.fillRoundedRectangle(bounds, 5.0f);

    // 状态指示器
    auto indicatorBounds = bounds.removeFromLeft(20).reduced(3);

    if (isProcessing)
    {
        // 动画效果
        auto alpha = 0.5f + 0.5f * std::sin(animationPhase);
        g.setColour(juce::Colours::orange.withAlpha(alpha));
    }
    else
    {
        g.setColour(statusColour);
    }

    g.fillEllipse(indicatorBounds);

    // 状态文本
    g.setColour(juce::Colours::white);
    g.setFont(12.0f);
    g.drawText(currentStatus, bounds.reduced(5), juce::Justification::centredLeft);
}

void AIStatusIndicator::resized()
{
    repaint();
}

void AIStatusIndicator::setAIManager(AIManager* manager)
{
    aiManager = manager;
}

void AIStatusIndicator::setStatus(const juce::String& status, juce::Colour colour)
{
    currentStatus = status;
    statusColour = colour;
    isProcessing = false;
    repaint();
}

void AIStatusIndicator::setError(const juce::String& error)
{
    setStatus("错误: " + error, juce::Colours::red);
}

void AIStatusIndicator::setProcessing(bool processing)
{
    isProcessing = processing;
    if (processing)
    {
        currentStatus = "AI处理中...";
    }
    repaint();
}

void AIStatusIndicator::timerCallback()
{
    if (isProcessing)
    {
        animationPhase += 0.2f;
        if (animationPhase > juce::MathConstants<float>::twoPi)
            animationPhase = 0.0f;
        repaint();
    }

    // 检查AI管理器状态
    if (aiManager)
    {
        if (aiManager->isBusy() && !isProcessing)
        {
            setProcessing(true);
        }
        else if (!aiManager->isBusy() && isProcessing)
        {
            setProcessing(false);
            setStatus("就绪", juce::Colours::green);
        }

        auto lastError = aiManager->getLastError();
        if (lastError.isNotEmpty())
        {
            setError(lastError);
        }
    }
}

//==============================================================================
// AISuggestionPanel Implementation
//==============================================================================

AISuggestionPanel::AISuggestionPanel()
{
    setupComponents();
    setSize(300, 400);
}

AISuggestionPanel::~AISuggestionPanel()
{
}

void AISuggestionPanel::paint(juce::Graphics& g)
{
    g.fillAll(juce::Colours::lightgrey);
    g.setColour(juce::Colours::black);
    g.drawRect(getLocalBounds(), 1);
}

void AISuggestionPanel::resized()
{
    auto bounds = getLocalBounds().reduced(10);

    // 标题
    titleLabel.setBounds(bounds.removeFromTop(25));
    bounds.removeFromTop(5);

    // 按钮区域
    auto buttonArea = bounds.removeFromBottom(35);
    auto buttonWidth = buttonArea.getWidth() / 2 - 5;
    acceptAllButton.setBounds(buttonArea.removeFromLeft(buttonWidth));
    buttonArea.removeFromLeft(10);
    clearButton.setBounds(buttonArea);

    bounds.removeFromBottom(10);

    // 和弦按钮区域
    auto chordsArea = bounds.removeFromBottom(150);
    chordsViewport.setBounds(chordsArea);

    bounds.removeFromBottom(10);

    // 解释区域
    explanationEditor.setBounds(bounds);

    layoutChordButtons();
}

void AISuggestionPanel::showSuggestions(const juce::Array<ChordData>& suggestions,
                                       const juce::String& explanation)
{
    currentSuggestions = suggestions;
    explanationEditor.setText(explanation);
    updateChordButtons();
    setVisible(true);
}

void AISuggestionPanel::clearSuggestions()
{
    currentSuggestions.clear();
    explanationEditor.clear();
    chordButtons.clear();
    setVisible(false);
}

void AISuggestionPanel::buttonClicked(juce::Button* button)
{
    if (button == &acceptAllButton)
    {
        if (onAllChordsAccepted)
            onAllChordsAccepted(currentSuggestions);
        clearSuggestions();
    }
    else if (button == &clearButton)
    {
        clearSuggestions();
    }
    else
    {
        // 检查是否是和弦按钮
        for (int i = 0; i < chordButtons.size(); ++i)
        {
            if (chordButtons[i] == button)
            {
                if (onChordSelected && i < currentSuggestions.size())
                {
                    onChordSelected(currentSuggestions.getReference(i));
                }
                break;
            }
        }
    }
}

void AISuggestionPanel::setupComponents()
{
    // 标题
    addAndMakeVisible(titleLabel);
    titleLabel.setText("AI建议", juce::dontSendNotification);
    titleLabel.setFont(juce::Font(16.0f, juce::Font::bold));
    titleLabel.setJustificationType(juce::Justification::centred);

    // 解释区域
    addAndMakeVisible(explanationEditor);
    explanationEditor.setMultiLine(true);
    explanationEditor.setReadOnly(true);
    explanationEditor.setScrollbarsShown(true);

    // 和弦视口
    addAndMakeVisible(chordsViewport);
    chordsViewport.setViewedComponent(&chordsContainer, false);

    // 按钮
    addAndMakeVisible(acceptAllButton);
    acceptAllButton.setButtonText("接受全部");
    acceptAllButton.addListener(this);

    addAndMakeVisible(clearButton);
    clearButton.setButtonText("清空");
    clearButton.addListener(this);
}

void AISuggestionPanel::updateChordButtons()
{
    chordButtons.clear();

    for (const auto& chord : currentSuggestions)
    {
        auto* button = new juce::TextButton(chord.chordName);
        button->addListener(this);
        chordButtons.add(button);
        chordsContainer.addAndMakeVisible(button);
    }

    layoutChordButtons();
}

void AISuggestionPanel::layoutChordButtons()
{
    const int buttonWidth = 80;
    const int buttonHeight = 30;
    const int spacing = 5;
    const int buttonsPerRow = 3;

    int x = 0, y = 0;

    for (int i = 0; i < chordButtons.size(); ++i)
    {
        chordButtons[i]->setBounds(x, y, buttonWidth, buttonHeight);

        x += buttonWidth + spacing;
        if ((i + 1) % buttonsPerRow == 0)
        {
            x = 0;
            y += buttonHeight + spacing;
        }
    }

    // 设置容器大小
    int totalHeight = y + (x > 0 ? buttonHeight : 0);
    chordsContainer.setSize(buttonsPerRow * (buttonWidth + spacing), totalHeight);
}
