# AI和弦助手插件制作计划

## 项目概述
基于JUCE框架开发AI驱动的音乐插件，能够辅助音乐人进行和弦编写，根据用户需求智能生成和弦进行，并提供乐理层面的解释。

## 技术架构
- **插件主体**: C++ + JUCE框架
- **AI后端**: Python (Flask/FastAPI) 或 C++直接调用API
- **音频处理**: JUCE内置音频处理和MIDI功能
- **UI框架**: JUCE GUI组件

## 分阶段开发计划

### 阶段一：核心数据结构与基础UI (第1-2周)

#### 1.1 音乐数据结构设计
- [ ] 创建和弦数据类 (ChordData)
  - 和弦名称 (String)
  - 根音 (int)
  - 和弦类型 (enum)
  - 转位 (int)
  - 时值 (float)
  - 位置信息 (小节、拍)

- [ ] 创建音乐段落管理类 (MusicSection)
  - 小节数组
  - 全局调式/拍号
  - 局部调式/拍号变化
  - BPM管理

#### 1.2 基础UI组件
- [ ] 音乐段落编辑器组件 (MusicSectionEditor)
  - 小节网格显示
  - 和弦放置区域
  - 时值可视化

- [ ] 和弦选择器弹窗 (ChordSelector)
  - 搜索功能
  - 分类选择
  - 常用和弦列表

- [ ] 参数控制面板
  - 调式选择器
  - 拍号选择器
  - 段落长度设置

#### 1.3 基础功能实现
- [ ] 和弦输入/编辑功能
- [ ] 数据序列化/反序列化
- [ ] 基础UI布局和响应

### 阶段二：播放引擎与MIDI功能 (第3-4周)

#### 2.1 内部音源实现
- [ ] 基于JUCE Synthesiser的简单音源
- [ ] 和弦音符生成逻辑
- [ ] 音量控制

#### 2.2 播放控制系统
- [ ] 播放/暂停/停止功能
- [ ] BPM同步机制
  - DAW同步模式
  - 手动BPM模式
- [ ] 播放进度指示

#### 2.3 MIDI导出功能
- [ ] 和弦到MIDI转换
- [ ] 拖拽到DAW功能
- [ ] MIDI文件导出

### 阶段三：AI集成基础架构 (第5-6周)

#### 3.1 AI通信架构选择
**方案A: Python后端** (推荐)
- [ ] 创建Flask/FastAPI后端服务
- [ ] API密钥管理
- [ ] 请求/响应处理

**方案B: C++直接调用**
- [ ] HTTP客户端集成
- [ ] JSON解析库集成

#### 3.2 Chat界面实现
- [ ] 用户输入框
- [ ] AI响应显示区
- [ ] 对话历史管理

#### 3.3 AI请求构建
- [ ] 音乐上下文序列化
- [ ] Prompt模板系统
- [ ] 请求格式标准化

### 阶段四：AI模型集成与Prompt优化 (第7-8周)

#### 4.1 首个AI模型集成
- [ ] 选择免费/低价模型 (Groq Llama 3 或 GPT-3.5-Turbo)
- [ ] API调用实现
- [ ] 错误处理机制

#### 4.2 Prompt工程
- [ ] 基础Prompt模板
- [ ] 上下文信息格式化
- [ ] 输出格式规范
- [ ] Few-shot示例集成

#### 4.3 响应解析
- [ ] JSON响应解析
- [ ] 和弦数据提取
- [ ] 解释文本格式化
- [ ] 错误响应处理

### 阶段五：功能完善与优化 (第9-10周)

#### 5.1 高级功能实现
- [ ] 局部调式/拍号变化支持
- [ ] 复杂时值处理
- [ ] 和弦保留机制

#### 5.2 用户体验优化
- [ ] 界面美化
- [ ] 操作流程优化
- [ ] 快捷键支持
- [ ] 撤销/重做功能

#### 5.3 性能优化
- [ ] 音频处理优化
- [ ] UI响应性优化
- [ ] 内存管理优化

### 阶段六：多模型支持与会员机制 (第11-12周)

#### 6.1 多AI模型支持
- [ ] 模型选择器UI
- [ ] 多API封装
- [ ] 模型切换逻辑

#### 6.2 会员机制 (可选)
- [ ] 用户认证集成
- [ ] 订阅状态管理
- [ ] 权限控制

### 阶段七：测试与发布 (第13-14周)

#### 7.1 全面测试
- [ ] 单元测试
- [ ] 集成测试
- [ ] 用户测试

#### 7.2 打包与分发
- [ ] 多平台编译
- [ ] 安装包制作
- [ ] 文档编写

## 技术要点

### 关键类设计
```cpp
// 和弦数据类
class ChordData {
    String chordName;
    int rootNote;
    ChordType type;
    int inversion;
    float duration;
    int bar, beat;
};

// 音乐段落管理
class MusicSection {
    Array<ChordData> chords;
    String globalKey;
    String timeSignature;
    float bpm;
    // 局部变化管理
};

// AI通信管理
class AIManager {
    void sendRequest(const MusicSection& section, const String& userInput);
    void parseResponse(const String& response);
};
```

### Prompt模板结构
1. 角色设定 (System Prompt)
2. 音乐上下文 (调式、拍号、现有和弦)
3. 用户要求 (Chat输入)
4. 任务指令 (填充规则)
5. 输出格式 (JSON结构)

## 风险控制
- 每个阶段都有可工作的版本
- 核心功能优先，高级功能可选
- AI部分可以先用模拟数据测试
- 保持代码模块化，便于调试和修改

## 成功标准
- 能够正确显示和编辑音乐段落
- 能够播放和弦并导出MIDI
- AI能够根据用户要求生成合理的和弦进行
- 界面友好，操作流畅
- 支持主流DAW的插件格式
