/*
  ==============================================================================

    AIManager.h
    Created: AI Chord Assistant Plugin - Stage 3
    Author:  AI Assistant

    This file contains the AI communication manager for LLM integration.

  ==============================================================================
*/

#pragma once

#include <JuceHeader.h>
#include "MusicData.h"

//==============================================================================
// AI模型提供商枚举
enum class AIProvider
{
    OpenAI,         // OpenAI GPT系列
    Groq,           // Groq Llama系列
    Claude,         // Anthropic <PERSON>
    Local,          // 本地模型
    Custom          // 自定义API
};

//==============================================================================
// AI请求类型
enum class AIRequestType
{
    GenerateChords,     // 生成和弦进行
    AnalyzeProgression, // 分析和弦进行
    SuggestNext,        // 建议下一个和弦
    ExplainTheory,      // 解释乐理
    ImproveProgression, // 改进和弦进行
    Chat                // 自由对话
};

//==============================================================================
// AI响应数据结构
struct AIResponse
{
    bool success;                           // 请求是否成功
    juce::String errorMessage;              // 错误信息
    juce::String explanation;               // AI的解释文本
    juce::Array<ChordData> suggestedChords; // 建议的和弦
    juce::String reasoning;                 // 推理过程
    float confidence;                       // 置信度 (0.0-1.0)
    
    AIResponse() : success(false), confidence(0.0f) {}
};

//==============================================================================
// AI配置结构
struct AIConfig
{
    AIProvider provider;
    juce::String apiKey;
    juce::String baseUrl;
    juce::String modelName;
    float temperature;      // 创造性参数 (0.0-1.0)
    int maxTokens;          // 最大token数
    bool useStreaming;      // 是否使用流式响应
    int timeoutSeconds;     // 超时时间
    
    AIConfig() 
        : provider(AIProvider::Groq), temperature(0.7f), 
          maxTokens(1000), useStreaming(false), timeoutSeconds(30) {}
};

//==============================================================================
// Prompt模板管理器
class PromptTemplateManager
{
public:
    PromptTemplateManager();
    
    // 生成不同类型的Prompt
    juce::String generateSystemPrompt() const;
    juce::String generateChordGenerationPrompt(const MusicSection& section, 
                                              const juce::String& userRequest) const;
    juce::String generateAnalysisPrompt(const MusicSection& section) const;
    juce::String generateSuggestionPrompt(const MusicSection& section, 
                                         int targetBar, float targetBeat) const;
    juce::String generateTheoryPrompt(const juce::String& question) const;
    
    // 上下文序列化
    juce::String serializeMusicContext(const MusicSection& section) const;
    juce::String serializeChordProgression(const juce::Array<ChordData>& chords) const;
    
    // 输出格式规范
    juce::String getOutputFormatInstructions() const;
    
private:
    juce::String systemPromptTemplate;
    juce::String chordGenerationTemplate;
    juce::String analysisTemplate;
    juce::String suggestionTemplate;
    juce::String theoryTemplate;
    
    void initializeTemplates();
    juce::String formatKeySignature(KeySignature key) const;
    juce::String formatTimeSignature(const TimeSignature& timeSig) const;
};

//==============================================================================
// HTTP客户端管理器
class HTTPClientManager
{
public:
    HTTPClientManager();
    ~HTTPClientManager();
    
    // 异步HTTP请求
    void sendRequest(const juce::String& url,
                    const juce::String& headers,
                    const juce::String& postData,
                    std::function<void(bool success, const juce::String& response)> callback);
    
    // 取消所有请求
    void cancelAllRequests();
    
    // 设置超时时间
    void setTimeout(int seconds) { timeoutSeconds = seconds; }
    
private:
    std::unique_ptr<juce::WebInputStream> currentStream;
    int timeoutSeconds;
    
    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR(HTTPClientManager)
};

//==============================================================================
// AI响应解析器
class AIResponseParser
{
public:
    AIResponseParser();
    
    // 解析不同格式的响应
    static AIResponse parseJSONResponse(const juce::String& jsonResponse);
    static AIResponse parseTextResponse(const juce::String& textResponse);
    
    // 提取和弦数据
    static juce::Array<ChordData> extractChordsFromText(const juce::String& text);
    static ChordData parseChordString(const juce::String& chordStr, int bar, float beat);
    
    // 验证响应
    static bool validateResponse(const AIResponse& response);
    
private:
    static juce::String cleanChordName(const juce::String& name);
    static bool isValidChordName(const juce::String& name);
};

//==============================================================================
// 主AI管理器类
class AIManager : public juce::Thread
{
public:
    AIManager();
    ~AIManager() override;
    
    // 配置管理
    void setConfig(const AIConfig& config);
    const AIConfig& getConfig() const { return currentConfig; }
    
    // AI请求方法
    void generateChords(const MusicSection& section, 
                       const juce::String& userRequest,
                       std::function<void(const AIResponse&)> callback);
    
    void analyzeProgression(const MusicSection& section,
                           std::function<void(const AIResponse&)> callback);
    
    void suggestNextChord(const MusicSection& section,
                         int targetBar, float targetBeat,
                         std::function<void(const AIResponse&)> callback);
    
    void explainTheory(const juce::String& question,
                      std::function<void(const AIResponse&)> callback);
    
    void chatRequest(const juce::String& message,
                    const MusicSection* context,
                    std::function<void(const AIResponse&)> callback);
    
    // 状态管理
    bool isBusy() const { return isThreadRunning(); }
    void cancelCurrentRequest();
    
    // 错误处理
    juce::String getLastError() const { return lastError; }
    
    // Thread override
    void run() override;
    
private:
    // 配置和状态
    AIConfig currentConfig;
    PromptTemplateManager promptManager;
    HTTPClientManager httpManager;
    
    // 请求队列
    struct AIRequest
    {
        AIRequestType type;
        juce::String prompt;
        std::function<void(const AIResponse&)> callback;
        juce::Time timestamp;
    };
    
    juce::Array<AIRequest> requestQueue;
    juce::CriticalSection queueLock;
    
    // 状态变量
    juce::String lastError;
    bool shouldStop;
    
    // 内部方法
    void processRequest(const AIRequest& request);
    juce::String buildAPIRequest(const juce::String& prompt) const;
    juce::String getAPIEndpoint() const;
    juce::String getAuthHeaders() const;
    
    // 不同提供商的特定实现
    void sendOpenAIRequest(const juce::String& prompt, std::function<void(const AIResponse&)> callback);
    void sendGroqRequest(const juce::String& prompt, std::function<void(const AIResponse&)> callback);
    void sendClaudeRequest(const juce::String& prompt, std::function<void(const AIResponse&)> callback);
    
    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR(AIManager)
};

//==============================================================================
// AI配置管理器
class AIConfigManager
{
public:
    AIConfigManager();
    
    // 配置文件管理
    void saveConfig(const AIConfig& config);
    AIConfig loadConfig();
    
    // 预设配置
    static AIConfig getOpenAIConfig(const juce::String& apiKey);
    static AIConfig getGroqConfig(const juce::String& apiKey);
    static AIConfig getClaudeConfig(const juce::String& apiKey);
    
    // 验证配置
    static bool validateConfig(const AIConfig& config);
    
private:
    juce::File getConfigFile();
    
    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR(AIConfigManager)
};
