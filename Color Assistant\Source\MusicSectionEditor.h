/*
  ==============================================================================

    MusicSectionEditor.h
    Created: AI Chord Assistant Plugin
    Author:  AI Assistant

    This component provides a visual editor for music sections with chord placement.

  ==============================================================================
*/

#pragma once

#include <JuceHeader.h>
#include "MusicData.h"

//==============================================================================
// 和弦选择器弹窗
class ChordSelectorDialog : public juce::Component,
                           public juce::TextEditor::Listener,
                           public juce::Button::Listener
{
public:
    ChordSelectorDialog();
    ~ChordSelectorDialog() override;
    
    void paint(juce::Graphics& g) override;
    void resized() override;
    
    // 设置回调函数
    std::function<void(const ChordData&)> onChordSelected;
    std::function<void()> onCancelled;
    
    // 显示对话框
    void showForPosition(int bar, float beat, const ChordData* existingChord = nullptr);
    
    // TextEditor::Listener
    void textEditorTextChanged(juce::TextEditor& editor) override;
    void textEditorReturnKeyPressed(juce::TextEditor& editor) override;
    
    // Button::Listener
    void buttonClicked(juce::Button* button) override;
    
private:
    // UI组件
    juce::TextEditor searchBox;
    juce::ListBox chordList;
    juce::TextButton okButton;
    juce::TextButton cancelButton;
    juce::Label titleLabel;
    
    // 数据
    juce::StringArray availableChords;
    juce::StringArray filteredChords;
    int currentBar;
    float currentBeat;
    
    void updateChordList();
    void populateAvailableChords();
    ChordData createChordFromSelection();
    
    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR(ChordSelectorDialog)
};

//==============================================================================
// 小节网格组件
class BarGrid : public juce::Component
{
public:
    BarGrid();
    ~BarGrid() override;
    
    void paint(juce::Graphics& g) override;
    void resized() override;
    void mouseDown(const juce::MouseEvent& event) override;
    void mouseDrag(const juce::MouseEvent& event) override;
    void mouseUp(const juce::MouseEvent& event) override;
    
    // 设置参数
    void setNumberOfBars(int bars);
    void setTimeSignature(const TimeSignature& timeSig);
    void setBeatsPerBar(int beats);
    
    // 和弦管理
    void setChords(const juce::Array<ChordData>& chords);
    void addChord(const ChordData& chord);
    void removeChord(int index);
    void updateChord(int index, const ChordData& newChord);
    
    // 回调函数
    std::function<void(int bar, float beat)> onPositionClicked;
    std::function<void(int chordIndex)> onChordClicked;
    std::function<void(int chordIndex, int bar, float beat, float duration)> onChordMoved;
    
private:
    // 参数
    int numberOfBars;
    TimeSignature timeSignature;
    int beatsPerBar;
    float beatWidth;
    float barHeight;
    
    // 和弦数据
    juce::Array<ChordData> chords;
    
    // 交互状态
    bool isDragging;
    int draggedChordIndex;
    juce::Point<float> dragStartPosition;
    
    // 辅助方法
    juce::Rectangle<float> getChordBounds(const ChordData& chord) const;
    void getPositionFromPoint(juce::Point<float> point, int& bar, float& beat) const;
    juce::Point<float> getPointFromPosition(int bar, float beat) const;
    int findChordAtPosition(juce::Point<float> point) const;
    
    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR(BarGrid)
};

//==============================================================================
// 音乐段落编辑器主组件
class MusicSectionEditor : public juce::Component,
                          public juce::Button::Listener,
                          public juce::ComboBox::Listener,
                          public juce::Slider::Listener
{
public:
    MusicSectionEditor();
    ~MusicSectionEditor() override;
    
    void paint(juce::Graphics& g) override;
    void resized() override;
    
    // 数据访问
    void setMusicSection(MusicSection* section);
    MusicSection* getMusicSection() const { return musicSection; }
    
    // 回调函数
    std::function<void()> onSectionChanged;
    
    // Button::Listener
    void buttonClicked(juce::Button* button) override;
    
    // ComboBox::Listener
    void comboBoxChanged(juce::ComboBox* comboBoxThatHasChanged) override;
    
    // Slider::Listener
    void sliderValueChanged(juce::Slider* slider) override;
    
private:
    // 数据
    MusicSection* musicSection;
    
    // UI组件
    BarGrid barGrid;
    ChordSelectorDialog chordSelector;
    
    // 控制面板
    juce::ComboBox keySelector;
    juce::ComboBox timeSignatureSelector;
    juce::Slider barsSlider;
    juce::Label keyLabel;
    juce::Label timeSignatureLabel;
    juce::Label barsLabel;
    
    // 按钮
    juce::TextButton clearAllButton;
    juce::TextButton clearSelectionButton;
    
    // 布局
    void layoutComponents();
    void updateFromMusicSection();
    void updateMusicSectionFromUI();
    
    // 事件处理
    void handlePositionClicked(int bar, float beat);
    void handleChordClicked(int chordIndex);
    void handleChordSelected(const ChordData& chord);
    void handleChordMoved(int chordIndex, int bar, float beat, float duration);
    
    // 初始化
    void setupControls();
    void populateKeySelector();
    void populateTimeSignatureSelector();
    
    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR(MusicSectionEditor)
};

//==============================================================================
// 播放控制面板
class PlaybackControls : public juce::Component,
                        public juce::Button::Listener,
                        public juce::Slider::Listener
{
public:
    PlaybackControls();
    ~PlaybackControls() override;
    
    void paint(juce::Graphics& g) override;
    void resized() override;
    
    // 播放状态
    void setPlaying(bool isPlaying);
    void setBPM(float bpm);
    void setSyncWithDAW(bool sync);
    
    // 回调函数
    std::function<void()> onPlayClicked;
    std::function<void()> onStopClicked;
    std::function<void(float)> onBPMChanged;
    std::function<void(bool)> onSyncChanged;
    std::function<void(float)> onVolumeChanged;
    
    // Button::Listener
    void buttonClicked(juce::Button* button) override;
    
    // Slider::Listener
    void sliderValueChanged(juce::Slider* slider) override;
    
private:
    // 播放控制
    juce::TextButton playButton;
    juce::TextButton stopButton;
    
    // BPM控制
    juce::Slider bpmSlider;
    juce::Label bpmLabel;
    juce::ToggleButton syncButton;
    
    // 音量控制
    juce::Slider volumeSlider;
    juce::Label volumeLabel;
    
    // 状态
    bool isPlaying;
    bool syncWithDAW;
    
    void updatePlayButton();
    void updateBPMControls();
    
    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR(PlaybackControls)
};
