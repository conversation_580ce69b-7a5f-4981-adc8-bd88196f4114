/*
  ==============================================================================

    AudioEngine.cpp
    Created: AI Chord Assistant Plugin - Stage 2
    Author:  AI Assistant

    Implementation of the audio engine for chord playback and MIDI generation.

  ==============================================================================
*/

#include "AudioEngine.h"

//==============================================================================
// SineWaveVoice Implementation
//==============================================================================

SineWaveVoice::SineWaveVoice()
{
}

bool SineWaveVoice::canPlaySound(juce::SynthesiserSound* sound)
{
    return dynamic_cast<SineWaveSound*>(sound) != nullptr;
}

void SineWaveVoice::startNote(int midiNoteNumber, float velocity, juce::SynthesiserSound* sound, int currentPitchWheelPosition)
{
    currentAngle = 0.0;
    level = velocity * 0.15;
    tailOff = 0.0;

    auto cyclesPerSecond = juce::MidiMessage::getMidiNoteInHertz(midiNoteNumber);
    auto cyclesPerSample = cyclesPerSecond / getSampleRate();
    angleDelta = cyclesPerSample * 2.0 * juce::MathConstants<double>::pi;
}

void SineWaveVoice::stopNote(float velocity, bool allowTailOff)
{
    if (allowTailOff)
    {
        if (tailOff == 0.0)
            tailOff = 1.0;
    }
    else
    {
        clearCurrentNote();
        angleDelta = 0.0;
    }
}

void SineWaveVoice::pitchWheelMoved(int newPitchWheelValue)
{
    // 可以在这里实现弯音功能
}

void SineWaveVoice::controllerMoved(int controllerNumber, int newControllerValue)
{
    // 可以在这里实现控制器功能
}

void SineWaveVoice::renderNextBlock(juce::AudioBuffer<float>& outputBuffer, int startSample, int numSamples)
{
    if (angleDelta != 0.0)
    {
        if (tailOff > 0.0)
        {
            while (--numSamples >= 0)
            {
                auto currentSample = (float)(std::sin(currentAngle) * level * tailOff);

                for (auto i = outputBuffer.getNumChannels(); --i >= 0;)
                    outputBuffer.addSample(i, startSample, currentSample);

                currentAngle += angleDelta;
                ++startSample;

                tailOff *= 0.99;

                if (tailOff <= 0.005)
                {
                    clearCurrentNote();
                    angleDelta = 0.0;
                    break;
                }
            }
        }
        else
        {
            while (--numSamples >= 0)
            {
                auto currentSample = (float)(std::sin(currentAngle) * level);

                for (auto i = outputBuffer.getNumChannels(); --i >= 0;)
                    outputBuffer.addSample(i, startSample, currentSample);

                currentAngle += angleDelta;
                ++startSample;
            }
        }
    }
}

//==============================================================================
// SineWaveSound Implementation
//==============================================================================

SineWaveSound::SineWaveSound()
{
}

bool SineWaveSound::appliesToNote(int midiNoteNumber)
{
    return true; // 适用于所有音符
}

bool SineWaveSound::appliesToChannel(int midiChannel)
{
    return true; // 适用于所有通道
}

//==============================================================================
// PlaybackEngine Implementation
//==============================================================================

PlaybackEngine::PlaybackEngine()
    : playing(false), paused(false), looping(false),
      sampleRate(44100.0), samplesPerBlock(512), currentBPM(120.0f), masterVolume(0.7f),
      playPositionInBeats(0.0), playPositionInSamples(0.0), musicSection(nullptr)
{
    setupSynthesiser();
}

PlaybackEngine::~PlaybackEngine()
{
}

void PlaybackEngine::prepareToPlay(double newSampleRate, int newSamplesPerBlock)
{
    sampleRate = newSampleRate;
    samplesPerBlock = newSamplesPerBlock;

    synthesiser.setCurrentPlaybackSampleRate(sampleRate);

    // 重置播放位置
    playPositionInBeats = 0.0;
    playPositionInSamples = 0.0;
}

void PlaybackEngine::releaseResources()
{
    synthesiser.allNotesOff(1, true);
}

void PlaybackEngine::startPlayback()
{
    if (!paused)
    {
        resetPlayPosition();
        generateMidiEvents();
    }

    playing = true;
    paused = false;
}

void PlaybackEngine::stopPlayback()
{
    playing = false;
    paused = false;
    synthesiser.allNotesOff(1, true);
    resetPlayPosition();
    scheduledMidiEvents.clear();
}

void PlaybackEngine::pausePlayback()
{
    playing = false;
    paused = true;
    synthesiser.allNotesOff(1, true);
}

void PlaybackEngine::setMusicSection(const MusicSection* section)
{
    musicSection = section;
    if (playing)
    {
        generateMidiEvents();
    }
}

void PlaybackEngine::setBPM(float newBPM)
{
    currentBPM = newBPM;
}

void PlaybackEngine::setPlayPosition(double positionInBeats)
{
    playPositionInBeats = positionInBeats;
    playPositionInSamples = beatsToSamples(positionInBeats);

    if (playing)
    {
        generateMidiEvents();
    }
}

void PlaybackEngine::resetPlayPosition()
{
    playPositionInBeats = 0.0;
    playPositionInSamples = 0.0;
}

void PlaybackEngine::processAudio(juce::AudioBuffer<float>& buffer, juce::MidiBuffer& midiMessages)
{
    buffer.clear();

    if (playing && musicSection)
    {
        // 更新播放位置
        updatePlayPosition(buffer.getNumSamples());

        // 处理预定的MIDI事件
        currentMidiBuffer.clear();
        processScheduledEvents(currentMidiBuffer, buffer.getNumSamples());

        // 渲染音频
        synthesiser.renderNextBlock(buffer, currentMidiBuffer, 0, buffer.getNumSamples());

        // 应用主音量
        buffer.applyGain(masterVolume);

        // 检查是否到达结尾
        if (musicSection && playPositionInBeats >= musicSection->numberOfBars * 4.0) // 假设4/4拍
        {
            if (looping)
            {
                resetPlayPosition();
                generateMidiEvents();
            }
            else
            {
                stopPlayback();
            }
        }
    }
}

void PlaybackEngine::setVolume(float volume)
{
    masterVolume = juce::jlimit(0.0f, 1.0f, volume);
}

void PlaybackEngine::updatePlayPosition(int numSamples)
{
    playPositionInSamples += numSamples;
    playPositionInBeats = samplesToBeats(playPositionInSamples);
}

void PlaybackEngine::generateMidiEvents()
{
    scheduledMidiEvents.clear();

    if (!musicSection)
        return;

    scheduleChordEvents();
}

void PlaybackEngine::scheduleChordEvents()
{
    if (!musicSection)
        return;

    for (const auto& chord : musicSection->chords)
    {
        if (chord.isEmpty())
            continue;

        // 计算和弦开始时间（以拍为单位）
        double chordStartBeats = (chord.barNumber - 1) * 4.0 + chord.startBeat; // 假设4/4拍
        double chordEndBeats = chordStartBeats + chord.duration;

        // 只处理当前播放位置之后的和弦
        if (chordStartBeats >= playPositionInBeats)
        {
            // 添加Note On事件
            for (int noteNumber : chord.noteNumbers)
            {
                auto noteOnMessage = juce::MidiMessage::noteOn(1, noteNumber, 0.8f);
                noteOnMessage.setTimeStamp(beatsToSamples(chordStartBeats - playPositionInBeats));
                scheduledMidiEvents.add(noteOnMessage);

                // 添加Note Off事件
                auto noteOffMessage = juce::MidiMessage::noteOff(1, noteNumber);
                noteOffMessage.setTimeStamp(beatsToSamples(chordEndBeats - playPositionInBeats));
                scheduledMidiEvents.add(noteOffMessage);
            }
        }
    }

    // 按时间戳排序
    scheduledMidiEvents.sort([](const juce::MidiMessage& a, const juce::MidiMessage& b)
    {
        return a.getTimeStamp() < b.getTimeStamp();
    });
}

void PlaybackEngine::processScheduledEvents(juce::MidiBuffer& midiBuffer, int numSamples)
{
    for (int i = scheduledMidiEvents.size() - 1; i >= 0; --i)
    {
        auto& message = scheduledMidiEvents.getReference(i);
        auto timestamp = message.getTimeStamp();

        if (timestamp < numSamples)
        {
            midiBuffer.addEvent(message, (int)timestamp);
            scheduledMidiEvents.remove(i);
        }
        else
        {
            // 调整时间戳
            message.setTimeStamp(timestamp - numSamples);
        }
    }
}

double PlaybackEngine::beatsToSamples(double beats) const
{
    double beatsPerSecond = currentBPM / 60.0;
    double secondsPerBeat = 1.0 / beatsPerSecond;
    return beats * secondsPerBeat * sampleRate;
}

double PlaybackEngine::samplesToBeats(double samples) const
{
    double beatsPerSecond = currentBPM / 60.0;
    double seconds = samples / sampleRate;
    return seconds * beatsPerSecond;
}

void PlaybackEngine::setupSynthesiser()
{
    // 添加16个声音
    for (int i = 0; i < 16; ++i)
    {
        synthesiser.addVoice(new SineWaveVoice());
    }

    // 添加声音
    synthesiser.addSound(new SineWaveSound());
}

//==============================================================================
// MidiExporter Implementation
//==============================================================================

MidiExporter::MidiExporter()
{
}

MidiExporter::~MidiExporter()
{
}

juce::MidiMessageSequence MidiExporter::generateMidiSequence(const MusicSection& section)
{
    juce::MidiMessageSequence sequence;

    for (const auto& chord : section.chords)
    {
        if (chord.isEmpty())
            continue;

        addChordToSequence(sequence, chord, 0.0);
    }

    return sequence;
}

bool MidiExporter::exportToMidiFile(const MusicSection& section, const juce::File& outputFile)
{
    auto sequence = generateMidiSequence(section);

    juce::MidiFile midiFile;
    midiFile.setTicksPerQuarterNote(480); // 标准分辨率

    // 设置速度
    auto tempoEvent = juce::MidiMessage::tempoMetaEvent(60000000 / (int)section.bpm);
    sequence.addEvent(tempoEvent, 0.0);

    // 设置拍号
    auto timeSigEvent = juce::MidiMessage::timeSignatureMetaEvent(
        section.globalTimeSignature.numerator,
        section.globalTimeSignature.denominator);
    sequence.addEvent(timeSigEvent, 0.0);

    midiFile.addTrack(sequence);

    juce::FileOutputStream outputStream(outputFile);
    if (outputStream.openedOk())
    {
        midiFile.writeTo(outputStream);
        return true;
    }

    return false;
}

juce::MemoryBlock MidiExporter::generateDragData(const MusicSection& section)
{
    juce::MemoryBlock data;

    auto sequence = generateMidiSequence(section);
    juce::MidiFile midiFile;
    midiFile.addTrack(sequence);

    juce::MemoryOutputStream stream(data, false);
    midiFile.writeTo(stream);

    return data;
}

juce::MidiBuffer MidiExporter::generateRealtimeMidi(const MusicSection& section,
                                                   double startTime,
                                                   double endTime,
                                                   double sampleRate)
{
    juce::MidiBuffer buffer;

    for (const auto& chord : section.chords)
    {
        if (chord.isEmpty())
            continue;

        double chordStartTime = calculateChordStartTime(chord, section);
        double chordEndTime = calculateChordEndTime(chord, section);

        // 检查和弦是否在时间范围内
        if (chordEndTime >= startTime && chordStartTime <= endTime)
        {
            // 计算相对于缓冲区开始的时间
            double relativeStartTime = juce::jmax(0.0, chordStartTime - startTime);
            double relativeEndTime = juce::jmin(endTime - startTime, chordEndTime - startTime);

            int startSample = (int)(relativeStartTime * sampleRate);
            int endSample = (int)(relativeEndTime * sampleRate);

            // 添加Note On事件
            for (int noteNumber : chord.noteNumbers)
            {
                if (chordStartTime >= startTime)
                {
                    buffer.addEvent(juce::MidiMessage::noteOn(1, noteNumber, 0.8f), startSample);
                }

                if (chordEndTime <= endTime)
                {
                    buffer.addEvent(juce::MidiMessage::noteOff(1, noteNumber), endSample);
                }
            }
        }
    }

    return buffer;
}

void MidiExporter::addChordToSequence(juce::MidiMessageSequence& sequence,
                                     const ChordData& chord,
                                     double timeOffset,
                                     float velocity)
{
    double startTime = calculateChordStartTime(chord, *static_cast<const MusicSection*>(nullptr)) + timeOffset;
    double endTime = calculateChordEndTime(chord, *static_cast<const MusicSection*>(nullptr)) + timeOffset;

    for (int noteNumber : chord.noteNumbers)
    {
        sequence.addEvent(juce::MidiMessage::noteOn(1, noteNumber, velocity), startTime);
        sequence.addEvent(juce::MidiMessage::noteOff(1, noteNumber), endTime);
    }
}

double MidiExporter::calculateChordStartTime(const ChordData& chord, const MusicSection& section)
{
    // 计算以秒为单位的开始时间
    double beatsFromStart = (chord.barNumber - 1) * 4.0 + chord.startBeat; // 假设4/4拍
    double beatsPerSecond = section.bpm / 60.0;
    return beatsFromStart / beatsPerSecond;
}

double MidiExporter::calculateChordEndTime(const ChordData& chord, const MusicSection& section)
{
    double startTime = calculateChordStartTime(chord, section);
    double beatsPerSecond = section.bpm / 60.0;
    return startTime + (chord.duration / beatsPerSecond);
}

//==============================================================================
// MidiDragComponent Implementation
//==============================================================================

MidiDragComponent::MidiDragComponent()
    : musicSection(nullptr), isDragging(false)
{
    setSize(100, 30);
}

MidiDragComponent::~MidiDragComponent()
{
}

void MidiDragComponent::paint(juce::Graphics& g)
{
    g.fillAll(juce::Colours::lightblue);
    g.setColour(juce::Colours::black);
    g.drawRect(getLocalBounds(), 1);

    g.setFont(12.0f);
    g.drawText("拖拽MIDI", getLocalBounds(), juce::Justification::centred);

    if (isDragging)
    {
        g.setColour(juce::Colours::blue);
        g.drawRect(getLocalBounds(), 2);
    }
}

void MidiDragComponent::mouseDown(const juce::MouseEvent& event)
{
    if (musicSection && !musicSection->chords.isEmpty())
    {
        isDragging = true;
        repaint();

        if (onDragStarted)
            onDragStarted();
    }
}

void MidiDragComponent::mouseDrag(const juce::MouseEvent& event)
{
    if (isDragging && musicSection)
    {
        // 生成MIDI数据
        auto midiData = MidiExporter::generateDragData(*musicSection);

        // 创建拖拽描述
        juce::DragAndDropContainer::DragImageComponent dragImage;

        // 开始拖拽操作
        startDragging("MIDI Data", this, juce::ScaledImage(), true);
    }
}

void MidiDragComponent::setMusicSection(const MusicSection* section)
{
    musicSection = section;
    repaint();
}

//==============================================================================
// PlaybackProgressIndicator Implementation
//==============================================================================

PlaybackProgressIndicator::PlaybackProgressIndicator()
    : playbackEngine(nullptr), musicSection(nullptr), currentPosition(0.0), totalLength(0.0)
{
    setSize(400, 20);
    startTimerHz(30); // 30 FPS更新
}

PlaybackProgressIndicator::~PlaybackProgressIndicator()
{
    stopTimer();
}

void PlaybackProgressIndicator::paint(juce::Graphics& g)
{
    auto bounds = getLocalBounds().toFloat();

    // 背景
    g.setColour(juce::Colours::darkgrey);
    g.fillRect(bounds);

    // 边框
    g.setColour(juce::Colours::black);
    g.drawRect(bounds, 1.0f);

    // 进度条
    if (totalLength > 0.0)
    {
        float progress = (float)(currentPosition / totalLength);
        progress = juce::jlimit(0.0f, 1.0f, progress);

        auto progressBounds = bounds.reduced(2.0f);
        progressBounds.setWidth(progressBounds.getWidth() * progress);

        g.setColour(juce::Colours::lightblue);
        g.fillRect(progressBounds);
    }

    // 小节标记
    if (musicSection && totalLength > 0.0)
    {
        g.setColour(juce::Colours::white);

        for (int bar = 1; bar <= musicSection->numberOfBars; ++bar)
        {
            double barPosition = (bar - 1) * 4.0; // 假设4/4拍
            float x = (float)(barPosition / totalLength) * bounds.getWidth();

            g.drawVerticalLine(x, bounds.getY() + 2, bounds.getBottom() - 2);

            // 小节号
            if (bar % 2 == 1) // 只显示奇数小节号
            {
                g.setFont(10.0f);
                g.drawText(juce::String(bar), x + 2, bounds.getY(), 20, bounds.getHeight(),
                          juce::Justification::centredLeft);
            }
        }
    }

    // 播放位置指示器
    if (totalLength > 0.0)
    {
        float progress = (float)(currentPosition / totalLength);
        float x = progress * bounds.getWidth();

        g.setColour(juce::Colours::red);
        g.drawVerticalLine(x, bounds.getY(), bounds.getBottom(), 2.0f);
    }
}

void PlaybackProgressIndicator::resized()
{
    // 组件大小改变时重绘
    repaint();
}

void PlaybackProgressIndicator::mouseDown(const juce::MouseEvent& event)
{
    if (totalLength > 0.0)
    {
        double newPosition = getPositionFromMouse(event);

        if (playbackEngine)
        {
            playbackEngine->setPlayPosition(newPosition);
        }

        if (onPositionChanged)
        {
            onPositionChanged(newPosition);
        }

        updatePosition();
        repaint();
    }
}

void PlaybackProgressIndicator::setPlaybackEngine(PlaybackEngine* engine)
{
    playbackEngine = engine;
    updatePosition();
}

void PlaybackProgressIndicator::setMusicSection(const MusicSection* section)
{
    musicSection = section;

    if (section)
    {
        totalLength = section->numberOfBars * 4.0; // 假设4/4拍
    }
    else
    {
        totalLength = 0.0;
    }

    updatePosition();
    repaint();
}

void PlaybackProgressIndicator::timerCallback()
{
    updatePosition();
}

void PlaybackProgressIndicator::updatePosition()
{
    if (playbackEngine)
    {
        double newPosition = playbackEngine->getPlayPosition();

        if (std::abs(newPosition - currentPosition) > 0.01) // 避免不必要的重绘
        {
            currentPosition = newPosition;
            repaint();
        }
    }
}

double PlaybackProgressIndicator::getPositionFromMouse(const juce::MouseEvent& event)
{
    if (totalLength <= 0.0)
        return 0.0;

    float progress = (float)event.x / getWidth();
    progress = juce::jlimit(0.0f, 1.0f, progress);

    return progress * totalLength;
}
