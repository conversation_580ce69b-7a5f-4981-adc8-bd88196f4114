/*
  ==============================================================================

    AudioEngine.h
    Created: AI Chord Assistant Plugin - Stage 2
    Author:  AI Assistant

    This file contains the audio engine for chord playback and MIDI generation.

  ==============================================================================
*/

#pragma once

#include <JuceHeader.h>
#include "MusicData.h"

//==============================================================================
// 简单的正弦波音源
class SineWaveVoice : public juce::SynthesiserVoice
{
public:
    SineWaveVoice();
    
    bool canPlaySound(juce::SynthesiserSound* sound) override;
    void startNote(int midiNoteNumber, float velocity, juce::SynthesiserSound* sound, int currentPitchWheelPosition) override;
    void stopNote(float velocity, bool allowTailOff) override;
    void pitchWheelMoved(int newPitchWheelValue) override;
    void controllerMoved(int controllerNumber, int newControllerValue) override;
    void renderNextBlock(juce::AudioBuffer<float>& outputBuffer, int startSample, int numSamples) override;
    
private:
    double currentAngle = 0.0;
    double angleDelta = 0.0;
    double level = 0.0;
    double tailOff = 0.0;
    
    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR(SineWaveVoice)
};

//==============================================================================
// 简单的音源声音
class SineWaveSound : public juce::SynthesiserSound
{
public:
    SineWaveSound();
    
    bool appliesToNote(int midiNoteNumber) override;
    bool appliesToChannel(int midiChannel) override;
    
private:
    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR(SineWaveSound)
};

//==============================================================================
// 播放引擎类
class PlaybackEngine
{
public:
    PlaybackEngine();
    ~PlaybackEngine();
    
    // 初始化和设置
    void prepareToPlay(double sampleRate, int samplesPerBlock);
    void releaseResources();
    
    // 播放控制
    void startPlayback();
    void stopPlayback();
    void pausePlayback();
    bool isPlaying() const { return playing; }
    bool isPaused() const { return paused; }
    
    // 音乐数据设置
    void setMusicSection(const MusicSection* section);
    void setBPM(float newBPM);
    float getBPM() const { return currentBPM; }
    
    // 播放位置
    void setPlayPosition(double positionInBeats);
    double getPlayPosition() const { return playPositionInBeats; }
    void resetPlayPosition();
    
    // 音频处理
    void processAudio(juce::AudioBuffer<float>& buffer, juce::MidiBuffer& midiMessages);
    
    // 音量控制
    void setVolume(float volume);
    float getVolume() const { return masterVolume; }
    
    // 循环播放
    void setLooping(bool shouldLoop) { looping = shouldLoop; }
    bool isLooping() const { return looping; }
    
private:
    // 播放状态
    bool playing;
    bool paused;
    bool looping;
    
    // 音频参数
    double sampleRate;
    int samplesPerBlock;
    float currentBPM;
    float masterVolume;
    
    // 播放位置跟踪
    double playPositionInBeats;
    double playPositionInSamples;
    
    // 音乐数据
    const MusicSection* musicSection;
    
    // 音频合成器
    juce::Synthesiser synthesiser;
    
    // MIDI事件管理
    juce::MidiBuffer currentMidiBuffer;
    juce::Array<juce::MidiMessage> scheduledMidiEvents;
    
    // 内部方法
    void updatePlayPosition(int numSamples);
    void generateMidiEvents();
    void scheduleChordEvents();
    void processScheduledEvents(juce::MidiBuffer& midiBuffer, int numSamples);
    double beatsToSamples(double beats) const;
    double samplesToBeats(double samples) const;
    void setupSynthesiser();
    
    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR(PlaybackEngine)
};

//==============================================================================
// MIDI导出工具类
class MidiExporter
{
public:
    MidiExporter();
    ~MidiExporter();
    
    // MIDI序列生成
    static juce::MidiMessageSequence generateMidiSequence(const MusicSection& section);
    
    // 文件导出
    static bool exportToMidiFile(const MusicSection& section, const juce::File& outputFile);
    
    // 拖拽数据生成
    static juce::MemoryBlock generateDragData(const MusicSection& section);
    
    // 实时MIDI生成（用于拖拽到DAW）
    static juce::MidiBuffer generateRealtimeMidi(const MusicSection& section, 
                                                double startTime, 
                                                double endTime, 
                                                double sampleRate);
    
private:
    // 辅助方法
    static void addChordToSequence(juce::MidiMessageSequence& sequence, 
                                  const ChordData& chord, 
                                  double timeOffset,
                                  float velocity = 0.8f);
    
    static double calculateChordStartTime(const ChordData& chord, 
                                        const MusicSection& section);
    
    static double calculateChordEndTime(const ChordData& chord, 
                                      const MusicSection& section);
};

//==============================================================================
// 拖拽组件 - 用于将MIDI拖拽到DAW
class MidiDragComponent : public juce::Component,
                         public juce::DragAndDropContainer
{
public:
    MidiDragComponent();
    ~MidiDragComponent() override;
    
    void paint(juce::Graphics& g) override;
    void mouseDown(const juce::MouseEvent& event) override;
    void mouseDrag(const juce::MouseEvent& event) override;
    
    // 设置要导出的音乐数据
    void setMusicSection(const MusicSection* section);
    
    // 回调函数
    std::function<void()> onDragStarted;
    std::function<void()> onDragEnded;
    
private:
    const MusicSection* musicSection;
    bool isDragging;
    
    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR(MidiDragComponent)
};

//==============================================================================
// 播放进度指示器
class PlaybackProgressIndicator : public juce::Component,
                                 public juce::Timer
{
public:
    PlaybackProgressIndicator();
    ~PlaybackProgressIndicator() override;
    
    void paint(juce::Graphics& g) override;
    void resized() override;
    void mouseDown(const juce::MouseEvent& event) override;
    
    // 设置播放引擎引用
    void setPlaybackEngine(PlaybackEngine* engine);
    
    // 设置音乐段落信息
    void setMusicSection(const MusicSection* section);
    
    // Timer回调
    void timerCallback() override;
    
    // 回调函数
    std::function<void(double)> onPositionChanged;
    
private:
    PlaybackEngine* playbackEngine;
    const MusicSection* musicSection;
    
    double currentPosition; // 当前播放位置（拍）
    double totalLength;     // 总长度（拍）
    
    void updatePosition();
    double getPositionFromMouse(const juce::MouseEvent& event);
    
    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR(PlaybackProgressIndicator)
};
