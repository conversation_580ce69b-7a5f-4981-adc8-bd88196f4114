<?xml version="1.0" encoding="UTF-8"?>

<Project DefaultTargets="Build"
         ToolsVersion="17.0"
         xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{005CCEDC-4B8A-948A-BB7B-DF26B9F46CF6}</ProjectGuid>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props"/>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'"
                 Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseOfMfc>false</UseOfMfc>
    <WholeProgramOptimization>false</WholeProgramOptimization>
    <PlatformToolset>v143</PlatformToolset>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'"
                 Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseOfMfc>false</UseOfMfc>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <PlatformToolset>v143</PlatformToolset>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props"/>
  <ImportGroup Label="ExtensionSettings"/>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props"
            Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')"
            Label="LocalAppDataPlatform"/>
  </ImportGroup>
  <PropertyGroup>
    <_ProjectFileVersion>10.0.30319.1</_ProjectFileVersion>
    <TargetExt>.lib</TargetExt>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(SolutionDir)$(Platform)\$(Configuration)\Shared Code\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(Platform)\$(Configuration)\Shared Code\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Color Assistant</TargetName>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</GenerateManifest>
    <PreBuildEventUseInBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</PreBuildEventUseInBuild>
    <PostBuildEventUseInBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</PostBuildEventUseInBuild>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(SolutionDir)$(Platform)\$(Configuration)\Shared Code\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(Platform)\$(Configuration)\Shared Code\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Color Assistant</TargetName>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</GenerateManifest>
    <PreBuildEventUseInBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</PreBuildEventUseInBuild>
    <PostBuildEventUseInBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</PostBuildEventUseInBuild>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Midl>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MkTypLibCompatible>true</MkTypLibCompatible>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TargetEnvironment>Win32</TargetEnvironment>
      <HeaderFileName/>
    </Midl>
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <DebugInformationFormat>OldStyle</DebugInformationFormat>
      <AdditionalIncludeDirectories>D:\JUCE\modules\juce_audio_processors\format_types\VST3_SDK;..\..\JuceLibraryCode;D:\JUCE\modules;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_CRT_SECURE_NO_WARNINGS;WIN32;_WINDOWS;DEBUG;_DEBUG;JUCE_PROJUCER_VERSION=0x80007;JUCE_MODULE_AVAILABLE_juce_analytics=1;JUCE_MODULE_AVAILABLE_juce_animation=1;JUCE_MODULE_AVAILABLE_juce_audio_basics=1;JUCE_MODULE_AVAILABLE_juce_audio_devices=1;JUCE_MODULE_AVAILABLE_juce_audio_formats=1;JUCE_MODULE_AVAILABLE_juce_audio_plugin_client=1;JUCE_MODULE_AVAILABLE_juce_audio_processors=1;JUCE_MODULE_AVAILABLE_juce_audio_utils=1;JUCE_MODULE_AVAILABLE_juce_core=1;JUCE_MODULE_AVAILABLE_juce_data_structures=1;JUCE_MODULE_AVAILABLE_juce_dsp=1;JUCE_MODULE_AVAILABLE_juce_events=1;JUCE_MODULE_AVAILABLE_juce_graphics=1;JUCE_MODULE_AVAILABLE_juce_gui_basics=1;JUCE_MODULE_AVAILABLE_juce_gui_extra=1;JUCE_MODULE_AVAILABLE_juce_midi_ci=1;JUCE_MODULE_AVAILABLE_juce_osc=1;JUCE_GLOBAL_MODULE_SETTINGS_INCLUDED=1;JUCE_VST3_CAN_REPLACE_VST2=0;JUCE_STRICT_REFCOUNTEDPOINTER=1;JucePlugin_Build_VST=0;JucePlugin_Build_VST3=1;JucePlugin_Build_AU=0;JucePlugin_Build_AUv3=0;JucePlugin_Build_AAX=0;JucePlugin_Build_Standalone=1;JucePlugin_Build_Unity=0;JucePlugin_Build_LV2=0;JucePlugin_Enable_IAA=0;JucePlugin_Enable_ARA=0;JucePlugin_Name=&quot;Color Assistant&quot;;JucePlugin_Desc=&quot;Color Assistant&quot;;JucePlugin_Manufacturer=&quot;EGURT&quot;;JucePlugin_ManufacturerWebsite=&quot;www.yourcompany.com&quot;;JucePlugin_ManufacturerEmail=&quot;&quot;;JucePlugin_ManufacturerCode=0x4d616e75;JucePlugin_PluginCode=0x4b74617a;JucePlugin_IsSynth=0;JucePlugin_WantsMidiInput=0;JucePlugin_ProducesMidiOutput=0;JucePlugin_IsMidiEffect=0;JucePlugin_EditorRequiresKeyboardFocus=0;JucePlugin_Version=1.0.0;JucePlugin_VersionCode=0x10000;JucePlugin_VersionString=&quot;1.0.0&quot;;JucePlugin_VSTUniqueID=JucePlugin_PluginCode;JucePlugin_VSTCategory=kPlugCategEffect;JucePlugin_Vst3Category=&quot;Fx&quot;;JucePlugin_AUMainType='aufx';JucePlugin_AUSubType=JucePlugin_PluginCode;JucePlugin_AUExportPrefix=ColorAssistantAU;JucePlugin_AUExportPrefixQuoted=&quot;ColorAssistantAU&quot;;JucePlugin_AUManufacturerCode=JucePlugin_ManufacturerCode;JucePlugin_CFBundleIdentifier=com.yourcompany.ColorAssistant;JucePlugin_AAXIdentifier=com.yourcompany.ColorAssistant;JucePlugin_AAXManufacturerCode=JucePlugin_ManufacturerCode;JucePlugin_AAXProductId=JucePlugin_PluginCode;JucePlugin_AAXCategory=0;JucePlugin_AAXDisableBypass=0;JucePlugin_AAXDisableMultiMono=0;JucePlugin_IAAType=0x61757278;JucePlugin_IAASubType=JucePlugin_PluginCode;JucePlugin_IAAName=&quot;EGURT: Color Assistant&quot;;JucePlugin_VSTNumMidiInputs=16;JucePlugin_VSTNumMidiOutputs=16;JucePlugin_ARAContentTypes=0;JucePlugin_ARATransformationFlags=0;JucePlugin_ARAFactoryID=&quot;com.yourcompany.ColorAssistant.factory&quot;;JucePlugin_ARADocumentArchiveID=&quot;com.yourcompany.ColorAssistant.aradocumentarchive.1.0.0&quot;;JucePlugin_ARACompatibleArchiveIDs=&quot;&quot;;JUCE_STANDALONE_APPLICATION=JucePlugin_Build_Standalone;JUCER_VS2022_78A503E=1;JUCE_APP_VERSION=1.0.0;JUCE_APP_VERSION_HEX=0x10000;JUCE_SHARED_CODE=1;_LIB;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <AssemblerListingLocation>$(IntDir)\</AssemblerListingLocation>
      <ObjectFileName>$(IntDir)\</ObjectFileName>
      <ProgramDataBaseFileName>$(IntDir)\Color Assistant.pdb</ProgramDataBaseFileName>
      <WarningLevel>Level4</WarningLevel>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <LanguageStandard>stdcpp17</LanguageStandard>
    </ClCompile>
    <ResourceCompile>
      <AdditionalIncludeDirectories>D:\JUCE\modules\juce_audio_processors\format_types\VST3_SDK;..\..\JuceLibraryCode;D:\JUCE\modules;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_CRT_SECURE_NO_WARNINGS;WIN32;_WINDOWS;DEBUG;_DEBUG;JUCE_PROJUCER_VERSION=0x80007;JUCE_MODULE_AVAILABLE_juce_analytics=1;JUCE_MODULE_AVAILABLE_juce_animation=1;JUCE_MODULE_AVAILABLE_juce_audio_basics=1;JUCE_MODULE_AVAILABLE_juce_audio_devices=1;JUCE_MODULE_AVAILABLE_juce_audio_formats=1;JUCE_MODULE_AVAILABLE_juce_audio_plugin_client=1;JUCE_MODULE_AVAILABLE_juce_audio_processors=1;JUCE_MODULE_AVAILABLE_juce_audio_utils=1;JUCE_MODULE_AVAILABLE_juce_core=1;JUCE_MODULE_AVAILABLE_juce_data_structures=1;JUCE_MODULE_AVAILABLE_juce_dsp=1;JUCE_MODULE_AVAILABLE_juce_events=1;JUCE_MODULE_AVAILABLE_juce_graphics=1;JUCE_MODULE_AVAILABLE_juce_gui_basics=1;JUCE_MODULE_AVAILABLE_juce_gui_extra=1;JUCE_MODULE_AVAILABLE_juce_midi_ci=1;JUCE_MODULE_AVAILABLE_juce_osc=1;JUCE_GLOBAL_MODULE_SETTINGS_INCLUDED=1;JUCE_VST3_CAN_REPLACE_VST2=0;JUCE_STRICT_REFCOUNTEDPOINTER=1;JucePlugin_Build_VST=0;JucePlugin_Build_VST3=1;JucePlugin_Build_AU=0;JucePlugin_Build_AUv3=0;JucePlugin_Build_AAX=0;JucePlugin_Build_Standalone=1;JucePlugin_Build_Unity=0;JucePlugin_Build_LV2=0;JucePlugin_Enable_IAA=0;JucePlugin_Enable_ARA=0;JucePlugin_Name=\&quot;Color Assistant\&quot;;JucePlugin_Desc=\&quot;Color Assistant\&quot;;JucePlugin_Manufacturer=\&quot;EGURT\&quot;;JucePlugin_ManufacturerWebsite=\&quot;www.yourcompany.com\&quot;;JucePlugin_ManufacturerEmail=\&quot;\&quot;;JucePlugin_ManufacturerCode=0x4d616e75;JucePlugin_PluginCode=0x4b74617a;JucePlugin_IsSynth=0;JucePlugin_WantsMidiInput=0;JucePlugin_ProducesMidiOutput=0;JucePlugin_IsMidiEffect=0;JucePlugin_EditorRequiresKeyboardFocus=0;JucePlugin_Version=1.0.0;JucePlugin_VersionCode=0x10000;JucePlugin_VersionString=\&quot;1.0.0\&quot;;JucePlugin_VSTUniqueID=JucePlugin_PluginCode;JucePlugin_VSTCategory=kPlugCategEffect;JucePlugin_Vst3Category=\&quot;Fx\&quot;;JucePlugin_AUMainType='aufx';JucePlugin_AUSubType=JucePlugin_PluginCode;JucePlugin_AUExportPrefix=ColorAssistantAU;JucePlugin_AUExportPrefixQuoted=\&quot;ColorAssistantAU\&quot;;JucePlugin_AUManufacturerCode=JucePlugin_ManufacturerCode;JucePlugin_CFBundleIdentifier=com.yourcompany.ColorAssistant;JucePlugin_AAXIdentifier=com.yourcompany.ColorAssistant;JucePlugin_AAXManufacturerCode=JucePlugin_ManufacturerCode;JucePlugin_AAXProductId=JucePlugin_PluginCode;JucePlugin_AAXCategory=0;JucePlugin_AAXDisableBypass=0;JucePlugin_AAXDisableMultiMono=0;JucePlugin_IAAType=0x61757278;JucePlugin_IAASubType=JucePlugin_PluginCode;JucePlugin_IAAName=\&quot;EGURT: Color Assistant\&quot;;JucePlugin_VSTNumMidiInputs=16;JucePlugin_VSTNumMidiOutputs=16;JucePlugin_ARAContentTypes=0;JucePlugin_ARATransformationFlags=0;JucePlugin_ARAFactoryID=\&quot;com.yourcompany.ColorAssistant.factory\&quot;;JucePlugin_ARADocumentArchiveID=\&quot;com.yourcompany.ColorAssistant.aradocumentarchive.1.0.0\&quot;;JucePlugin_ARACompatibleArchiveIDs=\&quot;\&quot;;JUCE_STANDALONE_APPLICATION=JucePlugin_Build_Standalone;JUCER_VS2022_78A503E=1;JUCE_APP_VERSION=1.0.0;JUCE_APP_VERSION_HEX=0x10000;JUCE_SHARED_CODE=1;_LIB;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ResourceCompile>
    <Link>
      <OutputFile>$(OutDir)\Color Assistant.lib</OutputFile>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <IgnoreSpecificDefaultLibraries>libcmt.lib; msvcrt.lib;;%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <ProgramDatabaseFile>$(IntDir)\Color Assistant.pdb</ProgramDatabaseFile>
      <SubSystem>Windows</SubSystem>
      <LargeAddressAware>true</LargeAddressAware>
    </Link>
    <Bscmake>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <OutputFile>$(IntDir)\Color Assistant.bsc</OutputFile>
    </Bscmake>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Midl>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MkTypLibCompatible>true</MkTypLibCompatible>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TargetEnvironment>Win32</TargetEnvironment>
      <HeaderFileName/>
    </Midl>
    <ClCompile>
      <Optimization>Full</Optimization>
      <DebugInformationFormat>OldStyle</DebugInformationFormat>
      <AdditionalIncludeDirectories>D:\JUCE\modules\juce_audio_processors\format_types\VST3_SDK;..\..\JuceLibraryCode;D:\JUCE\modules;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_CRT_SECURE_NO_WARNINGS;WIN32;_WINDOWS;NDEBUG;JUCE_PROJUCER_VERSION=0x80007;JUCE_MODULE_AVAILABLE_juce_analytics=1;JUCE_MODULE_AVAILABLE_juce_animation=1;JUCE_MODULE_AVAILABLE_juce_audio_basics=1;JUCE_MODULE_AVAILABLE_juce_audio_devices=1;JUCE_MODULE_AVAILABLE_juce_audio_formats=1;JUCE_MODULE_AVAILABLE_juce_audio_plugin_client=1;JUCE_MODULE_AVAILABLE_juce_audio_processors=1;JUCE_MODULE_AVAILABLE_juce_audio_utils=1;JUCE_MODULE_AVAILABLE_juce_core=1;JUCE_MODULE_AVAILABLE_juce_data_structures=1;JUCE_MODULE_AVAILABLE_juce_dsp=1;JUCE_MODULE_AVAILABLE_juce_events=1;JUCE_MODULE_AVAILABLE_juce_graphics=1;JUCE_MODULE_AVAILABLE_juce_gui_basics=1;JUCE_MODULE_AVAILABLE_juce_gui_extra=1;JUCE_MODULE_AVAILABLE_juce_midi_ci=1;JUCE_MODULE_AVAILABLE_juce_osc=1;JUCE_GLOBAL_MODULE_SETTINGS_INCLUDED=1;JUCE_VST3_CAN_REPLACE_VST2=0;JUCE_STRICT_REFCOUNTEDPOINTER=1;JucePlugin_Build_VST=0;JucePlugin_Build_VST3=1;JucePlugin_Build_AU=0;JucePlugin_Build_AUv3=0;JucePlugin_Build_AAX=0;JucePlugin_Build_Standalone=1;JucePlugin_Build_Unity=0;JucePlugin_Build_LV2=0;JucePlugin_Enable_IAA=0;JucePlugin_Enable_ARA=0;JucePlugin_Name=&quot;Color Assistant&quot;;JucePlugin_Desc=&quot;Color Assistant&quot;;JucePlugin_Manufacturer=&quot;EGURT&quot;;JucePlugin_ManufacturerWebsite=&quot;www.yourcompany.com&quot;;JucePlugin_ManufacturerEmail=&quot;&quot;;JucePlugin_ManufacturerCode=0x4d616e75;JucePlugin_PluginCode=0x4b74617a;JucePlugin_IsSynth=0;JucePlugin_WantsMidiInput=0;JucePlugin_ProducesMidiOutput=0;JucePlugin_IsMidiEffect=0;JucePlugin_EditorRequiresKeyboardFocus=0;JucePlugin_Version=1.0.0;JucePlugin_VersionCode=0x10000;JucePlugin_VersionString=&quot;1.0.0&quot;;JucePlugin_VSTUniqueID=JucePlugin_PluginCode;JucePlugin_VSTCategory=kPlugCategEffect;JucePlugin_Vst3Category=&quot;Fx&quot;;JucePlugin_AUMainType='aufx';JucePlugin_AUSubType=JucePlugin_PluginCode;JucePlugin_AUExportPrefix=ColorAssistantAU;JucePlugin_AUExportPrefixQuoted=&quot;ColorAssistantAU&quot;;JucePlugin_AUManufacturerCode=JucePlugin_ManufacturerCode;JucePlugin_CFBundleIdentifier=com.yourcompany.ColorAssistant;JucePlugin_AAXIdentifier=com.yourcompany.ColorAssistant;JucePlugin_AAXManufacturerCode=JucePlugin_ManufacturerCode;JucePlugin_AAXProductId=JucePlugin_PluginCode;JucePlugin_AAXCategory=0;JucePlugin_AAXDisableBypass=0;JucePlugin_AAXDisableMultiMono=0;JucePlugin_IAAType=0x61757278;JucePlugin_IAASubType=JucePlugin_PluginCode;JucePlugin_IAAName=&quot;EGURT: Color Assistant&quot;;JucePlugin_VSTNumMidiInputs=16;JucePlugin_VSTNumMidiOutputs=16;JucePlugin_ARAContentTypes=0;JucePlugin_ARATransformationFlags=0;JucePlugin_ARAFactoryID=&quot;com.yourcompany.ColorAssistant.factory&quot;;JucePlugin_ARADocumentArchiveID=&quot;com.yourcompany.ColorAssistant.aradocumentarchive.1.0.0&quot;;JucePlugin_ARACompatibleArchiveIDs=&quot;&quot;;JUCE_STANDALONE_APPLICATION=JucePlugin_Build_Standalone;JUCER_VS2022_78A503E=1;JUCE_APP_VERSION=1.0.0;JUCE_APP_VERSION_HEX=0x10000;JUCE_SHARED_CODE=1;_LIB;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <AssemblerListingLocation>$(IntDir)\</AssemblerListingLocation>
      <ObjectFileName>$(IntDir)\</ObjectFileName>
      <ProgramDataBaseFileName>$(IntDir)\Color Assistant.pdb</ProgramDataBaseFileName>
      <WarningLevel>Level4</WarningLevel>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <LanguageStandard>stdcpp17</LanguageStandard>
    </ClCompile>
    <ResourceCompile>
      <AdditionalIncludeDirectories>D:\JUCE\modules\juce_audio_processors\format_types\VST3_SDK;..\..\JuceLibraryCode;D:\JUCE\modules;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_CRT_SECURE_NO_WARNINGS;WIN32;_WINDOWS;NDEBUG;JUCE_PROJUCER_VERSION=0x80007;JUCE_MODULE_AVAILABLE_juce_analytics=1;JUCE_MODULE_AVAILABLE_juce_animation=1;JUCE_MODULE_AVAILABLE_juce_audio_basics=1;JUCE_MODULE_AVAILABLE_juce_audio_devices=1;JUCE_MODULE_AVAILABLE_juce_audio_formats=1;JUCE_MODULE_AVAILABLE_juce_audio_plugin_client=1;JUCE_MODULE_AVAILABLE_juce_audio_processors=1;JUCE_MODULE_AVAILABLE_juce_audio_utils=1;JUCE_MODULE_AVAILABLE_juce_core=1;JUCE_MODULE_AVAILABLE_juce_data_structures=1;JUCE_MODULE_AVAILABLE_juce_dsp=1;JUCE_MODULE_AVAILABLE_juce_events=1;JUCE_MODULE_AVAILABLE_juce_graphics=1;JUCE_MODULE_AVAILABLE_juce_gui_basics=1;JUCE_MODULE_AVAILABLE_juce_gui_extra=1;JUCE_MODULE_AVAILABLE_juce_midi_ci=1;JUCE_MODULE_AVAILABLE_juce_osc=1;JUCE_GLOBAL_MODULE_SETTINGS_INCLUDED=1;JUCE_VST3_CAN_REPLACE_VST2=0;JUCE_STRICT_REFCOUNTEDPOINTER=1;JucePlugin_Build_VST=0;JucePlugin_Build_VST3=1;JucePlugin_Build_AU=0;JucePlugin_Build_AUv3=0;JucePlugin_Build_AAX=0;JucePlugin_Build_Standalone=1;JucePlugin_Build_Unity=0;JucePlugin_Build_LV2=0;JucePlugin_Enable_IAA=0;JucePlugin_Enable_ARA=0;JucePlugin_Name=\&quot;Color Assistant\&quot;;JucePlugin_Desc=\&quot;Color Assistant\&quot;;JucePlugin_Manufacturer=\&quot;EGURT\&quot;;JucePlugin_ManufacturerWebsite=\&quot;www.yourcompany.com\&quot;;JucePlugin_ManufacturerEmail=\&quot;\&quot;;JucePlugin_ManufacturerCode=0x4d616e75;JucePlugin_PluginCode=0x4b74617a;JucePlugin_IsSynth=0;JucePlugin_WantsMidiInput=0;JucePlugin_ProducesMidiOutput=0;JucePlugin_IsMidiEffect=0;JucePlugin_EditorRequiresKeyboardFocus=0;JucePlugin_Version=1.0.0;JucePlugin_VersionCode=0x10000;JucePlugin_VersionString=\&quot;1.0.0\&quot;;JucePlugin_VSTUniqueID=JucePlugin_PluginCode;JucePlugin_VSTCategory=kPlugCategEffect;JucePlugin_Vst3Category=\&quot;Fx\&quot;;JucePlugin_AUMainType='aufx';JucePlugin_AUSubType=JucePlugin_PluginCode;JucePlugin_AUExportPrefix=ColorAssistantAU;JucePlugin_AUExportPrefixQuoted=\&quot;ColorAssistantAU\&quot;;JucePlugin_AUManufacturerCode=JucePlugin_ManufacturerCode;JucePlugin_CFBundleIdentifier=com.yourcompany.ColorAssistant;JucePlugin_AAXIdentifier=com.yourcompany.ColorAssistant;JucePlugin_AAXManufacturerCode=JucePlugin_ManufacturerCode;JucePlugin_AAXProductId=JucePlugin_PluginCode;JucePlugin_AAXCategory=0;JucePlugin_AAXDisableBypass=0;JucePlugin_AAXDisableMultiMono=0;JucePlugin_IAAType=0x61757278;JucePlugin_IAASubType=JucePlugin_PluginCode;JucePlugin_IAAName=\&quot;EGURT: Color Assistant\&quot;;JucePlugin_VSTNumMidiInputs=16;JucePlugin_VSTNumMidiOutputs=16;JucePlugin_ARAContentTypes=0;JucePlugin_ARATransformationFlags=0;JucePlugin_ARAFactoryID=\&quot;com.yourcompany.ColorAssistant.factory\&quot;;JucePlugin_ARADocumentArchiveID=\&quot;com.yourcompany.ColorAssistant.aradocumentarchive.1.0.0\&quot;;JucePlugin_ARACompatibleArchiveIDs=\&quot;\&quot;;JUCE_STANDALONE_APPLICATION=JucePlugin_Build_Standalone;JUCER_VS2022_78A503E=1;JUCE_APP_VERSION=1.0.0;JUCE_APP_VERSION_HEX=0x10000;JUCE_SHARED_CODE=1;_LIB;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ResourceCompile>
    <Link>
      <OutputFile>$(OutDir)\Color Assistant.lib</OutputFile>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <ProgramDatabaseFile>$(IntDir)\Color Assistant.pdb</ProgramDatabaseFile>
      <SubSystem>Windows</SubSystem>
      <OptimizeReferences>true</OptimizeReferences>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <LargeAddressAware>true</LargeAddressAware>
      <LinkTimeCodeGeneration>UseLinkTimeCodeGeneration</LinkTimeCodeGeneration>
    </Link>
    <Bscmake>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <OutputFile>$(IntDir)\Color Assistant.bsc</OutputFile>
    </Bscmake>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClCompile Include="..\..\Source\PluginProcessor.cpp"/>
    <ClCompile Include="..\..\Source\PluginEditor.cpp"/>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_analytics\analytics\juce_Analytics.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_analytics\analytics\juce_ButtonTracker.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_analytics\destinations\juce_ThreadedAnalyticsDestination.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_analytics\juce_analytics.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_animation\animation\juce_Animator.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_animation\animation\juce_AnimatorSetBuilder.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_animation\animation\juce_AnimatorUpdater.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_animation\animation\juce_Easings.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_animation\animation\juce_ValueAnimatorBuilder.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_animation\detail\chromium\cubic_bezier.cc">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_animation\juce_animation.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\audio_play_head\juce_AudioPlayHead.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\buffers\juce_AudioChannelSet.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\buffers\juce_AudioDataConverters.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\buffers\juce_AudioProcessLoadMeasurer.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\buffers\juce_FloatVectorOperations.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\midi\ump\juce_UMP_test.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\midi\ump\juce_UMPIterator.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\midi\ump\juce_UMPMidi1ToMidi2DefaultTranslator.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\midi\ump\juce_UMPSysEx7.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\midi\ump\juce_UMPUtils.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\midi\ump\juce_UMPView.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\midi\juce_MidiBuffer.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\midi\juce_MidiFile.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\midi\juce_MidiKeyboardState.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\midi\juce_MidiMessage.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\midi\juce_MidiMessageSequence.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\midi\juce_MidiRPN.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\mpe\juce_MPEInstrument.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\mpe\juce_MPEMessages.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\mpe\juce_MPENote.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\mpe\juce_MPESynthesiser.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\mpe\juce_MPESynthesiserBase.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\mpe\juce_MPESynthesiserVoice.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\mpe\juce_MPEUtils.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\mpe\juce_MPEValue.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\mpe\juce_MPEZoneLayout.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\sources\juce_BufferingAudioSource.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\sources\juce_ChannelRemappingAudioSource.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\sources\juce_IIRFilterAudioSource.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\sources\juce_MemoryAudioSource.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\sources\juce_MixerAudioSource.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\sources\juce_PositionableAudioSource.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\sources\juce_ResamplingAudioSource.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\sources\juce_ReverbAudioSource.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\sources\juce_ToneGeneratorAudioSource.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\synthesisers\juce_Synthesiser.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\utilities\juce_ADSR_test.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\utilities\juce_AudioWorkgroup.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\utilities\juce_IIRFilter.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\utilities\juce_Interpolators.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\utilities\juce_LagrangeInterpolator.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\utilities\juce_SmoothedValue.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\utilities\juce_WindowedSincInterpolator.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\juce_audio_basics.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\audio_io\juce_AudioDeviceManager.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\audio_io\juce_AudioIODevice.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\audio_io\juce_AudioIODeviceType.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\audio_io\juce_SampleRateHelpers.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\midi_io\juce_MidiDeviceListConnectionBroadcaster.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\midi_io\juce_MidiDevices.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\midi_io\juce_MidiMessageCollector.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\aaudio\AAudioLoader.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\aaudio\AudioStreamAAudio.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\common\AdpfWrapper.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\common\AudioSourceCaller.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\common\AudioStream.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\common\AudioStreamBuilder.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\common\DataConversionFlowGraph.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\common\FilterAudioStream.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\common\FixedBlockAdapter.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\common\FixedBlockReader.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\common\FixedBlockWriter.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\common\LatencyTuner.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\common\OboeExtensions.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\common\QuirksManager.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\common\SourceFloatCaller.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\common\SourceI16Caller.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\common\SourceI24Caller.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\common\SourceI32Caller.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\common\StabilizedCallback.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\common\Trace.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\common\Utilities.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\common\Version.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\fifo\FifoBuffer.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\fifo\FifoController.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\fifo\FifoControllerBase.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\fifo\FifoControllerIndirect.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\flowgraph\resampler\IntegerRatio.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\flowgraph\resampler\LinearResampler.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\flowgraph\resampler\MultiChannelResampler.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\flowgraph\resampler\PolyphaseResampler.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\flowgraph\resampler\PolyphaseResamplerMono.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\flowgraph\resampler\PolyphaseResamplerStereo.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\flowgraph\resampler\SincResampler.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\flowgraph\resampler\SincResamplerStereo.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\flowgraph\ChannelCountConverter.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\flowgraph\ClipToRange.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\flowgraph\FlowGraphNode.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\flowgraph\Limiter.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\flowgraph\ManyToMultiConverter.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\flowgraph\MonoBlend.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\flowgraph\MonoToMultiConverter.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\flowgraph\MultiToManyConverter.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\flowgraph\MultiToMonoConverter.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\flowgraph\RampLinear.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\flowgraph\SampleRateConverter.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\flowgraph\SinkFloat.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\flowgraph\SinkI8_24.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\flowgraph\SinkI16.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\flowgraph\SinkI24.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\flowgraph\SinkI32.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\flowgraph\SourceFloat.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\flowgraph\SourceI8_24.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\flowgraph\SourceI16.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\flowgraph\SourceI24.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\flowgraph\SourceI32.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\opensles\AudioInputStreamOpenSLES.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\opensles\AudioOutputStreamOpenSLES.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\opensles\AudioStreamBuffered.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\opensles\AudioStreamOpenSLES.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\opensles\EngineOpenSLES.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\opensles\OpenSLESUtilities.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\opensles\OutputMixerOpenSLES.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\juce_ALSA_linux.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\juce_ASIO_windows.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\juce_Audio_android.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\juce_Audio_ios.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\juce_Bela_linux.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\juce_CoreAudio_mac.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\juce_DirectSound_windows.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\juce_JackAudio.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\juce_Midi_android.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\juce_Midi_linux.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\juce_Midi_windows.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\juce_Oboe_android.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\juce_OpenSL_android.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\juce_WASAPI_windows.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\sources\juce_AudioSourcePlayer.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\sources\juce_AudioTransportSource.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\juce_audio_devices.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\flac\libFLAC\deduplication\bitreader_read_rice_signed_block.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\flac\libFLAC\deduplication\lpc_compute_autocorrelation_intrin.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\flac\libFLAC\deduplication\lpc_compute_autocorrelation_intrin_neon.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\flac\libFLAC\bitmath.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\flac\libFLAC\bitreader.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\flac\libFLAC\bitwriter.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\flac\libFLAC\cpu.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\flac\libFLAC\crc.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\flac\libFLAC\fixed.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\flac\libFLAC\float.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\flac\libFLAC\format.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\flac\libFLAC\lpc_flac.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\flac\libFLAC\lpc_intrin_neon.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\flac\libFLAC\md5.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\flac\libFLAC\memory.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\flac\libFLAC\stream_decoder.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\flac\libFLAC\stream_encoder.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\flac\libFLAC\stream_encoder_framing.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\flac\libFLAC\window_flac.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\analysis.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\bitrate.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\block.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\codebook.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\envelope.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\floor0.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\floor1.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\info.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\lookup.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\lpc.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\lsp.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\mapping0.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\mdct.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\misc.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\psy.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\registry.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\res0.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\sharedbook.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\smallft.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\synthesis.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\vorbisenc.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\vorbisfile.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\window.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\oggvorbis\bitwise.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\oggvorbis\framing.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\juce_AiffAudioFormat.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\juce_CoreAudioFormat.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\juce_FlacAudioFormat.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\juce_LAMEEncoderAudioFormat.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\juce_MP3AudioFormat.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\juce_OggVorbisAudioFormat.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\juce_WavAudioFormat.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\juce_WindowsMediaAudioFormat.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\format\juce_ARAAudioReaders.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\format\juce_AudioFormat.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\format\juce_AudioFormatManager.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\format\juce_AudioFormatReader.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\format\juce_AudioFormatReaderSource.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\format\juce_AudioFormatWriter.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\format\juce_AudioSubsectionReader.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\format\juce_BufferingAudioFormatReader.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\sampler\juce_Sampler.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\juce_audio_formats.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_plugin_client\juce_audio_plugin_client_ARA.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format\juce_AudioPluginFormat.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format\juce_AudioPluginFormatManager.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\lilv\src\zix\tree.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\lilv\src\collections.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\lilv\src\filesystem.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\lilv\src\instance.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\lilv\src\lib.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\lilv\src\node.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\lilv\src\plugin.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\lilv\src\pluginclass.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\lilv\src\port.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\lilv\src\query.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\lilv\src\scalepoint.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\lilv\src\state.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\lilv\src\ui.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\lilv\src\util.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\lilv\src\world.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\atom\atom-test-utils.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\atom\atom-test.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\atom\forge-overflow-test.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\serd\src\base64.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\serd\src\byte_source.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\serd\src\env.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\serd\src\n3.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\serd\src\node.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\serd\src\reader.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\serd\src\serdi.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\serd\src\string.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\serd\src\system.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\serd\src\uri.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\serd\src\writer.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\sord\src\zix\btree.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\sord\src\zix\digest.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\sord\src\zix\hash.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\sord\src\sord.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\sord\src\sord_test.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\sord\src\sord_validate.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\sord\src\sordi.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\sord\src\sordmm_test.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\sord\src\syntax.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\sratom\src\sratom.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\base\source\baseiids.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\base\source\fbuffer.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\base\source\fdebug.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\base\source\fobject.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\base\source\fstreamer.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\base\source\fstring.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\base\source\updatehandler.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\base\thread\source\flock.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\base\conststringtable.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\base\coreiids.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\base\funknown.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\base\ustring.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\samples\vst-utilities\moduleinfotool\source\main.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\common\commonstringconvert.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\common\memorystream.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\common\pluginview.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\common\readfile.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\hosting\hostclasses.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\hosting\module.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\hosting\module_linux.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\hosting\module_win32.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\hosting\pluginterfacesupport.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\moduleinfo\moduleinfocreator.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\moduleinfo\moduleinfoparser.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\utility\stringconvert.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\utility\vst2persistence.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\vstbus.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\vstcomponent.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\vstcomponentbase.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\vsteditcontroller.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\vstinitiids.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\vstparameters.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\vstpresetfile.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\juce_ARACommon.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\juce_ARAHosting.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\juce_LADSPAPluginFormat.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\juce_LegacyAudioParameter.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\juce_LV2PluginFormat.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\juce_LV2PluginFormat_test.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\juce_LV2SupportLibs.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\juce_VST3PluginFormat.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\juce_VST3PluginFormat_test.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\juce_VSTPluginFormat.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\processors\juce_AudioPluginInstance.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\processors\juce_AudioProcessor.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\processors\juce_AudioProcessorEditor.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\processors\juce_AudioProcessorGraph.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\processors\juce_AudioProcessorParameterGroup.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\processors\juce_GenericAudioProcessorEditor.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\processors\juce_PluginDescription.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\scanning\juce_KnownPluginList.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\scanning\juce_PluginDirectoryScanner.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\scanning\juce_PluginListComponent.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\utilities\ARA\juce_ARA_utils.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\utilities\ARA\juce_ARADocumentController.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\utilities\ARA\juce_ARADocumentControllerCommon.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\utilities\ARA\juce_ARAModelObjects.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\utilities\ARA\juce_ARAPlugInInstanceRoles.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\utilities\ARA\juce_AudioProcessor_ARAExtensions.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\utilities\juce_AAXClientExtensions.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\utilities\juce_AudioParameterBool.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\utilities\juce_AudioParameterChoice.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\utilities\juce_AudioParameterFloat.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\utilities\juce_AudioParameterInt.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\utilities\juce_AudioProcessorParameterWithID.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\utilities\juce_AudioProcessorValueTreeState.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\utilities\juce_ParameterAttachments.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\utilities\juce_PluginHostType.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\utilities\juce_RangedAudioParameter.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\utilities\juce_VST2ClientExtensions.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\utilities\juce_VST3ClientExtensions.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\juce_audio_processors.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\juce_audio_processors_ara.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\juce_audio_processors_lv2_libs.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_utils\audio_cd\juce_AudioCDReader.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_utils\gui\juce_AudioAppComponent.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_utils\gui\juce_AudioDeviceSelectorComponent.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_utils\gui\juce_AudioThumbnail.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_utils\gui\juce_AudioThumbnailCache.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_utils\gui\juce_AudioVisualiserComponent.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_utils\gui\juce_KeyboardComponentBase.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_utils\gui\juce_MidiKeyboardComponent.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_utils\gui\juce_MPEKeyboardComponent.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_utils\native\juce_AudioCDBurner_windows.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_utils\native\juce_AudioCDReader_linux.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_utils\native\juce_AudioCDReader_windows.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_utils\native\juce_BluetoothMidiDevicePairingDialogue_android.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_utils\native\juce_BluetoothMidiDevicePairingDialogue_linux.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_utils\native\juce_BluetoothMidiDevicePairingDialogue_windows.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_utils\players\juce_AudioProcessorPlayer.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_utils\players\juce_SoundPlayer.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_audio_utils\juce_audio_utils.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\containers\juce_AbstractFifo.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\containers\juce_ArrayBase.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\containers\juce_DynamicObject.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\containers\juce_Enumerate_test.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\containers\juce_FixedSizeFunction_test.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\containers\juce_HashMap_test.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\containers\juce_ListenerList_test.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\containers\juce_NamedValueSet.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\containers\juce_Optional_test.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\containers\juce_OwnedArray.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\containers\juce_PropertySet.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\containers\juce_ReferenceCountedArray.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\containers\juce_SparseSet.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\containers\juce_Variant.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\files\juce_common_MimeTypes.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\files\juce_DirectoryIterator.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\files\juce_File.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\files\juce_FileFilter.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\files\juce_FileInputStream.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\files\juce_FileOutputStream.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\files\juce_FileSearchPath.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\files\juce_RangedDirectoryIterator.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\files\juce_TemporaryFile.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\files\juce_WildcardFileFilter.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\json\juce_JSON.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\json\juce_JSONSerialisation_test.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\json\juce_JSONUtils.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\logging\juce_FileLogger.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\logging\juce_Logger.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\maths\juce_BigInteger.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\maths\juce_Expression.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\maths\juce_MathsFunctions_test.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\maths\juce_Random.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\memory\juce_AllocationHooks.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\memory\juce_MemoryBlock.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\memory\juce_SharedResourcePointer_test.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\misc\juce_ConsoleApplication.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\misc\juce_EnumHelpers_test.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\misc\juce_Result.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\misc\juce_RuntimePermissions.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\misc\juce_ScopeGuard.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\misc\juce_Uuid.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\native\juce_AndroidDocument_android.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\native\juce_CommonFile_linux.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\native\juce_Files_android.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\native\juce_Files_linux.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\native\juce_Files_windows.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\native\juce_JNIHelpers_android.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\native\juce_Misc_android.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\native\juce_NamedPipe_posix.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\native\juce_Network_android.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\native\juce_Network_curl.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\native\juce_Network_linux.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\native\juce_Network_windows.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\native\juce_PlatformTimer_generic.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\native\juce_PlatformTimer_windows.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\native\juce_Registry_windows.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\native\juce_RuntimePermissions_android.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\native\juce_SystemStats_android.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\native\juce_SystemStats_linux.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\native\juce_SystemStats_wasm.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\native\juce_SystemStats_windows.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\native\juce_Threads_android.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\native\juce_Threads_linux.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\native\juce_Threads_windows.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\network\juce_IPAddress.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\network\juce_MACAddress.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\network\juce_NamedPipe.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\network\juce_Socket.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\network\juce_URL.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\network\juce_WebInputStream.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\streams\juce_BufferedInputStream.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\streams\juce_FileInputSource.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\streams\juce_InputStream.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\streams\juce_MemoryInputStream.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\streams\juce_MemoryOutputStream.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\streams\juce_OutputStream.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\streams\juce_SubregionStream.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\streams\juce_URLInputSource.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\system\juce_SystemStats.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\text\juce_Base64.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\text\juce_CharacterFunctions.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\text\juce_CharPointer_UTF8_test.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\text\juce_CharPointer_UTF16_test.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\text\juce_CharPointer_UTF32_test.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\text\juce_Identifier.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\text\juce_LocalisedStrings.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\text\juce_String.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\text\juce_StringArray.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\text\juce_StringPairArray.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\text\juce_StringPool.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\text\juce_TextDiff.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\threads\juce_ChildProcess.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\threads\juce_HighResolutionTimer.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\threads\juce_ReadWriteLock.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\threads\juce_Thread.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\threads\juce_ThreadPool.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\threads\juce_TimeSliceThread.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\threads\juce_WaitableEvent.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\time\juce_PerformanceCounter.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\time\juce_RelativeTime.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\time\juce_Time.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\unit_tests\juce_UnitTest.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\xml\juce_XmlDocument.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\xml\juce_XmlElement.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\zip\zlib\adler32.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\zip\zlib\compress.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\zip\zlib\crc32.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\zip\zlib\deflate.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\zip\zlib\infback.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\zip\zlib\inffast.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\zip\zlib\inflate.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\zip\zlib\inftrees.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\zip\zlib\trees.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\zip\zlib\uncompr.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\zip\zlib\zutil.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\zip\juce_GZIPCompressorOutputStream.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\zip\juce_GZIPDecompressorInputStream.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\zip\juce_ZipFile.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\juce_core.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_core\juce_core_CompilationTime.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_data_structures\app_properties\juce_ApplicationProperties.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_data_structures\app_properties\juce_PropertiesFile.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_data_structures\undomanager\juce_UndoableAction.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_data_structures\undomanager\juce_UndoManager.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_data_structures\values\juce_CachedValue.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_data_structures\values\juce_Value.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_data_structures\values\juce_ValueTree.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_data_structures\values\juce_ValueTreePropertyWithDefault_test.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_data_structures\values\juce_ValueTreeSynchroniser.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_data_structures\juce_data_structures.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_dsp\containers\juce_AudioBlock_test.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_dsp\containers\juce_SIMDRegister_test.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_dsp\filter_design\juce_FilterDesign.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_dsp\frequency\juce_Convolution.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_dsp\frequency\juce_Convolution_test.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_dsp\frequency\juce_FFT.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_dsp\frequency\juce_FFT_test.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_dsp\frequency\juce_Windowing.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_dsp\maths\juce_LogRampedValue_test.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_dsp\maths\juce_LookupTable.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_dsp\maths\juce_Matrix.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_dsp\maths\juce_Matrix_test.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_dsp\maths\juce_SpecialFunctions.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_dsp\native\juce_SIMDNativeOps_avx.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_dsp\native\juce_SIMDNativeOps_neon.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_dsp\native\juce_SIMDNativeOps_sse.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_dsp\processors\juce_BallisticsFilter.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_dsp\processors\juce_DelayLine.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_dsp\processors\juce_DryWetMixer.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_dsp\processors\juce_FIRFilter.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_dsp\processors\juce_FIRFilter_test.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_dsp\processors\juce_FirstOrderTPTFilter.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_dsp\processors\juce_IIRFilter.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_dsp\processors\juce_LinkwitzRileyFilter.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_dsp\processors\juce_Oversampling.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_dsp\processors\juce_Panner.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_dsp\processors\juce_ProcessorChain_test.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_dsp\processors\juce_StateVariableTPTFilter.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_dsp\widgets\juce_Chorus.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_dsp\widgets\juce_Compressor.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_dsp\widgets\juce_LadderFilter.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_dsp\widgets\juce_Limiter.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_dsp\widgets\juce_NoiseGate.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_dsp\widgets\juce_Phaser.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_dsp\juce_dsp.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_events\broadcasters\juce_ActionBroadcaster.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_events\broadcasters\juce_AsyncUpdater.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_events\broadcasters\juce_ChangeBroadcaster.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_events\broadcasters\juce_LockingAsyncUpdater.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_events\interprocess\juce_ChildProcessManager.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_events\interprocess\juce_ConnectedChildProcess.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_events\interprocess\juce_InterprocessConnection.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_events\interprocess\juce_InterprocessConnectionServer.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_events\interprocess\juce_NetworkServiceDiscovery.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_events\messages\juce_ApplicationBase.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_events\messages\juce_DeletedAtShutdown.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_events\messages\juce_MessageListener.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_events\messages\juce_MessageManager.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_events\native\juce_Messaging_android.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_events\native\juce_Messaging_linux.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_events\native\juce_Messaging_windows.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_events\native\juce_ScopedLowPowerModeDisabler.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_events\native\juce_WinRTWrapper_windows.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_events\timers\juce_MultiTimer.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_events\timers\juce_Timer.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_events\juce_events.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\colour\juce_Colour.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\colour\juce_ColourGradient.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\colour\juce_Colours.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\colour\juce_FillType.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\contexts\juce_GraphicsContext.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\contexts\juce_LowLevelGraphicsSoftwareRenderer.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\detail\juce_JustifiedText.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\detail\juce_Ranges.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\detail\juce_ShapedText.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\detail\juce_SimpleShapedText.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\effects\juce_DropShadowEffect.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\effects\juce_GlowEffect.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\OT\Var\VARC\VARC.cc">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\failing-alloc.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\harfbuzz-subset.cc">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\harfbuzz.cc">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-aat-layout.cc">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-aat-map.cc">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-blob.cc">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-buffer-serialize.cc">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-buffer-verify.cc">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-buffer.cc">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-common.cc">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-coretext-font.cc">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-coretext-shape.cc">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-directwrite.cc">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-draw.cc">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-face-builder.cc">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-face.cc">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-fallback-shape.cc">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-font.cc">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ft.cc">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-gdi.cc">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-glib.cc">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-gobject-structs.cc">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-graphite2.cc">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-icu.cc">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-map.cc">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-number.cc">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-cff1-table.cc">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-cff2-table.cc">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-color.cc">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-face.cc">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-font.cc">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-layout.cc">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-map.cc">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-math.cc">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-meta.cc">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-metrics.cc">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-name.cc">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-shape-fallback.cc">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-shape-normalize.cc">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-shape.cc">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper-arabic.cc">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper-default.cc">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper-hangul.cc">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper-hebrew.cc">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper-indic-table.cc">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper-indic.cc">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper-khmer.cc">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper-myanmar.cc">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper-syllabic.cc">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper-thai.cc">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper-use.cc">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper-vowel-constraints.cc">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-tag.cc">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-var.cc">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-outline.cc">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-paint-extents.cc">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-paint.cc">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-set.cc">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-shape-plan.cc">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-shape.cc">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-shaper.cc">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-static.cc">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-style.cc">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-subset-cff-common.cc">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-subset-cff1.cc">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-subset-cff2.cc">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-subset-input.cc">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-subset-instancer-iup.cc">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-subset-instancer-solver.cc">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-subset-plan.cc">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-subset-repacker.cc">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-subset.cc">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ucd.cc">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-unicode.cc">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-uniscribe.cc">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-wasm-api.cc">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-wasm-shape.cc">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\juce_AttributedString.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\juce_Font.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\juce_FontOptions.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\juce_GlyphArrangement.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\juce_TextLayout.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\juce_Typeface.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\juce_TypefaceTestData.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\geometry\juce_AffineTransform.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\geometry\juce_EdgeTable.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\geometry\juce_Parallelogram_test.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\geometry\juce_Path.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\geometry\juce_PathIterator.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\geometry\juce_PathStrokeType.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\geometry\juce_Rectangle_test.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\image_formats\jpglib\jcapimin.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\image_formats\jpglib\jcapistd.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\image_formats\jpglib\jccoefct.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\image_formats\jpglib\jccolor.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\image_formats\jpglib\jcdctmgr.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\image_formats\jpglib\jchuff.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\image_formats\jpglib\jcinit.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\image_formats\jpglib\jcmainct.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\image_formats\jpglib\jcmarker.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\image_formats\jpglib\jcmaster.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\image_formats\jpglib\jcomapi.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\image_formats\jpglib\jcparam.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\image_formats\jpglib\jcphuff.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\image_formats\jpglib\jcprepct.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\image_formats\jpglib\jcsample.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\image_formats\jpglib\jctrans.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\image_formats\jpglib\jdapimin.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\image_formats\jpglib\jdapistd.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\image_formats\jpglib\jdatasrc.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\image_formats\jpglib\jdcoefct.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\image_formats\jpglib\jdcolor.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\image_formats\jpglib\jddctmgr.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\image_formats\jpglib\jdhuff.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\image_formats\jpglib\jdinput.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\image_formats\jpglib\jdmainct.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\image_formats\jpglib\jdmarker.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\image_formats\jpglib\jdmaster.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\image_formats\jpglib\jdmerge.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\image_formats\jpglib\jdphuff.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\image_formats\jpglib\jdpostct.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\image_formats\jpglib\jdsample.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\image_formats\jpglib\jdtrans.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\image_formats\jpglib\jerror.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\image_formats\jpglib\jfdctflt.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\image_formats\jpglib\jfdctfst.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\image_formats\jpglib\jfdctint.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\image_formats\jpglib\jidctflt.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\image_formats\jpglib\jidctfst.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\image_formats\jpglib\jidctint.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\image_formats\jpglib\jidctred.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\image_formats\jpglib\jmemmgr.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\image_formats\jpglib\jmemnobs.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\image_formats\jpglib\jquant1.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\image_formats\jpglib\jquant2.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\image_formats\jpglib\jutils.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\image_formats\jpglib\transupp.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\image_formats\pnglib\png.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\image_formats\pnglib\pngerror.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\image_formats\pnglib\pngget.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\image_formats\pnglib\pngmem.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\image_formats\pnglib\pngpread.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\image_formats\pnglib\pngread.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\image_formats\pnglib\pngrio.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\image_formats\pnglib\pngrtran.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\image_formats\pnglib\pngrutil.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\image_formats\pnglib\pngset.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\image_formats\pnglib\pngtrans.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\image_formats\pnglib\pngwio.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\image_formats\pnglib\pngwrite.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\image_formats\pnglib\pngwtran.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\image_formats\pnglib\pngwutil.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\image_formats\juce_GIFLoader.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\image_formats\juce_JPEGLoader.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\image_formats\juce_PNGLoader.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\images\juce_Image.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\images\juce_ImageCache.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\images\juce_ImageConvolutionKernel.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\images\juce_ImageFileFormat.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\native\juce_Direct2DGraphicsContext_windows.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\native\juce_Direct2DHelpers_windows.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\native\juce_Direct2DHwndContext_windows.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\native\juce_Direct2DImage_windows.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\native\juce_Direct2DImageContext_windows.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\native\juce_Direct2DMetrics_windows.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\native\juce_Direct2DResources_windows.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\native\juce_DirectWriteTypeface_windows.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\native\juce_Fonts_android.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\native\juce_Fonts_freetype.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\native\juce_Fonts_linux.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\native\juce_GraphicsContext_android.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\native\juce_IconHelpers_android.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\native\juce_IconHelpers_linux.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\native\juce_IconHelpers_mac.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\native\juce_IconHelpers_windows.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\placement\juce_RectanglePlacement.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\unicode\sheenbidi\Source\BidiChain.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\unicode\sheenbidi\Source\BidiTypeLookup.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\unicode\sheenbidi\Source\BracketQueue.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\unicode\sheenbidi\Source\GeneralCategoryLookup.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\unicode\sheenbidi\Source\IsolatingRun.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\unicode\sheenbidi\Source\LevelRun.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\unicode\sheenbidi\Source\PairingLookup.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\unicode\sheenbidi\Source\RunQueue.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\unicode\sheenbidi\Source\SBAlgorithm.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\unicode\sheenbidi\Source\SBBase.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\unicode\sheenbidi\Source\SBCodepointSequence.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\unicode\sheenbidi\Source\SBLine.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\unicode\sheenbidi\Source\SBLog.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\unicode\sheenbidi\Source\SBMirrorLocator.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\unicode\sheenbidi\Source\SBParagraph.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\unicode\sheenbidi\Source\SBScriptLocator.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\unicode\sheenbidi\Source\ScriptLookup.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\unicode\sheenbidi\Source\ScriptStack.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\unicode\sheenbidi\Source\SheenBidi.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\unicode\sheenbidi\Source\StatusStack.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\unicode\juce_Unicode.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\unicode\juce_UnicodeBidi.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\unicode\juce_UnicodeGenerated.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\unicode\juce_UnicodeLine.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\unicode\juce_UnicodeScript.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\unicode\juce_UnicodeUtils.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\juce_graphics.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\juce_graphics_Harfbuzz.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_graphics\juce_graphics_Sheenbidi.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\accessibility\juce_AccessibilityHandler.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\application\juce_Application.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\buttons\juce_ArrowButton.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\buttons\juce_Button.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\buttons\juce_DrawableButton.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\buttons\juce_HyperlinkButton.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\buttons\juce_ImageButton.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\buttons\juce_ShapeButton.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\buttons\juce_TextButton.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\buttons\juce_ToggleButton.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\buttons\juce_ToolbarButton.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\commands\juce_ApplicationCommandInfo.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\commands\juce_ApplicationCommandManager.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\commands\juce_ApplicationCommandTarget.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\commands\juce_KeyPressMappingSet.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\components\juce_Component.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\components\juce_ComponentListener.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\components\juce_FocusTraverser.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\components\juce_ModalComponentManager.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\desktop\juce_Desktop.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\desktop\juce_Displays.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\detail\juce_AccessibilityHelpers.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\detail\juce_ComponentPeerHelpers.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\drawables\juce_Drawable.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\drawables\juce_DrawableComposite.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\drawables\juce_DrawableImage.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\drawables\juce_DrawablePath.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\drawables\juce_DrawableRectangle.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\drawables\juce_DrawableShape.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\drawables\juce_DrawableText.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\drawables\juce_SVGParser.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\filebrowser\juce_ContentSharer.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\filebrowser\juce_DirectoryContentsDisplayComponent.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\filebrowser\juce_DirectoryContentsList.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\filebrowser\juce_FileBrowserComponent.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\filebrowser\juce_FileChooser.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\filebrowser\juce_FileChooserDialogBox.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\filebrowser\juce_FileListComponent.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\filebrowser\juce_FilenameComponent.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\filebrowser\juce_FileSearchPathListComponent.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\filebrowser\juce_FileTreeComponent.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\filebrowser\juce_ImagePreviewComponent.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\keyboard\juce_CaretComponent.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\keyboard\juce_KeyboardFocusTraverser.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\keyboard\juce_KeyListener.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\keyboard\juce_KeyPress.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\keyboard\juce_ModifierKeys.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\layout\juce_BorderedComponentBoundsConstrainer.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\layout\juce_ComponentAnimator.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\layout\juce_ComponentBoundsConstrainer.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\layout\juce_ComponentBuilder.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\layout\juce_ComponentMovementWatcher.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\layout\juce_ConcertinaPanel.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\layout\juce_FlexBox.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\layout\juce_Grid.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\layout\juce_GridItem.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\layout\juce_GroupComponent.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\layout\juce_MultiDocumentPanel.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\layout\juce_ResizableBorderComponent.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\layout\juce_ResizableCornerComponent.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\layout\juce_ResizableEdgeComponent.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\layout\juce_ScrollBar.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\layout\juce_SidePanel.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\layout\juce_StretchableLayoutManager.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\layout\juce_StretchableLayoutResizerBar.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\layout\juce_StretchableObjectResizer.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\layout\juce_TabbedButtonBar.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\layout\juce_TabbedComponent.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\layout\juce_Viewport.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\lookandfeel\juce_LookAndFeel.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\lookandfeel\juce_LookAndFeel_V1.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\lookandfeel\juce_LookAndFeel_V2.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\lookandfeel\juce_LookAndFeel_V3.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\lookandfeel\juce_LookAndFeel_V4.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\menus\juce_BurgerMenuComponent.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\menus\juce_MenuBarComponent.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\menus\juce_MenuBarModel.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\menus\juce_PopupMenu.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\misc\juce_BubbleComponent.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\misc\juce_DropShadower.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\misc\juce_FocusOutline.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\mouse\juce_ComponentDragger.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\mouse\juce_DragAndDropContainer.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\mouse\juce_MouseCursor.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\mouse\juce_MouseEvent.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\mouse\juce_MouseInactivityDetector.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\mouse\juce_MouseInputSource.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\mouse\juce_MouseListener.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\native\accessibility\juce_Accessibility.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\native\accessibility\juce_Accessibility_android.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\native\accessibility\juce_Accessibility_windows.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\native\accessibility\juce_AccessibilityElement_windows.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\native\accessibility\juce_AccessibilityTextHelpers_test.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\native\juce_ContentSharer_android.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\native\juce_ContentSharer_ios.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\native\juce_DragAndDrop_linux.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\native\juce_DragAndDrop_windows.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\native\juce_FileChooser_android.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\native\juce_FileChooser_linux.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\native\juce_FileChooser_windows.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\native\juce_NativeMessageBox_android.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\native\juce_NativeMessageBox_linux.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\native\juce_NativeMessageBox_windows.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\native\juce_ScopedDPIAwarenessDisabler.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\native\juce_VBlank_windows.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\native\juce_Windowing_android.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\native\juce_Windowing_linux.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\native\juce_Windowing_windows.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\native\juce_WindowsHooks_windows.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\native\juce_WindowUtils_android.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\native\juce_WindowUtils_linux.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\native\juce_WindowUtils_windows.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\native\juce_XSymbols_linux.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\native\juce_XWindowSystem_linux.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\positioning\juce_MarkerList.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\positioning\juce_RelativeCoordinate.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\positioning\juce_RelativeCoordinatePositioner.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\positioning\juce_RelativeParallelogram.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\positioning\juce_RelativePoint.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\positioning\juce_RelativePointPath.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\positioning\juce_RelativeRectangle.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\properties\juce_BooleanPropertyComponent.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\properties\juce_ButtonPropertyComponent.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\properties\juce_ChoicePropertyComponent.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\properties\juce_MultiChoicePropertyComponent.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\properties\juce_PropertyComponent.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\properties\juce_PropertyPanel.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\properties\juce_SliderPropertyComponent.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\properties\juce_TextPropertyComponent.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\widgets\juce_ComboBox.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\widgets\juce_ImageComponent.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\widgets\juce_Label.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\widgets\juce_ListBox.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\widgets\juce_ProgressBar.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\widgets\juce_Slider.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\widgets\juce_TableHeaderComponent.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\widgets\juce_TableListBox.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\widgets\juce_TextEditor.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\widgets\juce_TextEditorModel.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\widgets\juce_Toolbar.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\widgets\juce_ToolbarItemComponent.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\widgets\juce_ToolbarItemPalette.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\widgets\juce_TreeView.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\windows\juce_AlertWindow.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\windows\juce_CallOutBox.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\windows\juce_ComponentPeer.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\windows\juce_DialogWindow.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\windows\juce_DocumentWindow.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\windows\juce_MessageBoxOptions.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\windows\juce_NativeMessageBox.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\windows\juce_NativeScaleFactorNotifier.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\windows\juce_ResizableWindow.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\windows\juce_ScopedMessageBox.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\windows\juce_ThreadWithProgressWindow.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\windows\juce_TooltipWindow.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\windows\juce_TopLevelWindow.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\windows\juce_VBlankAttachment.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\juce_gui_basics.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_extra\code_editor\juce_CodeDocument.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_extra\code_editor\juce_CodeEditorComponent.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_extra\code_editor\juce_CPlusPlusCodeTokeniser.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_extra\code_editor\juce_LuaCodeTokeniser.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_extra\code_editor\juce_XMLCodeTokeniser.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_extra\documents\juce_FileBasedDocument.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_extra\misc\juce_AnimatedAppComponent.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_extra\misc\juce_BubbleMessageComponent.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_extra\misc\juce_ColourSelector.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_extra\misc\juce_KeyMappingEditorComponent.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_extra\misc\juce_LiveConstantEditor.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_extra\misc\juce_PreferencesPanel.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_extra\misc\juce_PushNotifications.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_extra\misc\juce_RecentlyOpenedFilesList.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_extra\misc\juce_SplashScreen.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_extra\misc\juce_SystemTrayIconComponent.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_extra\misc\juce_WebBrowserComponent.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_extra\misc\juce_WebControlRelays.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_extra\native\juce_ActiveXComponent_windows.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_extra\native\juce_AndroidViewComponent.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_extra\native\juce_HWNDComponent_windows.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_extra\native\juce_PushNotifications_android.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_extra\native\juce_PushNotifications_ios.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_extra\native\juce_PushNotifications_mac.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_extra\native\juce_SystemTrayIcon_linux.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_extra\native\juce_SystemTrayIcon_mac.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_extra\native\juce_SystemTrayIcon_windows.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_extra\native\juce_WebBrowserComponent_android.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_extra\native\juce_WebBrowserComponent_linux.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_extra\native\juce_WebBrowserComponent_windows.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_extra\native\juce_XEmbedComponent_linux.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_gui_extra\juce_gui_extra.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_midi_ci\ci\juce_CIDevice.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_midi_ci\ci\juce_CIEncodings.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_midi_ci\ci\juce_CIParser.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_midi_ci\ci\juce_CIProfileHost.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_midi_ci\ci\juce_CIProfileStates.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_midi_ci\ci\juce_CIPropertyDelegate.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_midi_ci\ci\juce_CIPropertyExchangeCache.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_midi_ci\ci\juce_CIPropertyHost.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_midi_ci\ci\juce_CIResponderOutput.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_midi_ci\ci\juce_CISubscriptionManager.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_midi_ci\detail\juce_CIPropertyDataMessageChunker.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_midi_ci\detail\juce_CIResponder.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_midi_ci\juce_midi_ci.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_osc\osc\juce_OSCAddress.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_osc\osc\juce_OSCArgument.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_osc\osc\juce_OSCBundle.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_osc\osc\juce_OSCMessage.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_osc\osc\juce_OSCReceiver.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_osc\osc\juce_OSCSender.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_osc\osc\juce_OSCTimeTag.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_osc\osc\juce_OSCTypes.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\JUCE\modules\juce_osc\juce_osc.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\..\JuceLibraryCode\include_juce_analytics.cpp"/>
    <ClCompile Include="..\..\JuceLibraryCode\include_juce_animation.cpp"/>
    <ClCompile Include="..\..\JuceLibraryCode\include_juce_audio_basics.cpp"/>
    <ClCompile Include="..\..\JuceLibraryCode\include_juce_audio_devices.cpp"/>
    <ClCompile Include="..\..\JuceLibraryCode\include_juce_audio_formats.cpp"/>
    <ClCompile Include="..\..\JuceLibraryCode\include_juce_audio_plugin_client_ARA.cpp"/>
    <ClCompile Include="..\..\JuceLibraryCode\include_juce_audio_processors.cpp">
      <AdditionalOptions> /bigobj %(AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\JuceLibraryCode\include_juce_audio_processors_ara.cpp"/>
    <ClCompile Include="..\..\JuceLibraryCode\include_juce_audio_processors_lv2_libs.cpp"/>
    <ClCompile Include="..\..\JuceLibraryCode\include_juce_audio_utils.cpp"/>
    <ClCompile Include="..\..\JuceLibraryCode\include_juce_core.cpp">
      <AdditionalOptions> /bigobj %(AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\JuceLibraryCode\include_juce_core_CompilationTime.cpp"/>
    <ClCompile Include="..\..\JuceLibraryCode\include_juce_data_structures.cpp"/>
    <ClCompile Include="..\..\JuceLibraryCode\include_juce_dsp.cpp"/>
    <ClCompile Include="..\..\JuceLibraryCode\include_juce_events.cpp"/>
    <ClCompile Include="..\..\JuceLibraryCode\include_juce_graphics.cpp">
      <AdditionalOptions> /bigobj %(AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\JuceLibraryCode\include_juce_graphics_Harfbuzz.cpp"/>
    <ClCompile Include="..\..\JuceLibraryCode\include_juce_graphics_Sheenbidi.c"/>
    <ClCompile Include="..\..\JuceLibraryCode\include_juce_gui_basics.cpp">
      <AdditionalOptions> /bigobj %(AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\JuceLibraryCode\include_juce_gui_extra.cpp"/>
    <ClCompile Include="..\..\JuceLibraryCode\include_juce_midi_ci.cpp"/>
    <ClCompile Include="..\..\JuceLibraryCode\include_juce_osc.cpp"/>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\..\Source\PluginProcessor.h"/>
    <ClInclude Include="..\..\Source\PluginEditor.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_analytics\analytics\juce_Analytics.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_analytics\analytics\juce_ButtonTracker.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_analytics\destinations\juce_AnalyticsDestination.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_analytics\destinations\juce_ThreadedAnalyticsDestination.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_analytics\juce_analytics.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_animation\animation\juce_Animator.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_animation\animation\juce_AnimatorSetBuilder.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_animation\animation\juce_AnimatorUpdater.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_animation\animation\juce_Easings.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_animation\animation\juce_StaticAnimationLimits.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_animation\animation\juce_ValueAnimatorBuilder.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_animation\animation\juce_VBlankAnimatorUpdater.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_animation\detail\chromium\cubic_bezier.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_animation\detail\juce_ArrayAndTupleOps.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_animation\juce_animation.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\audio_play_head\juce_AudioPlayHead.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\buffers\juce_AudioChannelSet.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\buffers\juce_AudioDataConverters.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\buffers\juce_AudioProcessLoadMeasurer.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\buffers\juce_AudioSampleBuffer.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\buffers\juce_FloatVectorOperations.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\midi\ump\juce_UMP.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\midi\ump\juce_UMPacket.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\midi\ump\juce_UMPackets.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\midi\ump\juce_UMPBytesOnGroup.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\midi\ump\juce_UMPConversion.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\midi\ump\juce_UMPConverters.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\midi\ump\juce_UMPDeviceInfo.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\midi\ump\juce_UMPDispatcher.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\midi\ump\juce_UMPFactory.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\midi\ump\juce_UMPIterator.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\midi\ump\juce_UMPMidi1ToBytestreamTranslator.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\midi\ump\juce_UMPMidi1ToMidi2DefaultTranslator.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\midi\ump\juce_UMPProtocols.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\midi\ump\juce_UMPReceiver.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\midi\ump\juce_UMPSysEx7.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\midi\ump\juce_UMPUtils.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\midi\ump\juce_UMPView.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\midi\juce_MidiBuffer.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\midi\juce_MidiDataConcatenator.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\midi\juce_MidiFile.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\midi\juce_MidiKeyboardState.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\midi\juce_MidiMessage.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\midi\juce_MidiMessageSequence.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\midi\juce_MidiRPN.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\mpe\juce_MPEInstrument.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\mpe\juce_MPEMessages.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\mpe\juce_MPENote.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\mpe\juce_MPESynthesiser.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\mpe\juce_MPESynthesiserBase.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\mpe\juce_MPESynthesiserVoice.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\mpe\juce_MPEUtils.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\mpe\juce_MPEValue.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\mpe\juce_MPEZoneLayout.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\native\juce_AudioWorkgroup_mac.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\native\juce_CoreAudioLayouts_mac.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\native\juce_CoreAudioTimeConversions_mac.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\sources\juce_AudioSource.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\sources\juce_BufferingAudioSource.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\sources\juce_ChannelRemappingAudioSource.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\sources\juce_IIRFilterAudioSource.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\sources\juce_MemoryAudioSource.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\sources\juce_MixerAudioSource.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\sources\juce_PositionableAudioSource.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\sources\juce_ResamplingAudioSource.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\sources\juce_ReverbAudioSource.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\sources\juce_ToneGeneratorAudioSource.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\synthesisers\juce_Synthesiser.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\utilities\juce_ADSR.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\utilities\juce_AudioWorkgroup.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\utilities\juce_Decibels.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\utilities\juce_GenericInterpolator.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\utilities\juce_IIRFilter.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\utilities\juce_Interpolators.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\utilities\juce_Reverb.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\utilities\juce_SmoothedValue.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_basics\juce_audio_basics.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\audio_io\juce_AudioDeviceManager.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\audio_io\juce_AudioIODevice.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\audio_io\juce_AudioIODeviceType.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\audio_io\juce_SystemAudioVolume.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\midi_io\ump\juce_UMPBytestreamInputHandler.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\midi_io\ump\juce_UMPU32InputHandler.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\midi_io\juce_MidiDevices.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\midi_io\juce_MidiMessageCollector.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\include\oboe\AudioStream.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\include\oboe\AudioStreamBase.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\include\oboe\AudioStreamBuilder.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\include\oboe\AudioStreamCallback.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\include\oboe\Definitions.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\include\oboe\FifoBuffer.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\include\oboe\FifoControllerBase.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\include\oboe\FullDuplexStream.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\include\oboe\LatencyTuner.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\include\oboe\Oboe.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\include\oboe\OboeExtensions.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\include\oboe\ResultWithValue.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\include\oboe\StabilizedCallback.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\include\oboe\Utilities.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\include\oboe\Version.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\aaudio\AAudioExtensions.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\aaudio\AAudioLoader.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\aaudio\AudioStreamAAudio.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\common\AdpfWrapper.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\common\AudioClock.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\common\AudioSourceCaller.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\common\DataConversionFlowGraph.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\common\FilterAudioStream.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\common\FixedBlockAdapter.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\common\FixedBlockReader.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\common\FixedBlockWriter.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\common\MonotonicCounter.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\common\OboeDebug.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\common\QuirksManager.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\common\SourceFloatCaller.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\common\SourceI16Caller.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\common\SourceI24Caller.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\common\SourceI32Caller.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\common\Trace.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\fifo\FifoController.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\fifo\FifoControllerIndirect.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\flowgraph\resampler\HyperbolicCosineWindow.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\flowgraph\resampler\IntegerRatio.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\flowgraph\resampler\KaiserWindow.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\flowgraph\resampler\LinearResampler.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\flowgraph\resampler\MultiChannelResampler.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\flowgraph\resampler\PolyphaseResampler.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\flowgraph\resampler\PolyphaseResamplerMono.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\flowgraph\resampler\PolyphaseResamplerStereo.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\flowgraph\resampler\ResamplerDefinitions.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\flowgraph\resampler\SincResampler.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\flowgraph\resampler\SincResamplerStereo.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\flowgraph\ChannelCountConverter.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\flowgraph\ClipToRange.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\flowgraph\FlowGraphNode.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\flowgraph\FlowgraphUtilities.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\flowgraph\Limiter.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\flowgraph\ManyToMultiConverter.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\flowgraph\MonoBlend.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\flowgraph\MonoToMultiConverter.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\flowgraph\MultiToManyConverter.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\flowgraph\MultiToMonoConverter.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\flowgraph\RampLinear.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\flowgraph\SampleRateConverter.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\flowgraph\SinkFloat.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\flowgraph\SinkI8_24.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\flowgraph\SinkI16.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\flowgraph\SinkI24.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\flowgraph\SinkI32.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\flowgraph\SourceFloat.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\flowgraph\SourceI8_24.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\flowgraph\SourceI16.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\flowgraph\SourceI24.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\flowgraph\SourceI32.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\opensles\AudioInputStreamOpenSLES.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\opensles\AudioOutputStreamOpenSLES.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\opensles\AudioStreamBuffered.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\opensles\AudioStreamOpenSLES.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\opensles\EngineOpenSLES.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\opensles\OpenSLESUtilities.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\opensles\OutputMixerOpenSLES.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\juce_Audio_ios.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\juce_HighPerformanceAudioHelpers_android.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\sources\juce_AudioSourcePlayer.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\sources\juce_AudioTransportSource.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\juce_audio_devices.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\flac\libFLAC\include\private\bitmath.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\flac\libFLAC\include\private\bitreader.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\flac\libFLAC\include\private\bitwriter.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\flac\libFLAC\include\private\cpu.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\flac\libFLAC\include\private\crc.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\flac\libFLAC\include\private\fixed.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\flac\libFLAC\include\private\float.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\flac\libFLAC\include\private\format.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\flac\libFLAC\include\private\lpc.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\flac\libFLAC\include\private\md5.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\flac\libFLAC\include\private\memory.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\flac\libFLAC\include\private\stream_encoder.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\flac\libFLAC\include\private\stream_encoder_framing.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\flac\libFLAC\include\private\window.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\flac\libFLAC\include\protected\stream_decoder.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\flac\libFLAC\include\protected\stream_encoder.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\flac\all.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\flac\alloc.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\flac\assert.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\flac\callback.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\flac\compat.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\flac\endswap.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\flac\export.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\flac\format.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\flac\metadata.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\flac\ordinals.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\flac\private.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\flac\stream_decoder.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\flac\stream_encoder.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\books\coupled\res_books_51.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\books\coupled\res_books_stereo.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\books\floor\floor_books.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\books\uncoupled\res_books_uncoupled.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\modes\floor_all.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\modes\psych_8.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\modes\psych_11.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\modes\psych_16.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\modes\psych_44.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\modes\residue_8.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\modes\residue_16.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\modes\residue_44.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\modes\residue_44p51.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\modes\residue_44u.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\modes\setup_8.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\modes\setup_11.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\modes\setup_16.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\modes\setup_22.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\modes\setup_32.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\modes\setup_44.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\modes\setup_44p51.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\modes\setup_44u.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\modes\setup_X.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\backends.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\bitrate.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\codebook.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\codec_internal.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\envelope.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\highlevel.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\lookup.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\lookup_data.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\lpc.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\lsp.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\masking.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\mdct.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\misc.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\os.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\psy.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\registry.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\scales.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\smallft.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\window.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\oggvorbis\codec.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\oggvorbis\config_types.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\oggvorbis\crctable.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\oggvorbis\ogg.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\oggvorbis\os_types.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\oggvorbis\vorbisenc.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\oggvorbis\vorbisfile.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\juce_AiffAudioFormat.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\juce_CoreAudioFormat.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\juce_FlacAudioFormat.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\juce_LAMEEncoderAudioFormat.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\juce_MP3AudioFormat.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\juce_OggVorbisAudioFormat.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\juce_WavAudioFormat.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\juce_WindowsMediaAudioFormat.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\format\juce_ARAAudioReaders.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\format\juce_AudioFormat.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\format\juce_AudioFormatManager.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\format\juce_AudioFormatReader.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\format\juce_AudioFormatReaderSource.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\format\juce_AudioFormatWriter.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\format\juce_AudioSubsectionReader.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\format\juce_BufferingAudioFormatReader.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\format\juce_MemoryMappedAudioFormatReader.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\sampler\juce_Sampler.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\juce_audio_formats.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_plugin_client\detail\juce_CheckSettingMacros.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_plugin_client\detail\juce_CreatePluginFilter.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_plugin_client\detail\juce_IncludeModuleHeaders.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_plugin_client\detail\juce_IncludeSystemHeaders.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_plugin_client\detail\juce_LinuxMessageThread.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_plugin_client\detail\juce_PluginUtilities.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_plugin_client\detail\juce_VSTWindowUtilities.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_plugin_client\juce_audio_plugin_client.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format\juce_AudioPluginFormat.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format\juce_AudioPluginFormatManager.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\lilv\lilv\lilv.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\lilv\lilv\lilvmm.hpp"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\lilv\src\zix\common.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\lilv\src\zix\tree.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\lilv\src\filesystem.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\lilv\src\lilv_internal.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\atom\atom.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\atom\forge.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\atom\util.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\buf-size\buf-size.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\core\attributes.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\core\lv2.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\core\lv2_util.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\data-access\data-access.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\dynmanifest\dynmanifest.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\event\event-helpers.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\event\event.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\instance-access\instance-access.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\log\log.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\log\logger.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\midi\midi.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\morph\morph.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\options\options.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\parameters\parameters.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\patch\patch.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\port-groups\port-groups.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\port-props\port-props.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\presets\presets.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\resize-port\resize-port.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\state\state.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\time\time.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\ui\ui.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\units\units.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\uri-map\uri-map.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\urid\urid.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\worker\worker.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\serd\serd\serd.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\serd\src\attributes.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\serd\src\base64.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\serd\src\byte_sink.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\serd\src\byte_source.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\serd\src\node.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\serd\src\reader.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\serd\src\serd_config.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\serd\src\serd_internal.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\serd\src\stack.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\serd\src\string_utils.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\serd\src\system.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\serd\src\uri_utils.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\sord\sord\sord.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\sord\sord\sordmm.hpp"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\sord\src\zix\btree.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\sord\src\zix\common.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\sord\src\zix\digest.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\sord\src\zix\hash.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\sord\src\sord_config.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\sord\src\sord_internal.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\sratom\sratom\sratom.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\juce_lv2_config.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\lilv_config.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\serd_config.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\sord_config.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\pslextensions\ipslcontextinfo.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\pslextensions\ipsleditcontroller.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\pslextensions\ipslgainreduction.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\pslextensions\ipslhostcommands.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\pslextensions\ipslviewembedding.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\pslextensions\ipslviewscaling.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\pslextensions\pslauextensions.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\pslextensions\pslvst2extensions.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\base\source\classfactoryhelpers.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\base\source\fbuffer.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\base\source\fcommandline.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\base\source\fdebug.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\base\source\fobject.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\base\source\fstreamer.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\base\source\fstring.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\base\source\updatehandler.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\base\thread\include\flock.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\base\conststringtable.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\base\falignpop.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\base\falignpush.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\base\fplatform.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\base\fstrdefs.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\base\ftypes.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\base\funknown.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\base\funknownimpl.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\base\futils.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\base\fvariant.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\base\ibstream.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\base\icloneable.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\base\ipersistent.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\base\ipluginbase.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\base\iplugincompatibility.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\base\istringresult.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\base\iupdatehandler.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\base\smartpointer.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\base\typesizecheck.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\base\ustring.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\gui\iplugview.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\gui\iplugviewcontentscalesupport.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst\ivstattributes.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst\ivstaudioprocessor.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst\ivstautomationstate.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst\ivstchannelcontextinfo.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst\ivstcomponent.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst\ivstcontextmenu.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst\ivstdataexchange.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst\ivsteditcontroller.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst\ivstevents.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst\ivsthostapplication.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst\ivstinterappaudio.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst\ivstmessage.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst\ivstmidicontrollers.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst\ivstmidilearn.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst\ivstnoteexpression.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst\ivstparameterchanges.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst\ivstparameterfunctionname.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst\ivstphysicalui.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst\ivstpluginterfacesupport.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst\ivstplugview.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst\ivstprefetchablesupport.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst\ivstprocesscontext.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst\ivstremapparamid.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst\ivstrepresentation.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst\ivsttestplugprovider.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst\ivstunits.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst\vstpshpack4.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst\vstspeaker.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst\vsttypes.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\common\commonstringconvert.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\common\memorystream.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\common\pluginview.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\common\readfile.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\hosting\hostclasses.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\hosting\module.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\hosting\pluginterfacesupport.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\moduleinfo\json.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\moduleinfo\jsoncxx.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\moduleinfo\moduleinfo.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\moduleinfo\moduleinfocreator.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\moduleinfo\moduleinfoparser.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\utility\optional.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\utility\stringconvert.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\utility\uid.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\utility\vst2persistence.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\vstbus.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\vstcomponent.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\vstcomponentbase.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\vsteditcontroller.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\vstparameters.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\vstpresetfile.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\juce_ARACommon.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\juce_ARAHosting.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\juce_AU_Shared.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\juce_AudioUnitPluginFormat.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\juce_LADSPAPluginFormat.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\juce_LV2Common.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\juce_LV2PluginFormat.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\juce_LV2Resources.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\juce_VST3Common.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\juce_VST3Headers.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\juce_VST3PluginFormat.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\juce_VSTCommon.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\juce_VSTMidiEventList.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\juce_VSTPluginFormat.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\processors\juce_AudioPluginInstance.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\processors\juce_AudioProcessor.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\processors\juce_AudioProcessorEditor.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\processors\juce_AudioProcessorEditorHostContext.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\processors\juce_AudioProcessorGraph.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\processors\juce_AudioProcessorListener.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\processors\juce_AudioProcessorParameter.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\processors\juce_AudioProcessorParameterGroup.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\processors\juce_GenericAudioProcessorEditor.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\processors\juce_HostedAudioProcessorParameter.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\processors\juce_PluginDescription.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\scanning\juce_KnownPluginList.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\scanning\juce_PluginDirectoryScanner.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\scanning\juce_PluginListComponent.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\utilities\ARA\juce_ARA_utils.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\utilities\ARA\juce_ARADebug.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\utilities\ARA\juce_ARADocumentController.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\utilities\ARA\juce_ARAModelObjects.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\utilities\ARA\juce_ARAPlugInInstanceRoles.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\utilities\ARA\juce_AudioProcessor_ARAExtensions.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\utilities\juce_AAXClientExtensions.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\utilities\juce_AudioParameterBool.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\utilities\juce_AudioParameterChoice.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\utilities\juce_AudioParameterFloat.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\utilities\juce_AudioParameterInt.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\utilities\juce_AudioProcessorParameterWithID.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\utilities\juce_AudioProcessorValueTreeState.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\utilities\juce_ExtensionsVisitor.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\utilities\juce_FlagCache.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\utilities\juce_ParameterAttachments.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\utilities\juce_PluginHostType.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\utilities\juce_RangedAudioParameter.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\utilities\juce_VST2ClientExtensions.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\utilities\juce_VST3ClientExtensions.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\juce_audio_processors.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_utils\audio_cd\juce_AudioCDBurner.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_utils\audio_cd\juce_AudioCDReader.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_utils\gui\juce_AudioAppComponent.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_utils\gui\juce_AudioDeviceSelectorComponent.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_utils\gui\juce_AudioThumbnail.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_utils\gui\juce_AudioThumbnailBase.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_utils\gui\juce_AudioThumbnailCache.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_utils\gui\juce_AudioVisualiserComponent.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_utils\gui\juce_BluetoothMidiDevicePairingDialogue.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_utils\gui\juce_KeyboardComponentBase.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_utils\gui\juce_MidiKeyboardComponent.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_utils\gui\juce_MPEKeyboardComponent.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_utils\players\juce_AudioProcessorPlayer.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_utils\players\juce_SoundPlayer.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_audio_utils\juce_audio_utils.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\containers\juce_AbstractFifo.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\containers\juce_Array.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\containers\juce_ArrayAllocationBase.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\containers\juce_ArrayBase.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\containers\juce_DynamicObject.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\containers\juce_ElementComparator.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\containers\juce_Enumerate.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\containers\juce_FixedSizeFunction.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\containers\juce_HashMap.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\containers\juce_LinkedListPointer.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\containers\juce_ListenerList.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\containers\juce_NamedValueSet.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\containers\juce_Optional.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\containers\juce_OwnedArray.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\containers\juce_PropertySet.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\containers\juce_ReferenceCountedArray.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\containers\juce_ScopedValueSetter.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\containers\juce_SingleThreadedAbstractFifo.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\containers\juce_SortedSet.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\containers\juce_Span.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\containers\juce_SparseSet.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\containers\juce_Variant.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\detail\juce_CallbackListenerList.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\detail\juce_NativeFileHandle.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\files\juce_AndroidDocument.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\files\juce_common_MimeTypes.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\files\juce_DirectoryIterator.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\files\juce_File.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\files\juce_FileFilter.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\files\juce_FileInputStream.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\files\juce_FileOutputStream.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\files\juce_FileSearchPath.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\files\juce_MemoryMappedFile.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\files\juce_RangedDirectoryIterator.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\files\juce_TemporaryFile.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\files\juce_WildcardFileFilter.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\json\juce_JSON.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\json\juce_JSONSerialisation.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\json\juce_JSONUtils.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\logging\juce_FileLogger.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\logging\juce_Logger.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\maths\juce_BigInteger.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\maths\juce_Expression.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\maths\juce_MathsFunctions.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\maths\juce_NormalisableRange.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\maths\juce_Random.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\maths\juce_Range.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\maths\juce_StatisticsAccumulator.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\memory\juce_AllocationHooks.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\memory\juce_Atomic.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\memory\juce_ByteOrder.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\memory\juce_ContainerDeletePolicy.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\memory\juce_CopyableHeapBlock.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\memory\juce_HeapBlock.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\memory\juce_HeavyweightLeakedObjectDetector.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\memory\juce_LeakedObjectDetector.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\memory\juce_Memory.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\memory\juce_MemoryBlock.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\memory\juce_OptionalScopedPointer.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\memory\juce_ReferenceCountedObject.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\memory\juce_Reservoir.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\memory\juce_ScopedPointer.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\memory\juce_SharedResourcePointer.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\memory\juce_Singleton.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\memory\juce_WeakReference.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\misc\juce_ConsoleApplication.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\misc\juce_EnumHelpers.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\misc\juce_Functional.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\misc\juce_OptionsHelpers.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\misc\juce_Result.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\misc\juce_RuntimePermissions.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\misc\juce_ScopeGuard.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\misc\juce_Uuid.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\misc\juce_WindowsRegistry.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\native\juce_BasicNativeHeaders.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\native\juce_CFHelpers_mac.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\native\juce_ComSmartPtr_windows.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\native\juce_IPAddress_posix.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\native\juce_JNIHelpers_android.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\native\juce_ObjCHelpers_mac.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\native\juce_PlatformTimerListener.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\native\juce_SharedCode_intel.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\native\juce_SharedCode_posix.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\native\juce_ThreadPriorities_native.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\network\juce_IPAddress.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\network\juce_MACAddress.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\network\juce_NamedPipe.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\network\juce_Socket.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\network\juce_URL.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\network\juce_WebInputStream.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\serialisation\juce_Serialisation.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\streams\juce_AndroidDocumentInputSource.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\streams\juce_BufferedInputStream.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\streams\juce_FileInputSource.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\streams\juce_InputSource.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\streams\juce_InputStream.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\streams\juce_MemoryInputStream.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\streams\juce_MemoryOutputStream.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\streams\juce_OutputStream.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\streams\juce_SubregionStream.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\streams\juce_URLInputSource.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\system\juce_CompilerSupport.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\system\juce_CompilerWarnings.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\system\juce_PlatformDefs.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\system\juce_StandardHeader.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\system\juce_SystemStats.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\system\juce_TargetPlatform.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\text\juce_Base64.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\text\juce_CharacterFunctions.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\text\juce_CharPointer_ASCII.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\text\juce_CharPointer_UTF8.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\text\juce_CharPointer_UTF16.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\text\juce_CharPointer_UTF32.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\text\juce_Identifier.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\text\juce_LocalisedStrings.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\text\juce_NewLine.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\text\juce_String.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\text\juce_StringArray.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\text\juce_StringPairArray.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\text\juce_StringPool.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\text\juce_StringRef.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\text\juce_TextDiff.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\threads\juce_ChildProcess.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\threads\juce_CriticalSection.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\threads\juce_DynamicLibrary.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\threads\juce_HighResolutionTimer.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\threads\juce_InterProcessLock.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\threads\juce_Process.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\threads\juce_ReadWriteLock.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\threads\juce_ScopedLock.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\threads\juce_ScopedReadLock.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\threads\juce_ScopedWriteLock.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\threads\juce_SpinLock.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\threads\juce_Thread.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\threads\juce_ThreadLocalValue.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\threads\juce_ThreadPool.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\threads\juce_TimeSliceThread.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\threads\juce_WaitableEvent.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\time\juce_PerformanceCounter.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\time\juce_RelativeTime.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\time\juce_Time.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\unit_tests\juce_UnitTest.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\unit_tests\juce_UnitTestCategories.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\xml\juce_XmlDocument.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\xml\juce_XmlElement.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\zip\zlib\crc32.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\zip\zlib\deflate.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\zip\zlib\gzguts.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\zip\zlib\inffast.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\zip\zlib\inffixed.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\zip\zlib\inflate.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\zip\zlib\inftrees.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\zip\zlib\trees.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\zip\zlib\zconf.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\zip\zlib\zlib.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\zip\zlib\zutil.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\zip\juce_GZIPCompressorOutputStream.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\zip\juce_GZIPDecompressorInputStream.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\zip\juce_ZipFile.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\zip\juce_zlib.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_core\juce_core.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_data_structures\app_properties\juce_ApplicationProperties.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_data_structures\app_properties\juce_PropertiesFile.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_data_structures\undomanager\juce_UndoableAction.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_data_structures\undomanager\juce_UndoManager.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_data_structures\values\juce_CachedValue.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_data_structures\values\juce_Value.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_data_structures\values\juce_ValueTree.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_data_structures\values\juce_ValueTreePropertyWithDefault.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_data_structures\values\juce_ValueTreeSynchroniser.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_data_structures\juce_data_structures.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_dsp\containers\juce_AudioBlock.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_dsp\containers\juce_SIMDRegister.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_dsp\containers\juce_SIMDRegister_Impl.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_dsp\filter_design\juce_FilterDesign.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_dsp\frequency\juce_Convolution.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_dsp\frequency\juce_FFT.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_dsp\frequency\juce_Windowing.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_dsp\maths\juce_FastMathApproximations.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_dsp\maths\juce_LogRampedValue.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_dsp\maths\juce_LookupTable.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_dsp\maths\juce_Matrix.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_dsp\maths\juce_Phase.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_dsp\maths\juce_Polynomial.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_dsp\maths\juce_SpecialFunctions.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_dsp\native\juce_SIMDNativeOps_avx.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_dsp\native\juce_SIMDNativeOps_fallback.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_dsp\native\juce_SIMDNativeOps_neon.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_dsp\native\juce_SIMDNativeOps_sse.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_dsp\processors\juce_BallisticsFilter.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_dsp\processors\juce_DelayLine.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_dsp\processors\juce_DryWetMixer.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_dsp\processors\juce_FIRFilter.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_dsp\processors\juce_FirstOrderTPTFilter.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_dsp\processors\juce_IIRFilter.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_dsp\processors\juce_IIRFilter_Impl.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_dsp\processors\juce_LinkwitzRileyFilter.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_dsp\processors\juce_Oversampling.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_dsp\processors\juce_Panner.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_dsp\processors\juce_ProcessContext.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_dsp\processors\juce_ProcessorChain.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_dsp\processors\juce_ProcessorDuplicator.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_dsp\processors\juce_ProcessorWrapper.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_dsp\processors\juce_StateVariableFilter.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_dsp\processors\juce_StateVariableTPTFilter.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_dsp\widgets\juce_Bias.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_dsp\widgets\juce_Chorus.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_dsp\widgets\juce_Compressor.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_dsp\widgets\juce_Gain.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_dsp\widgets\juce_LadderFilter.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_dsp\widgets\juce_Limiter.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_dsp\widgets\juce_NoiseGate.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_dsp\widgets\juce_Oscillator.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_dsp\widgets\juce_Phaser.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_dsp\widgets\juce_Reverb.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_dsp\widgets\juce_WaveShaper.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_dsp\juce_dsp.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_events\broadcasters\juce_ActionBroadcaster.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_events\broadcasters\juce_ActionListener.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_events\broadcasters\juce_AsyncUpdater.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_events\broadcasters\juce_ChangeBroadcaster.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_events\broadcasters\juce_ChangeListener.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_events\broadcasters\juce_LockingAsyncUpdater.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_events\interprocess\juce_ChildProcessManager.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_events\interprocess\juce_ConnectedChildProcess.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_events\interprocess\juce_InterprocessConnection.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_events\interprocess\juce_InterprocessConnectionServer.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_events\interprocess\juce_NetworkServiceDiscovery.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_events\messages\juce_ApplicationBase.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_events\messages\juce_CallbackMessage.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_events\messages\juce_DeletedAtShutdown.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_events\messages\juce_Initialisation.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_events\messages\juce_Message.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_events\messages\juce_MessageListener.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_events\messages\juce_MessageManager.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_events\messages\juce_MountedVolumeListChangeDetector.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_events\messages\juce_NotificationType.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_events\native\juce_EventLoop_linux.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_events\native\juce_EventLoopInternal_linux.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_events\native\juce_HiddenMessageWindow_windows.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_events\native\juce_MessageQueue_mac.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_events\native\juce_RunningInUnity.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_events\native\juce_ScopedLowPowerModeDisabler.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_events\native\juce_WinRTWrapper_windows.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_events\timers\juce_MultiTimer.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_events\timers\juce_TimedCallback.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_events\timers\juce_Timer.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_events\juce_events.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\colour\juce_Colour.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\colour\juce_ColourGradient.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\colour\juce_Colours.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\colour\juce_FillType.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\colour\juce_PixelFormats.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\contexts\juce_GraphicsContext.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\contexts\juce_LowLevelGraphicsContext.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\contexts\juce_LowLevelGraphicsSoftwareRenderer.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\detail\juce_JustifiedText.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\detail\juce_Ranges.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\detail\juce_ShapedText.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\detail\juce_SimpleShapedText.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\detail\juce_Unicode.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\effects\juce_DropShadowEffect.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\effects\juce_GlowEffect.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\effects\juce_ImageEffectFilter.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\OT\Color\CBDT\CBDT.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\OT\Color\COLR\COLR.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\OT\Color\COLR\colrv1-closure.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\OT\Color\CPAL\CPAL.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\OT\Color\sbix\sbix.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\OT\Color\svg\svg.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\OT\glyf\composite-iter.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\OT\glyf\CompositeGlyph.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\OT\glyf\glyf-helpers.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\OT\glyf\glyf.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\OT\glyf\Glyph.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\OT\glyf\GlyphHeader.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\OT\glyf\loca.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\OT\glyf\path-builder.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\OT\glyf\SimpleGlyph.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\OT\glyf\SubsetGlyph.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\OT\Layout\Common\Coverage.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\OT\Layout\Common\CoverageFormat1.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\OT\Layout\Common\CoverageFormat2.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\OT\Layout\Common\RangeRecord.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GDEF\GDEF.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\Anchor.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\AnchorFormat1.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\AnchorFormat2.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\AnchorFormat3.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\AnchorMatrix.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\ChainContextPos.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\Common.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\ContextPos.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\CursivePos.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\CursivePosFormat1.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\ExtensionPos.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\GPOS.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\LigatureArray.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\MarkArray.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\MarkBasePos.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\MarkBasePosFormat1.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\MarkLigPos.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\MarkLigPosFormat1.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\MarkMarkPos.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\MarkMarkPosFormat1.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\MarkRecord.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\PairPos.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\PairPosFormat1.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\PairPosFormat2.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\PairSet.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\PairValueRecord.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\PosLookup.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\PosLookupSubTable.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\SinglePos.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\SinglePosFormat1.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\SinglePosFormat2.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\ValueFormat.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB\AlternateSet.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB\AlternateSubst.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB\AlternateSubstFormat1.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB\ChainContextSubst.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB\Common.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB\ContextSubst.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB\ExtensionSubst.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB\GSUB.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB\Ligature.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB\LigatureSet.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB\LigatureSubst.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB\LigatureSubstFormat1.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB\MultipleSubst.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB\MultipleSubstFormat1.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB\ReverseChainSingleSubst.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB\ReverseChainSingleSubstFormat1.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB\Sequence.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB\SingleSubst.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB\SingleSubstFormat1.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB\SingleSubstFormat2.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB\SubstLookup.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB\SubstLookupSubTable.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\OT\Layout\types.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\OT\name\name.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\OT\Var\VARC\coord-setter.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\OT\Var\VARC\VARC.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-aat-layout-ankr-table.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-aat-layout-bsln-table.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-aat-layout-common.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-aat-layout-feat-table.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-aat-layout-just-table.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-aat-layout-kerx-table.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-aat-layout-morx-table.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-aat-layout-opbd-table.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-aat-layout-trak-table.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-aat-layout.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-aat-layout.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-aat-ltag-table.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-aat-map.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-aat.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-algs.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-array.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-atomic.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-bimap.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-bit-page.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-bit-set-invertible.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-bit-set.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-blob.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-blob.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-buffer-deserialize-json.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-buffer-deserialize-text-glyphs.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-buffer-deserialize-text-unicode.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-buffer.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-buffer.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-cache.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-cff-interp-common.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-cff-interp-cs-common.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-cff-interp-dict-common.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-cff1-interp-cs.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-cff2-interp-cs.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-common.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-config.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-coretext.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-cplusplus.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-debug.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-deprecated.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-directwrite.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-dispatch.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-draw.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-draw.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-face.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-face.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-font.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-font.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ft-colr.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ft.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-gdi.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-geometry.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-glib.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-gobject-structs.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-gobject.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-graphite2.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-icu.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-iter.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-kern.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-limits.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-machinery.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-map.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-map.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-meta.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ms-feature-ranges.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-multimap.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-mutex.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-null.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-number-parser.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-number.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-object.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-open-file.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-open-type.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-cff-common.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-cff1-std-str.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-cff1-table.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-cff2-table.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-cmap-table.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-color.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-deprecated.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-face-table-list.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-face.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-font.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-gasp-table.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-glyf-table.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-hdmx-table.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-head-table.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-hhea-table.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-hmtx-table.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-kern-table.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-layout-base-table.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-layout-common.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-layout-gdef-table.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-layout-gpos-table.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-layout-gsub-table.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-layout-gsubgpos.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-layout-jstf-table.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-layout.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-layout.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-map.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-math-table.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-math.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-maxp-table.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-meta-table.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-meta.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-metrics.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-metrics.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-name-language-static.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-name-language.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-name-table.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-name.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-os2-table.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-os2-unicode-ranges.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-post-macroman.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-post-table-v2subset.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-post-table.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-shape-fallback.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-shape-normalize.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-shape.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-shape.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper-arabic-fallback.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper-arabic-joining-list.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper-arabic-pua.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper-arabic-table.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper-arabic-win1256.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper-arabic.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper-indic-machine.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper-indic.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper-khmer-machine.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper-myanmar-machine.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper-syllabic.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper-use-machine.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper-use-table.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper-vowel-constraints.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-stat-table.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-tag-table.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-var-avar-table.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-var-common.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-var-cvar-table.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-var-fvar-table.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-var-gvar-table.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-var-hvar-table.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-var-mvar-table.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-var-varc-table.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-var.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-vorg-table.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-outline.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-paint-extents.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-paint.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-paint.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-pool.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-priority-queue.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-repacker.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-sanitize.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-serialize.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-set-digest.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-set.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-set.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-shape-plan.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-shape-plan.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-shape.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-shaper-impl.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-shaper-list.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-shaper.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-string-array.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-style.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-subset-accelerator.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-subset-cff-common.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-subset-input.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-subset-instancer-iup.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-subset-instancer-solver.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-subset-plan-member-list.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-subset-plan.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-subset-repacker.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-subset.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-subset.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ucd-table.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-unicode-emoji-table.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-unicode.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-unicode.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-uniscribe.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-utf.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-vector.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-version.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-wasm-api-blob.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-wasm-api-buffer.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-wasm-api-common.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-wasm-api-face.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-wasm-api-font.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-wasm-api-list.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-wasm-api-shape.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-wasm-api.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-wasm-api.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\harfbuzz\hb.hh"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\juce_AttributedString.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\juce_Font.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\juce_FontOptions.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\juce_FunctionPointerDestructor.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\juce_GlyphArrangement.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\juce_LruCache.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\juce_TextLayout.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\juce_Typeface.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\fonts\juce_TypefaceFileCache.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\geometry\juce_AffineTransform.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\geometry\juce_BorderSize.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\geometry\juce_EdgeTable.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\geometry\juce_Line.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\geometry\juce_Parallelogram.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\geometry\juce_Path.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\geometry\juce_PathIterator.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\geometry\juce_PathStrokeType.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\geometry\juce_Point.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\geometry\juce_Rectangle.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\geometry\juce_RectangleList.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\image_formats\jpglib\cderror.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\image_formats\jpglib\jchuff.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\image_formats\jpglib\jconfig.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\image_formats\jpglib\jdct.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\image_formats\jpglib\jdhuff.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\image_formats\jpglib\jerror.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\image_formats\jpglib\jinclude.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\image_formats\jpglib\jmemsys.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\image_formats\jpglib\jmorecfg.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\image_formats\jpglib\jpegint.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\image_formats\jpglib\jpeglib.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\image_formats\jpglib\jversion.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\image_formats\jpglib\transupp.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\image_formats\pnglib\png.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\image_formats\pnglib\pngconf.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\image_formats\pnglib\pngdebug.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\image_formats\pnglib\pnginfo.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\image_formats\pnglib\pngpriv.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\image_formats\pnglib\pngstruct.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\images\juce_Image.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\images\juce_ImageCache.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\images\juce_ImageConvolutionKernel.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\images\juce_ImageFileFormat.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\images\juce_ScaledImage.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\native\juce_CoreGraphicsContext_mac.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\native\juce_CoreGraphicsHelpers_mac.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\native\juce_Direct2DGraphicsContext_windows.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\native\juce_Direct2DHwndContext_windows.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\native\juce_Direct2DImage_windows.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\native\juce_Direct2DImageContext_windows.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\native\juce_Direct2DMetrics_windows.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\native\juce_Direct2DPixelDataPage_windows.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\native\juce_DirectX_windows.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\native\juce_EventTracing.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\native\juce_RenderingHelpers.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\placement\juce_Justification.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\placement\juce_RectanglePlacement.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\unicode\sheenbidi\Headers\SBAlgorithm.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\unicode\sheenbidi\Headers\SBBase.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\unicode\sheenbidi\Headers\SBBidiType.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\unicode\sheenbidi\Headers\SBCodepoint.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\unicode\sheenbidi\Headers\SBCodepointSequence.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\unicode\sheenbidi\Headers\SBConfig.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\unicode\sheenbidi\Headers\SBGeneralCategory.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\unicode\sheenbidi\Headers\SBLine.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\unicode\sheenbidi\Headers\SBMirrorLocator.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\unicode\sheenbidi\Headers\SBParagraph.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\unicode\sheenbidi\Headers\SBRun.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\unicode\sheenbidi\Headers\SBScript.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\unicode\sheenbidi\Headers\SBScriptLocator.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\unicode\sheenbidi\Headers\SheenBidi.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\unicode\sheenbidi\Source\BidiChain.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\unicode\sheenbidi\Source\BidiTypeLookup.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\unicode\sheenbidi\Source\BracketQueue.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\unicode\sheenbidi\Source\BracketType.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\unicode\sheenbidi\Source\GeneralCategoryLookup.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\unicode\sheenbidi\Source\IsolatingRun.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\unicode\sheenbidi\Source\LevelRun.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\unicode\sheenbidi\Source\PairingLookup.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\unicode\sheenbidi\Source\RunExtrema.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\unicode\sheenbidi\Source\RunKind.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\unicode\sheenbidi\Source\RunQueue.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\unicode\sheenbidi\Source\SBAlgorithm.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\unicode\sheenbidi\Source\SBAssert.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\unicode\sheenbidi\Source\SBBase.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\unicode\sheenbidi\Source\SBCodepointSequence.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\unicode\sheenbidi\Source\SBLine.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\unicode\sheenbidi\Source\SBLog.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\unicode\sheenbidi\Source\SBMirrorLocator.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\unicode\sheenbidi\Source\SBParagraph.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\unicode\sheenbidi\Source\SBScriptLocator.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\unicode\sheenbidi\Source\ScriptLookup.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\unicode\sheenbidi\Source\ScriptStack.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\unicode\sheenbidi\Source\StatusStack.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_graphics\juce_graphics.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\accessibility\enums\juce_AccessibilityActions.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\accessibility\enums\juce_AccessibilityEvent.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\accessibility\enums\juce_AccessibilityRole.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\accessibility\interfaces\juce_AccessibilityCellInterface.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\accessibility\interfaces\juce_AccessibilityTableInterface.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\accessibility\interfaces\juce_AccessibilityTextInterface.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\accessibility\interfaces\juce_AccessibilityValueInterface.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\accessibility\juce_AccessibilityHandler.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\accessibility\juce_AccessibilityState.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\application\juce_Application.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\buttons\juce_ArrowButton.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\buttons\juce_Button.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\buttons\juce_DrawableButton.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\buttons\juce_HyperlinkButton.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\buttons\juce_ImageButton.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\buttons\juce_ShapeButton.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\buttons\juce_TextButton.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\buttons\juce_ToggleButton.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\buttons\juce_ToolbarButton.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\commands\juce_ApplicationCommandID.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\commands\juce_ApplicationCommandInfo.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\commands\juce_ApplicationCommandManager.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\commands\juce_ApplicationCommandTarget.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\commands\juce_KeyPressMappingSet.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\components\juce_CachedComponentImage.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\components\juce_Component.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\components\juce_ComponentListener.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\components\juce_ComponentTraverser.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\components\juce_FocusTraverser.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\components\juce_ModalComponentManager.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\desktop\juce_Desktop.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\desktop\juce_Displays.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\detail\juce_AccessibilityHelpers.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\detail\juce_AlertWindowHelpers.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\detail\juce_ButtonAccessibilityHandler.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\detail\juce_ComponentHelpers.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\detail\juce_ComponentPeerHelpers.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\detail\juce_CustomMouseCursorInfo.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\detail\juce_FocusHelpers.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\detail\juce_FocusRestorer.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\detail\juce_LookAndFeelHelpers.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\detail\juce_MouseInputSourceImpl.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\detail\juce_MouseInputSourceList.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\detail\juce_PointerState.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\detail\juce_ScalingHelpers.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\detail\juce_ScopedContentSharerImpl.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\detail\juce_ScopedContentSharerInterface.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\detail\juce_ScopedMessageBoxImpl.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\detail\juce_ScopedMessageBoxInterface.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\detail\juce_StandardCachedComponentImage.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\detail\juce_ToolbarItemDragAndDropOverlayComponent.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\detail\juce_TopLevelWindowManager.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\detail\juce_ViewportHelpers.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\detail\juce_WindowingHelpers.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\drawables\juce_Drawable.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\drawables\juce_DrawableComposite.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\drawables\juce_DrawableImage.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\drawables\juce_DrawablePath.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\drawables\juce_DrawableRectangle.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\drawables\juce_DrawableShape.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\drawables\juce_DrawableText.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\filebrowser\juce_ContentSharer.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\filebrowser\juce_DirectoryContentsDisplayComponent.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\filebrowser\juce_DirectoryContentsList.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\filebrowser\juce_FileBrowserComponent.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\filebrowser\juce_FileBrowserListener.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\filebrowser\juce_FileChooser.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\filebrowser\juce_FileChooserDialogBox.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\filebrowser\juce_FileListComponent.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\filebrowser\juce_FilenameComponent.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\filebrowser\juce_FilePreviewComponent.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\filebrowser\juce_FileSearchPathListComponent.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\filebrowser\juce_FileTreeComponent.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\filebrowser\juce_ImagePreviewComponent.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\keyboard\juce_CaretComponent.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\keyboard\juce_KeyboardFocusTraverser.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\keyboard\juce_KeyListener.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\keyboard\juce_KeyPress.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\keyboard\juce_ModifierKeys.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\keyboard\juce_SystemClipboard.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\keyboard\juce_TextEditorKeyMapper.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\keyboard\juce_TextInputTarget.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\layout\juce_AnimatedPosition.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\layout\juce_AnimatedPositionBehaviours.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\layout\juce_BorderedComponentBoundsConstrainer.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\layout\juce_ComponentAnimator.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\layout\juce_ComponentBoundsConstrainer.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\layout\juce_ComponentBuilder.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\layout\juce_ComponentMovementWatcher.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\layout\juce_ConcertinaPanel.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\layout\juce_FlexBox.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\layout\juce_FlexItem.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\layout\juce_Grid.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\layout\juce_GridItem.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\layout\juce_GroupComponent.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\layout\juce_MultiDocumentPanel.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\layout\juce_ResizableBorderComponent.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\layout\juce_ResizableCornerComponent.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\layout\juce_ResizableEdgeComponent.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\layout\juce_ScrollBar.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\layout\juce_SidePanel.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\layout\juce_StretchableLayoutManager.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\layout\juce_StretchableLayoutResizerBar.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\layout\juce_StretchableObjectResizer.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\layout\juce_TabbedButtonBar.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\layout\juce_TabbedComponent.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\layout\juce_Viewport.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\lookandfeel\juce_LookAndFeel.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\lookandfeel\juce_LookAndFeel_V1.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\lookandfeel\juce_LookAndFeel_V2.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\lookandfeel\juce_LookAndFeel_V3.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\lookandfeel\juce_LookAndFeel_V4.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\menus\juce_BurgerMenuComponent.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\menus\juce_MenuBarComponent.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\menus\juce_MenuBarModel.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\menus\juce_PopupMenu.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\misc\juce_BubbleComponent.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\misc\juce_DropShadower.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\misc\juce_FocusOutline.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\mouse\juce_ComponentDragger.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\mouse\juce_DragAndDropContainer.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\mouse\juce_DragAndDropTarget.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\mouse\juce_FileDragAndDropTarget.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\mouse\juce_LassoComponent.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\mouse\juce_MouseCursor.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\mouse\juce_MouseEvent.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\mouse\juce_MouseInactivityDetector.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\mouse\juce_MouseInputSource.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\mouse\juce_MouseListener.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\mouse\juce_SelectedItemSet.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\mouse\juce_TextDragAndDropTarget.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\mouse\juce_TooltipClient.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\native\accessibility\juce_AccessibilityElement_windows.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\native\accessibility\juce_AccessibilityTextHelpers.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\native\accessibility\juce_UIAExpandCollapseProvider_windows.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\native\accessibility\juce_UIAGridItemProvider_windows.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\native\accessibility\juce_UIAGridProvider_windows.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\native\accessibility\juce_UIAHelpers_windows.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\native\accessibility\juce_UIAInvokeProvider_windows.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\native\accessibility\juce_UIAProviderBase_windows.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\native\accessibility\juce_UIAProviders_windows.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\native\accessibility\juce_UIARangeValueProvider_windows.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\native\accessibility\juce_UIASelectionProvider_windows.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\native\accessibility\juce_UIATextProvider_windows.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\native\accessibility\juce_UIAToggleProvider_windows.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\native\accessibility\juce_UIATransformProvider_windows.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\native\accessibility\juce_UIAValueProvider_windows.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\native\accessibility\juce_UIAWindowProvider_windows.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\native\accessibility\juce_WindowsUIAWrapper_windows.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\native\juce_CGMetalLayerRenderer_mac.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\native\juce_MultiTouchMapper.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\native\juce_NativeModalWrapperComponent_ios.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\native\juce_PerScreenDisplayLinks_mac.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\native\juce_ScopedDPIAwarenessDisabler.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\native\juce_ScopedThreadDPIAwarenessSetter_windows.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\native\juce_ScopedWindowAssociation_linux.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\native\juce_WindowsHooks_windows.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\native\juce_XSymbols_linux.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\native\juce_XWindowSystem_linux.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\positioning\juce_MarkerList.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\positioning\juce_RelativeCoordinate.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\positioning\juce_RelativeCoordinatePositioner.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\positioning\juce_RelativeParallelogram.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\positioning\juce_RelativePoint.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\positioning\juce_RelativePointPath.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\positioning\juce_RelativeRectangle.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\properties\juce_BooleanPropertyComponent.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\properties\juce_ButtonPropertyComponent.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\properties\juce_ChoicePropertyComponent.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\properties\juce_MultiChoicePropertyComponent.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\properties\juce_PropertyComponent.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\properties\juce_PropertyPanel.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\properties\juce_SliderPropertyComponent.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\properties\juce_TextPropertyComponent.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\widgets\juce_ComboBox.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\widgets\juce_ImageComponent.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\widgets\juce_Label.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\widgets\juce_ListBox.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\widgets\juce_ProgressBar.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\widgets\juce_Slider.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\widgets\juce_TableHeaderComponent.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\widgets\juce_TableListBox.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\widgets\juce_TextEditor.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\widgets\juce_Toolbar.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\widgets\juce_ToolbarItemComponent.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\widgets\juce_ToolbarItemFactory.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\widgets\juce_ToolbarItemPalette.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\widgets\juce_TreeView.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\windows\juce_AlertWindow.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\windows\juce_CallOutBox.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\windows\juce_ComponentPeer.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\windows\juce_DialogWindow.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\windows\juce_DocumentWindow.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\windows\juce_MessageBoxOptions.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\windows\juce_NativeMessageBox.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\windows\juce_NativeScaleFactorNotifier.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\windows\juce_ResizableWindow.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\windows\juce_ScopedMessageBox.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\windows\juce_ThreadWithProgressWindow.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\windows\juce_TooltipWindow.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\windows\juce_TopLevelWindow.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\windows\juce_VBlankAttachment.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\windows\juce_WindowUtils.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_basics\juce_gui_basics.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_extra\code_editor\juce_CodeDocument.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_extra\code_editor\juce_CodeEditorComponent.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_extra\code_editor\juce_CodeTokeniser.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_extra\code_editor\juce_CPlusPlusCodeTokeniser.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_extra\code_editor\juce_CPlusPlusCodeTokeniserFunctions.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_extra\code_editor\juce_LuaCodeTokeniser.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_extra\code_editor\juce_XMLCodeTokeniser.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_extra\detail\juce_WebControlRelayEvents.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_extra\documents\juce_FileBasedDocument.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_extra\embedding\juce_ActiveXControlComponent.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_extra\embedding\juce_AndroidViewComponent.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_extra\embedding\juce_HWNDComponent.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_extra\embedding\juce_NSViewComponent.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_extra\embedding\juce_UIViewComponent.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_extra\embedding\juce_XEmbedComponent.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_extra\misc\juce_AnimatedAppComponent.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_extra\misc\juce_AppleRemote.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_extra\misc\juce_BubbleMessageComponent.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_extra\misc\juce_ColourSelector.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_extra\misc\juce_KeyMappingEditorComponent.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_extra\misc\juce_LiveConstantEditor.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_extra\misc\juce_PreferencesPanel.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_extra\misc\juce_PushNotifications.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_extra\misc\juce_RecentlyOpenedFilesList.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_extra\misc\juce_SplashScreen.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_extra\misc\juce_SystemTrayIconComponent.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_extra\misc\juce_WebBrowserComponent.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_extra\misc\juce_WebControlParameterIndexReceiver.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_extra\misc\juce_WebControlRelays.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_extra\native\juce_NSViewFrameWatcher_mac.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_gui_extra\juce_gui_extra.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_midi_ci\ci\juce_CIChannelAddress.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_midi_ci\ci\juce_CIDevice.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_midi_ci\ci\juce_CIDeviceFeatures.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_midi_ci\ci\juce_CIDeviceListener.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_midi_ci\ci\juce_CIDeviceMessageHandler.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_midi_ci\ci\juce_CIDeviceOptions.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_midi_ci\ci\juce_CIEncoding.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_midi_ci\ci\juce_CIEncodings.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_midi_ci\ci\juce_CIFunctionBlock.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_midi_ci\ci\juce_CIMessages.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_midi_ci\ci\juce_CIMuid.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_midi_ci\ci\juce_CIParser.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_midi_ci\ci\juce_CIProfileAtAddress.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_midi_ci\ci\juce_CIProfileDelegate.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_midi_ci\ci\juce_CIProfileHost.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_midi_ci\ci\juce_CIProfileStates.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_midi_ci\ci\juce_CIPropertyDelegate.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_midi_ci\ci\juce_CIPropertyExchangeCache.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_midi_ci\ci\juce_CIPropertyExchangeResult.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_midi_ci\ci\juce_CIPropertyHost.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_midi_ci\ci\juce_CIResponderDelegate.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_midi_ci\ci\juce_CIResponderOutput.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_midi_ci\ci\juce_CISubscription.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_midi_ci\ci\juce_CISubscriptionManager.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_midi_ci\ci\juce_CISupportedAndActive.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_midi_ci\detail\juce_CIMarshalling.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_midi_ci\detail\juce_CIMessageMeta.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_midi_ci\detail\juce_CIMessageTypeUtils.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_midi_ci\detail\juce_CIPropertyDataMessageChunker.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_midi_ci\detail\juce_CIPropertyHostUtils.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_midi_ci\detail\juce_CIResponder.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_midi_ci\juce_midi_ci.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_osc\osc\juce_OSCAddress.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_osc\osc\juce_OSCArgument.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_osc\osc\juce_OSCBundle.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_osc\osc\juce_OSCMessage.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_osc\osc\juce_OSCReceiver.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_osc\osc\juce_OSCSender.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_osc\osc\juce_OSCTimeTag.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_osc\osc\juce_OSCTypes.h"/>
    <ClInclude Include="..\..\..\..\..\JUCE\modules\juce_osc\juce_osc.h"/>
    <ClInclude Include="..\..\JuceLibraryCode\JuceHeader.h"/>
    <ClInclude Include="..\..\JuceLibraryCode\JucePluginDefines.h"/>
  </ItemGroup>
  <ItemGroup>
    <None Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\common\README.md"/>
    <None Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\src\flowgraph\resampler\README.md"/>
    <None Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\CMakeLists.txt"/>
    <None Include="..\..\..\..\..\JUCE\modules\juce_audio_devices\native\oboe\README.md"/>
    <None Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\flac\Flac Licence.txt"/>
    <None Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\flac\JUCE_CHANGES.txt"/>
    <None Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\README.md"/>
    <None Include="..\..\..\..\..\JUCE\modules\juce_audio_formats\codecs\oggvorbis\Ogg Vorbis Licence.txt"/>
    <None Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\LV2_SDK\README.md"/>
    <None Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\base\LICENSE.txt"/>
    <None Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\base\README.md"/>
    <None Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\LICENSE.txt"/>
    <None Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\README.md"/>
    <None Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\moduleinfo\ReadMe.md"/>
    <None Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\LICENSE.txt"/>
    <None Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\README.md"/>
    <None Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\JUCE_README.md"/>
    <None Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\LICENSE.txt"/>
    <None Include="..\..\..\..\..\JUCE\modules\juce_audio_processors\format_types\VST3_SDK\README.md"/>
    <None Include="..\..\..\..\..\JUCE\modules\juce_core\native\java\README.txt"/>
    <None Include="..\..\..\..\..\JUCE\modules\juce_core\zip\zlib\JUCE_CHANGES.txt"/>
    <None Include="..\..\..\..\..\JUCE\modules\juce_graphics\image_formats\jpglib\changes to libjpeg for JUCE.txt"/>
    <None Include="..\..\..\..\..\JUCE\modules\juce_graphics\image_formats\pnglib\libpng_readme.txt"/>
    <None Include="..\..\..\..\..\JUCE\modules\juce_graphics\unicode\sheenbidi\JUCE_CHANGES.txt"/>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets"/>
</Project>
