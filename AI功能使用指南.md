# AI和弦助手插件 - AI功能使用指南

## 🚀 快速开始

### 第一步：配置AI服务
1. 启动插件后，点击右侧Chat界面的"AI配置"按钮
2. 选择您偏好的AI提供商：
   - **OpenAI**: 需要OpenAI API密钥
   - **Groq**: 免费且快速，推荐新手使用
   - **Claude**: Anthropic的Claude模型
3. 输入您的API密钥
4. 点击"测试连接"确保配置正确
5. 点击"保存"完成配置

### 第二步：开始使用AI功能
配置完成后，您就可以开始使用所有AI功能了！

## 🎵 主要AI功能

### 1. 快捷风格生成
**位置**: Chat界面顶部的风格按钮

**使用方法**:
- 点击"流行"按钮 → 生成流行音乐风格的和弦进行
- 点击"爵士"按钮 → 生成爵士风格的复杂和弦
- 点击"古典"按钮 → 生成古典音乐风格的和声
- 点击"蓝调"按钮 → 生成蓝调风格的和弦进行

**示例结果**:
```
流行风格: C - Am - F - G (经典的I-vi-IV-V进行)
爵士风格: Cmaj7 - A7 - Dm7 - G7 (更复杂的七和弦)
古典风格: C - F - G - C (传统的功能和声)
蓝调风格: C7 - F7 - G7 - C7 (蓝调十二小节)
```

### 2. 智能对话生成
**位置**: Chat界面的文本输入框

**使用方法**:
1. 在输入框中描述您的需求
2. 点击"发送"按钮
3. AI会根据当前音乐上下文生成建议

**示例对话**:
```
用户: "请为我的歌曲副歌部分生成一个有张力的和弦进行"
AI: "建议使用 Am - F - C - G 的进行，这个进行从小调开始，
    通过F大调和弦增加亮度，最后用属和弦G创造回到主调的张力。
    这种进行常用于流行歌曲的副歌部分，能够很好地表达情感张力。"
```

### 3. 和弦进行分析
**位置**: Chat界面的"分析"按钮

**使用方法**:
1. 在音乐编辑器中创建一些和弦
2. 点击"分析进行"按钮
3. AI会分析您的和弦进行并提供专业见解

**分析内容包括**:
- 调性分析 (大调/小调)
- 功能和声分析 (主、属、下属功能)
- 和弦进行模式识别
- 风格特征分析
- 改进建议

### 4. 智能和弦生成
**位置**: Chat界面的"生成和弦"按钮

**使用方法**:
1. 设置好基本的音乐参数 (调性、拍号等)
2. 点击"生成和弦"按钮
3. AI会为当前段落生成合适的和弦进行

**生成考虑因素**:
- 当前调性设置
- 已有的和弦内容
- 音乐段落长度
- 和声进行的逻辑性

## 🎛️ AI建议面板使用

### 查看AI建议
当AI生成和弦建议后，左侧的AI建议面板会显示：
- **和弦按钮**: 每个建议的和弦都有独立按钮
- **解释文本**: AI对建议的详细解释
- **操作按钮**: "接受全部"和"清空"

### 选择和弦
- **单个选择**: 点击任意和弦按钮，该和弦会添加到音乐编辑器
- **批量接受**: 点击"接受全部"按钮，所有建议的和弦都会添加
- **清空建议**: 点击"清空"按钮移除当前建议

## 💡 高级使用技巧

### 1. 上下文相关的请求
AI会考虑您当前的音乐上下文，所以您可以：
- 在已有和弦的基础上请求续写
- 要求AI修改特定位置的和弦
- 请求与现有风格一致的新和弦

**示例**:
```
"我已经有了 C - Am - F，请帮我完成这个四和弦进行"
"请把第三个和弦改成更有张力的选择"
"这个进行太平淡了，请给我一些更有趣的变化"
```

### 2. 风格混合请求
您可以要求AI混合不同的音乐风格：

**示例**:
```
"请生成一个融合了爵士和流行元素的和弦进行"
"我想要古典风格的和声，但是用现代流行的节奏"
"请给我一个蓝调风格的进行，但是要适合摇滚歌曲"
```

### 3. 技术性要求
AI理解音乐理论术语，您可以使用专业术语：

**示例**:
```
"请使用二级五级一级的进行"
"我需要一个包含减七和弦的进行"
"请避免使用平行五度"
"给我一个使用模态交换的和弦进行"
```

## 🔧 配置优化建议

### API密钥获取
1. **Groq** (推荐新手):
   - 访问 console.groq.com
   - 注册免费账户
   - 获取API密钥
   - 优点：免费、快速、稳定

2. **OpenAI**:
   - 访问 platform.openai.com
   - 需要付费账户
   - 优点：响应质量高、理解能力强

3. **Claude**:
   - 访问 console.anthropic.com
   - 需要付费账户
   - 优点：音乐理论理解深入

### 参数调整
- **温度 (0.0-1.0)**: 控制AI的创造性
  - 0.3-0.5: 保守、理论正确的建议
  - 0.7-0.9: 更有创意、实验性的建议
- **最大Token数**: 控制响应长度
  - 500-1000: 简洁的建议
  - 1000-2000: 详细的解释和建议

## 🎯 使用场景示例

### 场景1: 歌曲创作
```
1. 设置调性为C大调，4/4拍
2. 点击"流行"按钮获得基础进行
3. 对话："请为这个进行添加一个更有趣的桥段"
4. 选择AI建议的和弦
5. 分析完整的和弦进行
```

### 场景2: 学习音乐理论
```
1. 创建一个简单的和弦进行
2. 点击"分析进行"
3. 阅读AI的理论解释
4. 对话："为什么这个进行听起来很悲伤？"
5. 学习AI提供的音乐理论知识
```

### 场景3: 风格探索
```
1. 对话："请解释爵士和弦的特点"
2. 点击"爵士"按钮体验爵士和弦
3. 对话："请把这个爵士进行简化成流行风格"
4. 比较不同风格的差异
```

## ⚠️ 注意事项

### 网络连接
- 确保网络连接稳定
- AI功能需要互联网连接
- 请求失败时会自动重试

### API限制
- 注意API调用频率限制
- 合理使用，避免过度请求
- 保存重要的AI建议

### 隐私安全
- API密钥会安全存储在本地
- 不会上传您的个人音乐作品
- 只发送必要的音乐上下文信息

## 🆘 常见问题

**Q: AI不响应怎么办？**
A: 检查网络连接和API密钥配置，点击"测试连接"验证。

**Q: AI建议的和弦不合适怎么办？**
A: 可以通过对话进一步细化要求，或者调整温度参数。

**Q: 如何获得更专业的建议？**
A: 使用音乐理论术语描述需求，提供更多上下文信息。

**Q: 可以保存AI的建议吗？**
A: 当前版本的建议会在对话历史中保存，未来版本会支持导出功能。

---

**提示**: 多尝试不同的表达方式和AI提供商，找到最适合您创作风格的配置！
