/*
  ==============================================================================

    This file contains the basic framework code for a JUCE plugin processor.

  ==============================================================================
*/

#include "PluginProcessor.h"
#include "PluginEditor.h"

//==============================================================================
ColorAssistantAudioProcessor::ColorAssistantAudioProcessor()
#ifndef JucePlugin_PreferredChannelConfigurations
     : AudioProcessor (BusesProperties()
                     #if ! JucePlugin_IsMidiEffect
                      #if ! JucePlugin_IsSynth
                       .withInput  ("Input",  juce::AudioChannelSet::stereo(), true)
                      #endif
                       .withOutput ("Output", juce::AudioChannelSet::stereo(), true)
                     #endif
                       ),
#endif
       syncWithDAW(true), manualBPM(120.0f),
       currentSampleRate(44100.0), currentSamplesPerBlock(512)
{
    updateMusicSectionInEngine();
}

ColorAssistantAudioProcessor::~ColorAssistantAudioProcessor()
{
}

//==============================================================================
const juce::String ColorAssistantAudioProcessor::getName() const
{
    return JucePlugin_Name;
}

bool ColorAssistantAudioProcessor::acceptsMidi() const
{
   #if JucePlugin_WantsMidiInput
    return true;
   #else
    return false;
   #endif
}

bool ColorAssistantAudioProcessor::producesMidi() const
{
   #if JucePlugin_ProducesMidiOutput
    return true;
   #else
    return false;
   #endif
}

bool ColorAssistantAudioProcessor::isMidiEffect() const
{
   #if JucePlugin_IsMidiEffect
    return true;
   #else
    return false;
   #endif
}

double ColorAssistantAudioProcessor::getTailLengthSeconds() const
{
    return 0.0;
}

int ColorAssistantAudioProcessor::getNumPrograms()
{
    return 1;   // NB: some hosts don't cope very well if you tell them there are 0 programs,
                // so this should be at least 1, even if you're not really implementing programs.
}

int ColorAssistantAudioProcessor::getCurrentProgram()
{
    return 0;
}

void ColorAssistantAudioProcessor::setCurrentProgram (int index)
{
}

const juce::String ColorAssistantAudioProcessor::getProgramName (int index)
{
    return {};
}

void ColorAssistantAudioProcessor::changeProgramName (int index, const juce::String& newName)
{
}

//==============================================================================
void ColorAssistantAudioProcessor::prepareToPlay (double sampleRate, int samplesPerBlock)
{
    currentSampleRate = sampleRate;
    currentSamplesPerBlock = samplesPerBlock;

    playbackEngine.prepareToPlay(sampleRate, samplesPerBlock);
}

void ColorAssistantAudioProcessor::releaseResources()
{
    playbackEngine.releaseResources();
}

#ifndef JucePlugin_PreferredChannelConfigurations
bool ColorAssistantAudioProcessor::isBusesLayoutSupported (const BusesLayout& layouts) const
{
  #if JucePlugin_IsMidiEffect
    juce::ignoreUnused (layouts);
    return true;
  #else
    // This is the place where you check if the layout is supported.
    // In this template code we only support mono or stereo.
    // Some plugin hosts, such as certain GarageBand versions, will only
    // load plugins that support stereo bus layouts.
    if (layouts.getMainOutputChannelSet() != juce::AudioChannelSet::mono()
     && layouts.getMainOutputChannelSet() != juce::AudioChannelSet::stereo())
        return false;

    // This checks if the input layout matches the output layout
   #if ! JucePlugin_IsSynth
    if (layouts.getMainOutputChannelSet() != layouts.getMainInputChannelSet())
        return false;
   #endif

    return true;
  #endif
}
#endif

void ColorAssistantAudioProcessor::processBlock (juce::AudioBuffer<float>& buffer, juce::MidiBuffer& midiMessages)
{
    juce::ScopedNoDenormals noDenormals;
    auto totalNumInputChannels  = getTotalNumInputChannels();
    auto totalNumOutputChannels = getTotalNumOutputChannels();

    // Clear any output channels that don't contain input data
    for (auto i = totalNumInputChannels; i < totalNumOutputChannels; ++i)
        buffer.clear (i, 0, buffer.getNumSamples());

    // Update BPM from DAW if syncing
    if (syncWithDAW)
    {
        auto playHead = getPlayHead();
        if (playHead != nullptr)
        {
            juce::AudioPlayHead::CurrentPositionInfo positionInfo;
            if (playHead->getCurrentPosition(positionInfo) && positionInfo.bpm > 0)
            {
                playbackEngine.setBPM((float)positionInfo.bpm);
            }
        }
    }
    else
    {
        playbackEngine.setBPM(manualBPM);
    }

    // Process audio through the playback engine
    playbackEngine.processAudio(buffer, midiMessages);
}

//==============================================================================
bool ColorAssistantAudioProcessor::hasEditor() const
{
    return true; // (change this to false if you choose to not supply an editor)
}

juce::AudioProcessorEditor* ColorAssistantAudioProcessor::createEditor()
{
    return new ColorAssistantAudioProcessorEditor (*this);
}

//==============================================================================
void ColorAssistantAudioProcessor::getStateInformation (juce::MemoryBlock& destData)
{
    // You should use this method to store your parameters in the memory block.
    // You could do that either as raw data, or use the XML or ValueTree classes
    // as intermediaries to make it easy to save and load complex data.
}

void ColorAssistantAudioProcessor::setStateInformation (const void* data, int sizeInBytes)
{
    // You should use this method to restore your parameters from this memory block,
    // whose contents will have been created by the getStateInformation() call.
}

//==============================================================================
// AI Chord Assistant specific implementations

void ColorAssistantAudioProcessor::startPlayback()
{
    playbackEngine.startPlayback();
}

void ColorAssistantAudioProcessor::stopPlayback()
{
    playbackEngine.stopPlayback();
}

void ColorAssistantAudioProcessor::pausePlayback()
{
    playbackEngine.pausePlayback();
}

bool ColorAssistantAudioProcessor::isPlaying() const
{
    return playbackEngine.isPlaying();
}

bool ColorAssistantAudioProcessor::isPaused() const
{
    return playbackEngine.isPaused();
}

void ColorAssistantAudioProcessor::setBPM(float newBPM)
{
    manualBPM = newBPM;
    if (!syncWithDAW)
    {
        playbackEngine.setBPM(newBPM);
    }
}

float ColorAssistantAudioProcessor::getBPM() const
{
    if (syncWithDAW)
    {
        auto playHead = getPlayHead();
        if (playHead != nullptr)
        {
            juce::AudioPlayHead::CurrentPositionInfo positionInfo;
            if (playHead->getCurrentPosition(positionInfo) && positionInfo.bpm > 0)
            {
                return (float)positionInfo.bpm;
            }
        }
        return 120.0f; // Default if no DAW info available
    }
    return manualBPM;
}

void ColorAssistantAudioProcessor::setPlayPosition(double positionInBeats)
{
    playbackEngine.setPlayPosition(positionInBeats);
}

double ColorAssistantAudioProcessor::getPlayPosition() const
{
    return playbackEngine.getPlayPosition();
}

void ColorAssistantAudioProcessor::setVolume(float volume)
{
    playbackEngine.setVolume(volume);
}

float ColorAssistantAudioProcessor::getVolume() const
{
    return playbackEngine.getVolume();
}

void ColorAssistantAudioProcessor::setLooping(bool shouldLoop)
{
    playbackEngine.setLooping(shouldLoop);
}

bool ColorAssistantAudioProcessor::isLooping() const
{
    return playbackEngine.isLooping();
}

void ColorAssistantAudioProcessor::updateMusicSectionInEngine()
{
    playbackEngine.setMusicSection(&musicSection);
}

//==============================================================================
// This creates new instances of the plugin..
juce::AudioProcessor* JUCE_CALLTYPE createPluginFilter()
{
    return new ColorAssistantAudioProcessor();
}
