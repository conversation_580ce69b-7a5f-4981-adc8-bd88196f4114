/*
  ==============================================================================

    Demo.cpp
    Created: AI Chord Assistant Plugin
    Author:  AI Assistant

    This file contains demo code to test the basic functionality.

  ==============================================================================
*/

#include "MusicData.h"
#include <iostream>

// 简单的演示函数，展示如何使用我们的音乐数据类
void demonstrateBasicFunctionality()
{
    std::cout << "=== AI和弦助手插件 - 基础功能演示 ===" << std::endl;
    
    // 创建音乐段落
    MusicSection section;
    section.numberOfBars = 4;
    section.globalKey = KeySignature::CMajor;
    section.globalTimeSignature = TimeSignature(4, 4);
    section.bpm = 120.0f;
    
    std::cout << "创建了一个4小节的音乐段落，C大调，4/4拍，120 BPM" << std::endl;
    
    // 创建一些和弦
    ChordData chord1("Cmaj", 0, ChordType::Major);
    chord1.barNumber = 1;
    chord1.startBeat = 0.0f;
    chord1.duration = 4.0f;
    
    ChordData chord2("Am", 9, ChordType::Minor);
    chord2.barNumber = 2;
    chord2.startBeat = 0.0f;
    chord2.duration = 4.0f;
    
    ChordData chord3("F", 5, ChordType::Major);
    chord3.barNumber = 3;
    chord3.startBeat = 0.0f;
    chord3.duration = 4.0f;
    
    ChordData chord4("G", 7, ChordType::Major);
    chord4.barNumber = 4;
    chord4.startBeat = 0.0f;
    chord4.duration = 4.0f;
    
    // 添加和弦到段落
    section.addChord(chord1);
    section.addChord(chord2);
    section.addChord(chord3);
    section.addChord(chord4);
    
    std::cout << "添加了经典的 C-Am-F-G 和弦进行" << std::endl;
    
    // 显示和弦信息
    std::cout << "\n和弦详情:" << std::endl;
    for (int i = 0; i < section.chords.size(); ++i)
    {
        const auto& chord = section.chords.getReference(i);
        std::cout << "  " << (i+1) << ". " << chord.toString().toStdString() << std::endl;
        
        // 显示MIDI音符
        std::cout << "     MIDI音符: ";
        for (int note : chord.noteNumbers)
        {
            std::cout << note << " ";
        }
        std::cout << std::endl;
    }
    
    // 测试MIDI生成
    auto midiSequence = section.generateMidiSequence();
    std::cout << "\n生成了包含 " << midiSequence.getNumEvents() << " 个MIDI事件的序列" << std::endl;
    
    // 测试音乐理论工具
    std::cout << "\n=== 音乐理论工具测试 ===" << std::endl;
    
    // 测试音符名称转换
    for (int i = 0; i < 12; ++i)
    {
        auto noteName = MusicTheoryUtils::noteNumberToName(i);
        auto noteNumber = MusicTheoryUtils::noteNameToNumber(noteName);
        std::cout << "音符 " << i << " = " << noteName.toStdString() 
                  << " (反向转换: " << noteNumber << ")" << std::endl;
    }
    
    // 测试和弦构建
    std::cout << "\n=== 和弦构建测试 ===" << std::endl;
    auto majorChordNotes = MusicTheoryUtils::buildChord(0, ChordType::Major);
    std::cout << "C大三和弦音符: ";
    for (int note : majorChordNotes)
    {
        std::cout << note << " ";
    }
    std::cout << std::endl;
    
    auto minorChordNotes = MusicTheoryUtils::buildChord(9, ChordType::Minor);
    std::cout << "A小三和弦音符: ";
    for (int note : minorChordNotes)
    {
        std::cout << note << " ";
    }
    std::cout << std::endl;
    
    std::cout << "\n=== 演示完成 ===" << std::endl;
}

// 测试和弦解析功能
void testChordParsing()
{
    std::cout << "\n=== 和弦解析测试 ===" << std::endl;
    
    juce::StringArray testChords = {
        "C", "Cmaj", "Cmaj7", "Cm", "Cm7", "C7",
        "Dm", "Dm7", "Em", "F", "Fmaj7", "G", "G7", "Am", "Am7"
    };
    
    for (const auto& chordName : testChords)
    {
        auto chord = ChordData::fromString(chordName);
        std::cout << "解析 '" << chordName.toStdString() << "' -> " 
                  << chord.toString().toStdString() << std::endl;
    }
}

// 测试时间签名和调式变化
void testMusicChanges()
{
    std::cout << "\n=== 调式和拍号变化测试 ===" << std::endl;
    
    MusicSection section;
    section.numberOfBars = 8;
    section.globalKey = KeySignature::CMajor;
    section.globalTimeSignature = TimeSignature(4, 4);
    
    // 添加调式变化
    MusicChange keyChange(5, 0.0f);
    keyChange.hasKeyChange = true;
    keyChange.newKey = KeySignature::AMinor;
    section.addMusicChange(keyChange);
    
    // 添加拍号变化
    MusicChange timeChange(7, 0.0f);
    timeChange.hasTimeSignatureChange = true;
    timeChange.newTimeSignature = TimeSignature(3, 4);
    section.addMusicChange(timeChange);
    
    // 测试不同位置的调式和拍号
    for (int bar = 1; bar <= 8; ++bar)
    {
        auto key = section.getKeyAt(bar, 0.0f);
        auto timeSig = section.getTimeSignatureAt(bar, 0.0f);
        
        std::cout << "小节 " << bar << ": 调式=" << (int)key 
                  << ", 拍号=" << timeSig.toString().toStdString() << std::endl;
    }
}

/*
// 注意：这个演示函数不会被编译到插件中，
// 它只是用来展示我们的类的基本用法。
// 在实际的插件中，这些功能会通过UI界面来使用。

int main()
{
    demonstrateBasicFunctionality();
    testChordParsing();
    testMusicChanges();
    return 0;
}
*/
