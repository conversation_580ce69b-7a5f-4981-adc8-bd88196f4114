@echo off
echo ========================================
echo AI和弦助手插件 - 构建脚本
echo ========================================

echo.
echo 检查JUCE项目文件...
if not exist "Color Assistant\Color Assistant.jucer" (
    echo 错误: 找不到JUCE项目文件
    echo 请确保在正确的目录中运行此脚本
    pause
    exit /b 1
)

echo 找到JUCE项目文件: Color Assistant\Color Assistant.jucer

echo.
echo 检查构建目录...
if not exist "Color Assistant\Builds\VisualStudio2022" (
    echo 警告: Visual Studio 2022构建目录不存在
    echo 请先在Projucer中生成项目文件
    echo.
    echo 步骤:
    echo 1. 打开 Color Assistant\Color Assistant.jucer
    echo 2. 点击 "Save and Open in IDE"
    echo 3. 重新运行此脚本
    pause
    exit /b 1
)

echo.
echo 尝试使用MSBuild编译项目...
echo.

set MSBUILD_PATH=""
for /f "usebackq tokens=*" %%i in (`"%ProgramFiles(x86)%\Microsoft Visual Studio\Installer\vswhere.exe" -latest -products * -requires Microsoft.Component.MSBuild -property installationPath`) do (
    set VS_PATH=%%i
)

if defined VS_PATH (
    set MSBUILD_PATH="%VS_PATH%\MSBuild\Current\Bin\MSBuild.exe"
    echo 找到Visual Studio: %VS_PATH%
) else (
    echo 警告: 未找到Visual Studio 2022
    echo 请手动打开项目文件进行编译
    pause
    exit /b 1
)

echo.
echo 开始编译 Debug 版本...
%MSBUILD_PATH% "Color Assistant\Builds\VisualStudio2022\Color Assistant.sln" /p:Configuration=Debug /p:Platform=x64

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✓ Debug 版本编译成功!
    echo.
    echo 开始编译 Release 版本...
    %MSBUILD_PATH% "Color Assistant\Builds\VisualStudio2022\Color Assistant.sln" /p:Configuration=Release /p:Platform=x64
    
    if %ERRORLEVEL% EQU 0 (
        echo.
        echo ✓ Release 版本编译成功!
        echo.
        echo 构建完成! 插件文件位置:
        echo Debug VST3:   Color Assistant\Builds\VisualStudio2022\x64\Debug\VST3\
        echo Release VST3: Color Assistant\Builds\VisualStudio2022\x64\Release\VST3\
    ) else (
        echo.
        echo ✗ Release 版本编译失败
    )
) else (
    echo.
    echo ✗ Debug 版本编译失败
    echo.
    echo 可能的解决方案:
    echo 1. 检查是否安装了所有必需的Visual Studio组件
    echo 2. 确保JUCE路径配置正确
    echo 3. 手动在Visual Studio中打开项目进行编译
)

echo.
echo ========================================
echo 构建脚本完成
echo ========================================
pause
