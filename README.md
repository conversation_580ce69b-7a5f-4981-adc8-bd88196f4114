# AI和弦助手插件

一个基于JUCE框架开发的AI驱动音乐插件，能够辅助音乐人进行和弦编写，根据用户需求智能生成和弦进行，并提供乐理层面的解释。

## 项目状态

### ✅ 已完成 (第一阶段)

1. **核心数据结构**
   - `ChordData` - 和弦数据类，支持各种和弦类型
   - `MusicSection` - 音乐段落管理类
   - `MusicTheoryUtils` - 音乐理论工具类
   - 支持调式和拍号变化
   - MIDI序列生成基础功能

2. **用户界面组件**
   - `MusicSectionEditor` - 音乐段落编辑器
   - `BarGrid` - 小节网格显示组件
   - `ChordSelectorDialog` - 和弦选择器弹窗
   - `PlaybackControls` - 播放控制面板
   - 基础的Chat界面框架

3. **插件框架**
   - 完整的JUCE插件结构
   - 音频处理器集成
   - 编辑器界面集成
   - BPM同步功能框架

### 🚧 进行中 (第二阶段)

1. **播放引擎**
   - 内部音源实现 (基础框架已完成)
   - MIDI播放功能
   - 音频合成器优化

2. **MIDI导出**
   - 拖拽到DAW功能
   - MIDI文件导出

### 📋 待开发 (后续阶段)

1. **AI集成**
   - Python后端服务
   - LLM API调用
   - Prompt工程
   - 响应解析

2. **高级功能**
   - 复杂和弦解析
   - 多模型支持
   - 会员机制

## 技术架构

### 核心组件

```
AI和弦助手插件
├── 数据层
│   ├── MusicData.h/cpp - 核心音乐数据结构
│   └── Demo.cpp - 功能演示代码
├── UI层
│   ├── MusicSectionEditor.h/cpp - 主编辑界面
│   ├── PluginEditor.h/cpp - 插件主界面
│   └── PluginProcessor.h/cpp - 音频处理器
└── 配置
    └── Color Assistant.jucer - JUCE项目配置
```

### 主要类说明

#### MusicData.h
- `ChordData`: 和弦数据，包含名称、类型、位置、时值等
- `MusicSection`: 音乐段落，管理多个和弦和调式变化
- `MusicTheoryUtils`: 音乐理论工具，提供音符转换、和弦构建等功能

#### MusicSectionEditor.h
- `MusicSectionEditor`: 主编辑器组件
- `BarGrid`: 小节网格，支持和弦的可视化编辑
- `ChordSelectorDialog`: 和弦选择弹窗
- `PlaybackControls`: 播放控制面板

## 编译和运行

### 环境要求
- JUCE Framework (已配置)
- Visual Studio 2022 (Windows)
- Xcode (macOS)
- 支持C++17的编译器

### 编译步骤
1. 打开 `Color Assistant/Color Assistant.jucer`
2. 在Projucer中点击 "Save and Open in IDE"
3. 在IDE中编译项目

### 当前功能测试
1. 编译成功后，插件会显示主界面
2. 可以在小节网格中点击添加和弦
3. 可以使用播放控制面板
4. 可以在Chat界面输入文本（目前返回模拟响应）

## 使用说明

### 基本操作
1. **添加和弦**: 点击小节网格中的空白位置，会弹出和弦选择器
2. **编辑和弦**: 点击已有的和弦可以重新编辑
3. **调整参数**: 使用顶部的控制面板调整调式、拍号、小节数
4. **播放控制**: 使用播放按钮试听和弦（需要完成音源实现）

### 界面布局
```
┌─────────────────────────────────────────────────────────┐
│ AI和弦助手                                               │
├─────────────────────────────────────────────────────────┤
│ [播放] [停止] [同步DAW] BPM: [120] 音量: [70%]           │
├─────────────────────────────────┬───────────────────────┤
│ 调式: [C大调] 拍号: [4/4]        │ AI对话                │
│ 小节数: [8] [清空全部]           │ ┌─────────────────────┐ │
│ ┌─────────────────────────────┐ │ │ AI响应显示区域       │ │
│ │ 小节网格 (和弦编辑区域)      │ │ │                     │ │
│ │ [1] [2] [3] [4] [5] [6] [7] │ │ │                     │ │
│ │ ┌─┐ ┌─┐ ┌─┐ ┌─┐ ┌─┐ ┌─┐ ┌─┐ │ │ │                     │ │
│ │ │C│ │Am│ │F│ │G│ │ │ │ │ │ │ │ │                     │ │
│ │ └─┘ └─┘ └─┘ └─┘ └─┘ └─┘ └─┘ │ │ └─────────────────────┘ │
│ └─────────────────────────────┘ │ ┌─────────────────────┐ │
│                                 │ │ 用户输入框           │ │
│                                 │ └─────────────────────┘ │
│                                 │ [生成和弦]              │
└─────────────────────────────────┴───────────────────────┘
```

## 开发计划

### 第二阶段 (1-2周)
- [ ] 完善内部音源实现
- [ ] 实现MIDI播放功能
- [ ] 添加MIDI拖拽导出
- [ ] 优化UI响应性

### 第三阶段 (2-3周)
- [ ] 创建Python AI后端
- [ ] 集成第一个LLM API (Groq/OpenAI)
- [ ] 实现基础Prompt工程
- [ ] 测试AI和弦生成

### 第四阶段 (1-2周)
- [ ] 优化Prompt和响应解析
- [ ] 添加更多和弦类型支持
- [ ] 实现复杂时值处理
- [ ] 用户体验优化

## 贡献指南

### 代码结构
- 遵循JUCE编码规范
- 使用中文注释
- 保持模块化设计
- 添加适当的错误处理

### 测试
- 每个新功能都应该有对应的测试
- 使用Demo.cpp进行基础功能验证
- 在多个DAW中测试插件兼容性

## 许可证

本项目采用MIT许可证。详见LICENSE文件。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 项目Issues
- 开发者邮箱

---

**注意**: 这是一个正在开发中的项目，某些功能可能还不完整。请参考上面的项目状态了解当前可用的功能。
