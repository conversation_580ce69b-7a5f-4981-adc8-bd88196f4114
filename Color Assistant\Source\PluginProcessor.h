/*
  ==============================================================================

    This file contains the basic framework code for a JUCE plugin processor.

  ==============================================================================
*/

#pragma once

#include <JuceHeader.h>
#include "MusicData.h"

//==============================================================================
/**
*/
class ColorAssistantAudioProcessor  : public juce::AudioProcessor
{
public:
    //==============================================================================
    ColorAssistantAudioProcessor();
    ~ColorAssistantAudioProcessor() override;

    //==============================================================================
    void prepareToPlay (double sampleRate, int samplesPerBlock) override;
    void releaseResources() override;

   #ifndef JucePlugin_PreferredChannelConfigurations
    bool isBusesLayoutSupported (const BusesLayout& layouts) const override;
   #endif

    void processBlock (juce::AudioBuffer<float>&, juce::MidiBuffer&) override;

    //==============================================================================
    juce::AudioProcessorEditor* createEditor() override;
    bool hasEditor() const override;

    //==============================================================================
    const juce::String getName() const override;

    bool acceptsMidi() const override;
    bool producesMidi() const override;
    bool isMidiEffect() const override;
    double getTailLengthSeconds() const override;

    //==============================================================================
    int getNumPrograms() override;
    int getCurrentProgram() override;
    void setCurrentProgram (int index) override;
    const juce::String getProgramName (int index) override;
    void changeProgramName (int index, const juce::String& newName) override;

    //==============================================================================
    void getStateInformation (juce::MemoryBlock& destData) override;
    void setStateInformation (const void* data, int sizeInBytes) override;

    //==============================================================================
    // AI Chord Assistant specific methods
    MusicSection& getMusicSection() { return musicSection; }
    const MusicSection& getMusicSection() const { return musicSection; }

    // Playback control
    void startPlayback();
    void stopPlayback();
    bool isPlaying() const { return playbackActive; }

    // BPM management
    void setBPM(float newBPM);
    float getBPM() const;
    void setSyncWithDAW(bool sync) { syncWithDAW = sync; }
    bool isSyncWithDAW() const { return syncWithDAW; }

private:
    //==============================================================================
    // Core data
    MusicSection musicSection;

    // Playback state
    bool playbackActive;
    bool syncWithDAW;
    float manualBPM;

    // Audio synthesis
    juce::Synthesiser synthesiser;
    juce::MidiMessageCollector midiCollector;

    // Timing
    double currentSampleRate;
    int currentSamplesPerBlock;

    // Internal methods
    void setupSynthesiser();
    void updatePlaybackPosition();
    void generateMidiFromSection();

    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR (ColorAssistantAudioProcessor)
};
