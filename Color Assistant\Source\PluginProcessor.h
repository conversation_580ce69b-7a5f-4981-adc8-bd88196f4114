/*
  ==============================================================================

    This file contains the basic framework code for a JUCE plugin processor.

  ==============================================================================
*/

#pragma once

#include <JuceHeader.h>
#include "MusicData.h"
#include "AudioEngine.h"

//==============================================================================
/**
*/
class ColorAssistantAudioProcessor  : public juce::AudioProcessor
{
public:
    //==============================================================================
    ColorAssistantAudioProcessor();
    ~ColorAssistantAudioProcessor() override;

    //==============================================================================
    void prepareToPlay (double sampleRate, int samplesPerBlock) override;
    void releaseResources() override;

   #ifndef JucePlugin_PreferredChannelConfigurations
    bool isBusesLayoutSupported (const BusesLayout& layouts) const override;
   #endif

    void processBlock (juce::AudioBuffer<float>&, juce::MidiBuffer&) override;

    //==============================================================================
    juce::AudioProcessorEditor* createEditor() override;
    bool hasEditor() const override;

    //==============================================================================
    const juce::String getName() const override;

    bool acceptsMidi() const override;
    bool producesMidi() const override;
    bool isMidiEffect() const override;
    double getTailLengthSeconds() const override;

    //==============================================================================
    int getNumPrograms() override;
    int getCurrentProgram() override;
    void setCurrentProgram (int index) override;
    const juce::String getProgramName (int index) override;
    void changeProgramName (int index, const juce::String& newName) override;

    //==============================================================================
    void getStateInformation (juce::MemoryBlock& destData) override;
    void setStateInformation (const void* data, int sizeInBytes) override;

    //==============================================================================
    // AI Chord Assistant specific methods
    MusicSection& getMusicSection() { return musicSection; }
    const MusicSection& getMusicSection() const { return musicSection; }

    // Playback control
    void startPlayback();
    void stopPlayback();
    void pausePlayback();
    bool isPlaying() const;
    bool isPaused() const;

    // BPM management
    void setBPM(float newBPM);
    float getBPM() const;
    void setSyncWithDAW(bool sync) { syncWithDAW = sync; }
    bool isSyncWithDAW() const { return syncWithDAW; }

    // Playback position
    void setPlayPosition(double positionInBeats);
    double getPlayPosition() const;

    // Volume control
    void setVolume(float volume);
    float getVolume() const;

    // Looping
    void setLooping(bool shouldLoop);
    bool isLooping() const;

    // Audio engine access
    PlaybackEngine& getPlaybackEngine() { return playbackEngine; }

private:
    //==============================================================================
    // Core data
    MusicSection musicSection;

    // Audio engine
    PlaybackEngine playbackEngine;

    // Playback state
    bool syncWithDAW;
    float manualBPM;

    // Timing
    double currentSampleRate;
    int currentSamplesPerBlock;

    // Internal methods
    void updateMusicSectionInEngine();

    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR (ColorAssistantAudioProcessor)
};
