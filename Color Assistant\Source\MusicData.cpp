/*
  ==============================================================================

    MusicData.cpp
    Created: AI Chord Assistant Plugin
    Author:  AI Assistant

    Implementation of core music data structures.

  ==============================================================================
*/

#include "MusicData.h"

//==============================================================================
// ChordData Implementation
//==============================================================================

ChordData::ChordData()
    : chordName(""), rootNote(0), chordType(ChordType::Major), inversion(0),
      startBeat(0.0f), duration(1.0f), barNumber(1)
{
}

ChordData::ChordData(const juce::String& name, int root, ChordType type, int inv)
    : chordName(name), rootNote(root), chordType(type), inversion(inv),
      startBeat(0.0f), duration(1.0f), barNumber(1)
{
    generateNotes();
}

void ChordData::generateNotes()
{
    noteNumbers.clear();
    
    if (isEmpty())
        return;
    
    // 获取和弦音程
    auto intervals = MusicTheoryUtils::getChordIntervals(chordType);
    
    // 生成基础和弦音符
    for (int interval : intervals)
    {
        noteNumbers.add(rootNote + interval + 60); // 中央C为60
    }
    
    // 处理转位
    for (int i = 0; i < inversion && !noteNumbers.isEmpty(); ++i)
    {
        int lowestNote = noteNumbers.removeAndReturn(0);
        noteNumbers.add(lowestNote + 12); // 提高八度
    }
}

juce::String ChordData::toString() const
{
    if (isEmpty())
        return "Empty";
    
    return chordName + " (Bar:" + juce::String(barNumber) + 
           ", Beat:" + juce::String(startBeat, 1) + 
           ", Duration:" + juce::String(duration, 1) + ")";
}

bool ChordData::isEmpty() const
{
    return chordName.isEmpty() || chordName == "Empty" || chordName == "?";
}

ChordData ChordData::fromString(const juce::String& chordString)
{
    // 简化的和弦解析 - 实际实现需要更复杂的解析逻辑
    ChordData chord;
    chord.chordName = chordString;
    
    // 基础解析逻辑
    if (chordString.startsWith("C"))
        chord.rootNote = 0;
    else if (chordString.startsWith("D"))
        chord.rootNote = 2;
    else if (chordString.startsWith("E"))
        chord.rootNote = 4;
    else if (chordString.startsWith("F"))
        chord.rootNote = 5;
    else if (chordString.startsWith("G"))
        chord.rootNote = 7;
    else if (chordString.startsWith("A"))
        chord.rootNote = 9;
    else if (chordString.startsWith("B"))
        chord.rootNote = 11;
    
    // 检测和弦类型
    if (chordString.contains("maj7"))
        chord.chordType = ChordType::Major7;
    else if (chordString.contains("m7"))
        chord.chordType = ChordType::Minor7;
    else if (chordString.contains("7"))
        chord.chordType = ChordType::Dominant7;
    else if (chordString.contains("m"))
        chord.chordType = ChordType::Minor;
    else
        chord.chordType = ChordType::Major;
    
    chord.generateNotes();
    return chord;
}

juce::String ChordData::chordTypeToString(ChordType type)
{
    switch (type)
    {
        case ChordType::Major: return "maj";
        case ChordType::Minor: return "m";
        case ChordType::Dominant7: return "7";
        case ChordType::Major7: return "maj7";
        case ChordType::Minor7: return "m7";
        case ChordType::Diminished: return "dim";
        case ChordType::Diminished7: return "dim7";
        case ChordType::HalfDiminished7: return "m7b5";
        case ChordType::Augmented: return "aug";
        case ChordType::Sus2: return "sus2";
        case ChordType::Sus4: return "sus4";
        case ChordType::Add9: return "add9";
        case ChordType::Minor6: return "m6";
        case ChordType::Major6: return "6";
        case ChordType::Ninth: return "9";
        case ChordType::Eleventh: return "11";
        case ChordType::Thirteenth: return "13";
        case ChordType::Altered: return "alt";
        default: return "maj";
    }
}

//==============================================================================
// MusicSection Implementation
//==============================================================================

MusicSection::MusicSection()
    : numberOfBars(8), globalKey(KeySignature::CMajor), 
      globalTimeSignature(4, 4), bpm(120.0f), syncWithDAW(true)
{
}

MusicSection::~MusicSection()
{
}

void MusicSection::addChord(const ChordData& chord)
{
    chords.add(chord);
    sortChords();
}

void MusicSection::removeChord(int index)
{
    if (index >= 0 && index < chords.size())
        chords.remove(index);
}

void MusicSection::updateChord(int index, const ChordData& newChord)
{
    if (index >= 0 && index < chords.size())
    {
        chords.setUnchecked(index, newChord);
        sortChords();
    }
}

ChordData* MusicSection::getChordAt(int bar, float beat)
{
    for (auto& chord : chords)
    {
        if (chord.barNumber == bar && 
            beat >= chord.startBeat && 
            beat < chord.startBeat + chord.duration)
        {
            return &chord;
        }
    }
    return nullptr;
}

void MusicSection::addMusicChange(const MusicChange& change)
{
    changes.add(change);
    sortChanges();
}

KeySignature MusicSection::getKeyAt(int bar, float beat) const
{
    KeySignature currentKey = globalKey;
    
    for (const auto& change : changes)
    {
        if (change.hasKeyChange && 
            (change.barNumber < bar || 
             (change.barNumber == bar && change.beatPosition <= beat)))
        {
            currentKey = change.newKey;
        }
    }
    
    return currentKey;
}

TimeSignature MusicSection::getTimeSignatureAt(int bar, float beat) const
{
    TimeSignature currentTimeSig = globalTimeSignature;
    
    for (const auto& change : changes)
    {
        if (change.hasTimeSignatureChange && 
            (change.barNumber < bar || 
             (change.barNumber == bar && change.beatPosition <= beat)))
        {
            currentTimeSig = change.newTimeSignature;
        }
    }
    
    return currentTimeSig;
}

void MusicSection::clear()
{
    chords.clear();
    changes.clear();
}

juce::MidiMessageSequence MusicSection::generateMidiSequence() const
{
    juce::MidiMessageSequence sequence;
    
    for (const auto& chord : chords)
    {
        if (chord.isEmpty())
            continue;
        
        // 计算时间戳 (简化计算)
        double startTime = (chord.barNumber - 1) * 4.0 + chord.startBeat;
        double endTime = startTime + chord.duration;
        
        // 添加音符
        for (int noteNumber : chord.noteNumbers)
        {
            sequence.addEvent(juce::MidiMessage::noteOn(1, noteNumber, 0.8f), startTime);
            sequence.addEvent(juce::MidiMessage::noteOff(1, noteNumber), endTime);
        }
    }
    
    return sequence;
}

void MusicSection::sortChords()
{
    chords.sort([](const ChordData& a, const ChordData& b)
    {
        if (a.barNumber != b.barNumber)
            return a.barNumber < b.barNumber;
        return a.startBeat < b.startBeat;
    });
}

void MusicSection::sortChanges()
{
    changes.sort([](const MusicChange& a, const MusicChange& b)
    {
        if (a.barNumber != b.barNumber)
            return a.barNumber < b.barNumber;
        return a.beatPosition < b.beatPosition;
    });
}

//==============================================================================
// MusicTheoryUtils Implementation
//==============================================================================

const juce::StringArray MusicTheoryUtils::noteNames = 
{
    "C", "C#", "D", "D#", "E", "F", "F#", "G", "G#", "A", "A#", "B"
};

juce::String MusicTheoryUtils::noteNumberToName(int noteNumber)
{
    return noteNames[noteNumber % 12];
}

int MusicTheoryUtils::noteNameToNumber(const juce::String& noteName)
{
    return noteNames.indexOf(noteName);
}

juce::Array<int> MusicTheoryUtils::getChordIntervals(ChordType type)
{
    switch (type)
    {
        case ChordType::Major: return {0, 4, 7};
        case ChordType::Minor: return {0, 3, 7};
        case ChordType::Dominant7: return {0, 4, 7, 10};
        case ChordType::Major7: return {0, 4, 7, 11};
        case ChordType::Minor7: return {0, 3, 7, 10};
        case ChordType::Diminished: return {0, 3, 6};
        case ChordType::Diminished7: return {0, 3, 6, 9};
        case ChordType::HalfDiminished7: return {0, 3, 6, 10};
        case ChordType::Augmented: return {0, 4, 8};
        case ChordType::Sus2: return {0, 2, 7};
        case ChordType::Sus4: return {0, 5, 7};
        default: return {0, 4, 7};
    }
}

juce::Array<int> MusicTheoryUtils::buildChord(int root, ChordType type, int inversion)
{
    auto intervals = getChordIntervals(type);
    juce::Array<int> notes;
    
    for (int interval : intervals)
    {
        notes.add(root + interval);
    }
    
    // 处理转位
    for (int i = 0; i < inversion && !notes.isEmpty(); ++i)
    {
        int lowestNote = notes.removeAndReturn(0);
        notes.add(lowestNote + 12);
    }
    
    return notes;
}
