/*
  ==============================================================================

    MusicSectionEditor.cpp
    Created: AI Chord Assistant Plugin
    Author:  AI Assistant

    Implementation of the music section editor components.

  ==============================================================================
*/

#include "MusicSectionEditor.h"

//==============================================================================
// ChordSelectorDialog Implementation
//==============================================================================

ChordSelectorDialog::ChordSelectorDialog()
    : currentBar(1), currentBeat(0.0f)
{
    addAndMakeVisible(titleLabel);
    titleLabel.setText("选择和弦", juce::dontSendNotification);
    titleLabel.setFont(juce::Font(16.0f, juce::Font::bold));
    titleLabel.setJustificationType(juce::Justification::centred);

    addAndMakeVisible(searchBox);
    searchBox.setTextToShowWhenEmpty("输入和弦名称...", juce::Colours::grey);
    searchBox.addListener(this);

    addAndMakeVisible(chordList);

    addAndMakeVisible(okButton);
    okButton.setButtonText("确定");
    okButton.addListener(this);

    addAndMakeVisible(cancelButton);
    cancelButton.setButtonText("取消");
    cancelButton.addListener(this);

    populateAvailableChords();
    updateChordList();

    setSize(300, 400);
}

ChordSelectorDialog::~ChordSelectorDialog()
{
}

void ChordSelectorDialog::paint(juce::Graphics& g)
{
    g.fillAll(juce::Colours::lightgrey);
    g.setColour(juce::Colours::black);
    g.drawRect(getLocalBounds(), 2);
}

void ChordSelectorDialog::resized()
{
    auto bounds = getLocalBounds().reduced(10);

    titleLabel.setBounds(bounds.removeFromTop(30));
    bounds.removeFromTop(10);

    searchBox.setBounds(bounds.removeFromTop(25));
    bounds.removeFromTop(10);

    auto buttonArea = bounds.removeFromBottom(30);
    bounds.removeFromBottom(10);

    chordList.setBounds(bounds);

    auto buttonWidth = buttonArea.getWidth() / 2 - 5;
    okButton.setBounds(buttonArea.removeFromLeft(buttonWidth));
    buttonArea.removeFromLeft(10);
    cancelButton.setBounds(buttonArea);
}

void ChordSelectorDialog::showForPosition(int bar, float beat, const ChordData* existingChord)
{
    currentBar = bar;
    currentBeat = beat;

    if (existingChord && !existingChord->isEmpty())
    {
        searchBox.setText(existingChord->chordName);
    }
    else
    {
        searchBox.clear();
    }

    updateChordList();
    setVisible(true);
    searchBox.grabKeyboardFocus();
}

void ChordSelectorDialog::textEditorTextChanged(juce::TextEditor& editor)
{
    updateChordList();
}

void ChordSelectorDialog::textEditorReturnKeyPressed(juce::TextEditor& editor)
{
    if (onChordSelected)
    {
        auto chord = createChordFromSelection();
        onChordSelected(chord);
    }
    setVisible(false);
}

void ChordSelectorDialog::buttonClicked(juce::Button* button)
{
    if (button == &okButton)
    {
        if (onChordSelected)
        {
            auto chord = createChordFromSelection();
            onChordSelected(chord);
        }
        setVisible(false);
    }
    else if (button == &cancelButton)
    {
        if (onCancelled)
            onCancelled();
        setVisible(false);
    }
}

void ChordSelectorDialog::updateChordList()
{
    filteredChords.clear();
    auto searchText = searchBox.getText().toLowerCase();

    if (searchText.isEmpty())
    {
        filteredChords = availableChords;
    }
    else
    {
        for (const auto& chord : availableChords)
        {
            if (chord.toLowerCase().contains(searchText))
                filteredChords.add(chord);
        }
    }

    chordList.updateContent();
}

void ChordSelectorDialog::populateAvailableChords()
{
    // 基础和弦
    juce::StringArray roots = {"C", "C#", "D", "D#", "E", "F", "F#", "G", "G#", "A", "A#", "B"};
    juce::StringArray types = {"", "m", "7", "maj7", "m7", "dim", "aug", "sus2", "sus4"};

    for (const auto& root : roots)
    {
        for (const auto& type : types)
        {
            availableChords.add(root + type);
        }
    }
}

ChordData ChordSelectorDialog::createChordFromSelection()
{
    auto chordName = searchBox.getText();
    if (chordName.isEmpty())
        return ChordData();

    auto chord = ChordData::fromString(chordName);
    chord.barNumber = currentBar;
    chord.startBeat = currentBeat;
    chord.duration = 1.0f; // 默认一拍

    return chord;
}

//==============================================================================
// BarGrid Implementation
//==============================================================================

BarGrid::BarGrid()
    : numberOfBars(8), timeSignature(4, 4), beatsPerBar(4),
      beatWidth(50.0f), barHeight(60.0f), isDragging(false), draggedChordIndex(-1)
{
}

BarGrid::~BarGrid()
{
}

void BarGrid::paint(juce::Graphics& g)
{
    auto bounds = getLocalBounds().toFloat();

    // 背景
    g.fillAll(juce::Colours::white);

    // 绘制网格
    g.setColour(juce::Colours::lightgrey);

    // 垂直线 (拍线)
    for (int bar = 0; bar <= numberOfBars; ++bar)
    {
        for (int beat = 0; beat <= beatsPerBar; ++beat)
        {
            float x = bar * beatsPerBar * beatWidth + beat * beatWidth;
            g.drawVerticalLine(x, 0, barHeight);
        }
    }

    // 水平线
    g.drawHorizontalLine(0, 0, numberOfBars * beatsPerBar * beatWidth);
    g.drawHorizontalLine(barHeight, 0, numberOfBars * beatsPerBar * beatWidth);

    // 小节分隔线 (粗线)
    g.setColour(juce::Colours::black);
    for (int bar = 0; bar <= numberOfBars; ++bar)
    {
        float x = bar * beatsPerBar * beatWidth;
        g.drawVerticalLine(x, 0, barHeight, 2.0f);
    }

    // 绘制和弦
    for (int i = 0; i < chords.size(); ++i)
    {
        const auto& chord = chords.getReference(i);
        if (chord.isEmpty())
            continue;

        auto chordBounds = getChordBounds(chord);

        // 和弦背景
        g.setColour(juce::Colours::lightblue);
        g.fillRect(chordBounds);

        // 和弦边框
        g.setColour(juce::Colours::blue);
        g.drawRect(chordBounds, 1.0f);

        // 和弦文本
        g.setColour(juce::Colours::black);
        g.setFont(12.0f);
        g.drawText(chord.chordName, chordBounds, juce::Justification::centred);
    }

    // 小节号
    g.setColour(juce::Colours::black);
    g.setFont(10.0f);
    for (int bar = 0; bar < numberOfBars; ++bar)
    {
        float x = bar * beatsPerBar * beatWidth + 5;
        g.drawText(juce::String(bar + 1), x, barHeight + 5, 30, 15, juce::Justification::left);
    }
}

void BarGrid::resized()
{
    auto bounds = getLocalBounds();
    beatWidth = bounds.getWidth() / (float)(numberOfBars * beatsPerBar);
    barHeight = bounds.getHeight() - 25; // 留出小节号空间
}

void BarGrid::mouseDown(const juce::MouseEvent& event)
{
    auto point = event.position;
    int chordIndex = findChordAtPosition(point);

    if (chordIndex >= 0)
    {
        // 点击了和弦
        if (onChordClicked)
            onChordClicked(chordIndex);

        isDragging = true;
        draggedChordIndex = chordIndex;
        dragStartPosition = point;
    }
    else
    {
        // 点击了空白位置
        int bar;
        float beat;
        getPositionFromPoint(point, bar, beat);

        if (onPositionClicked)
            onPositionClicked(bar, beat);
    }
}

void BarGrid::mouseDrag(const juce::MouseEvent& event)
{
    if (isDragging && draggedChordIndex >= 0)
    {
        // 拖拽和弦
        repaint();
    }
}

void BarGrid::mouseUp(const juce::MouseEvent& event)
{
    if (isDragging && draggedChordIndex >= 0)
    {
        int bar;
        float beat;
        getPositionFromPoint(event.position, bar, beat);

        if (onChordMoved)
        {
            auto& chord = chords.getReference(draggedChordIndex);
            onChordMoved(draggedChordIndex, bar, beat, chord.duration);
        }
    }

    isDragging = false;
    draggedChordIndex = -1;
}

void BarGrid::setNumberOfBars(int bars)
{
    numberOfBars = bars;
    resized();
    repaint();
}

void BarGrid::setTimeSignature(const TimeSignature& timeSig)
{
    timeSignature = timeSig;
    beatsPerBar = timeSig.numerator;
    resized();
    repaint();
}

void BarGrid::setChords(const juce::Array<ChordData>& newChords)
{
    chords = newChords;
    repaint();
}

void BarGrid::addChord(const ChordData& chord)
{
    chords.add(chord);
    repaint();
}

void BarGrid::removeChord(int index)
{
    if (index >= 0 && index < chords.size())
    {
        chords.remove(index);
        repaint();
    }
}

void BarGrid::updateChord(int index, const ChordData& newChord)
{
    if (index >= 0 && index < chords.size())
    {
        chords.setUnchecked(index, newChord);
        repaint();
    }
}

juce::Rectangle<float> BarGrid::getChordBounds(const ChordData& chord) const
{
    float x = ((chord.barNumber - 1) * beatsPerBar + chord.startBeat) * beatWidth;
    float width = chord.duration * beatWidth;

    return juce::Rectangle<float>(x, 5, width, barHeight - 10);
}

void BarGrid::getPositionFromPoint(juce::Point<float> point, int& bar, float& beat) const
{
    float totalBeats = point.x / beatWidth;
    bar = (int)(totalBeats / beatsPerBar) + 1;
    beat = totalBeats - (bar - 1) * beatsPerBar;

    // 限制范围
    bar = juce::jmax(1, juce::jmin(bar, numberOfBars));
    beat = juce::jmax(0.0f, juce::jmin(beat, (float)beatsPerBar));
}

juce::Point<float> BarGrid::getPointFromPosition(int bar, float beat) const
{
    float x = ((bar - 1) * beatsPerBar + beat) * beatWidth;
    return juce::Point<float>(x, barHeight / 2);
}

int BarGrid::findChordAtPosition(juce::Point<float> point) const
{
    for (int i = 0; i < chords.size(); ++i)
    {
        if (getChordBounds(chords.getReference(i)).contains(point))
            return i;
    }
    return -1;
}

//==============================================================================
// MusicSectionEditor Implementation
//==============================================================================

MusicSectionEditor::MusicSectionEditor()
    : musicSection(nullptr)
{
    addAndMakeVisible(barGrid);
    addChildComponent(chordSelector);
    addAndMakeVisible(midiDragComponent);
    addAndMakeVisible(progressIndicator);

    // 设置回调
    barGrid.onPositionClicked = [this](int bar, float beat) { handlePositionClicked(bar, beat); };
    barGrid.onChordClicked = [this](int index) { handleChordClicked(index); };
    barGrid.onChordMoved = [this](int index, int bar, float beat, float duration) {
        handleChordMoved(index, bar, beat, duration);
    };

    chordSelector.onChordSelected = [this](const ChordData& chord) { handleChordSelected(chord); };
    chordSelector.onCancelled = [this]() { chordSelector.setVisible(false); };

    // MIDI拖拽组件回调
    midiDragComponent.onDragStarted = [this]() { handleMidiDragStarted(); };

    // 进度指示器回调
    progressIndicator.onPositionChanged = [this](double position) { handleProgressPositionChanged(position); };

    setupControls();
    layoutComponents();
}

MusicSectionEditor::~MusicSectionEditor()
{
}

void MusicSectionEditor::paint(juce::Graphics& g)
{
    g.fillAll(juce::Colours::darkgrey);
}

void MusicSectionEditor::resized()
{
    layoutComponents();
}

void MusicSectionEditor::setMusicSection(MusicSection* section)
{
    musicSection = section;
    updateFromMusicSection();
}

void MusicSectionEditor::buttonClicked(juce::Button* button)
{
    if (button == &clearAllButton)
    {
        if (musicSection)
        {
            musicSection->clear();
            updateFromMusicSection();
            if (onSectionChanged)
                onSectionChanged();
        }
    }
    else if (button == &clearSelectionButton)
    {
        // TODO: 实现清除选中和弦的功能
    }
    else if (button == &exportMidiButton)
    {
        handleExportMidi();
    }
}

void MusicSectionEditor::comboBoxChanged(juce::ComboBox* comboBoxThatHasChanged)
{
    if (comboBoxThatHasChanged == &keySelector)
    {
        if (musicSection)
        {
            // TODO: 更新调式
            updateMusicSectionFromUI();
            if (onSectionChanged)
                onSectionChanged();
        }
    }
    else if (comboBoxThatHasChanged == &timeSignatureSelector)
    {
        if (musicSection)
        {
            // TODO: 更新拍号
            updateMusicSectionFromUI();
            if (onSectionChanged)
                onSectionChanged();
        }
    }
}

void MusicSectionEditor::sliderValueChanged(juce::Slider* slider)
{
    if (slider == &barsSlider)
    {
        if (musicSection)
        {
            musicSection->numberOfBars = (int)barsSlider.getValue();
            barGrid.setNumberOfBars(musicSection->numberOfBars);
            if (onSectionChanged)
                onSectionChanged();
        }
    }
}

void MusicSectionEditor::layoutComponents()
{
    auto bounds = getLocalBounds();

    // 控制面板 (顶部)
    auto controlArea = bounds.removeFromTop(100);
    auto row1 = controlArea.removeFromTop(35);
    auto row2 = controlArea.removeFromTop(35);
    auto row3 = controlArea.removeFromTop(25);

    // 第一行
    keyLabel.setBounds(row1.removeFromLeft(60));
    keySelector.setBounds(row1.removeFromLeft(120));
    row1.removeFromLeft(20);

    timeSignatureLabel.setBounds(row1.removeFromLeft(60));
    timeSignatureSelector.setBounds(row1.removeFromLeft(80));
    row1.removeFromLeft(20);

    barsLabel.setBounds(row1.removeFromLeft(60));
    barsSlider.setBounds(row1.removeFromLeft(100));

    // 第二行
    clearAllButton.setBounds(row2.removeFromLeft(100));
    row2.removeFromLeft(10);
    clearSelectionButton.setBounds(row2.removeFromLeft(100));
    row2.removeFromLeft(10);
    exportMidiButton.setBounds(row2.removeFromLeft(100));
    row2.removeFromLeft(20);
    midiDragComponent.setBounds(row2.removeFromLeft(100));

    // 第三行 - 进度指示器
    progressIndicator.setBounds(row3);

    // 主编辑区域
    bounds.removeFromTop(10);
    barGrid.setBounds(bounds);

    // 和弦选择器居中显示
    if (chordSelector.isVisible())
    {
        auto selectorBounds = getLocalBounds();
        chordSelector.setBounds(selectorBounds.withSizeKeepingCentre(300, 400));
    }
}

void MusicSectionEditor::updateFromMusicSection()
{
    if (!musicSection)
        return;

    // 更新控件
    barsSlider.setValue(musicSection->numberOfBars, juce::dontSendNotification);
    barGrid.setNumberOfBars(musicSection->numberOfBars);
    barGrid.setTimeSignature(musicSection->globalTimeSignature);
    barGrid.setChords(musicSection->chords);

    // 更新MIDI拖拽组件和进度指示器
    midiDragComponent.setMusicSection(musicSection);
    progressIndicator.setMusicSection(musicSection);

    repaint();
}

void MusicSectionEditor::updateMusicSectionFromUI()
{
    if (!musicSection)
        return;

    // TODO: 从UI更新音乐段落数据
}

void MusicSectionEditor::handlePositionClicked(int bar, float beat)
{
    chordSelector.showForPosition(bar, beat);
}

void MusicSectionEditor::handleChordClicked(int chordIndex)
{
    if (musicSection && chordIndex >= 0 && chordIndex < musicSection->chords.size())
    {
        const auto& chord = musicSection->chords.getReference(chordIndex);
        chordSelector.showForPosition(chord.barNumber, chord.startBeat, &chord);
    }
}

void MusicSectionEditor::handleChordSelected(const ChordData& chord)
{
    if (!musicSection)
        return;

    // 查找是否已存在相同位置的和弦
    bool found = false;
    for (int i = 0; i < musicSection->chords.size(); ++i)
    {
        auto& existingChord = musicSection->chords.getReference(i);
        if (existingChord.barNumber == chord.barNumber &&
            existingChord.startBeat == chord.startBeat)
        {
            existingChord = chord;
            found = true;
            break;
        }
    }

    if (!found)
    {
        musicSection->addChord(chord);
    }

    updateFromMusicSection();
    if (onSectionChanged)
        onSectionChanged();
}

void MusicSectionEditor::handleChordMoved(int chordIndex, int bar, float beat, float duration)
{
    if (!musicSection || chordIndex < 0 || chordIndex >= musicSection->chords.size())
        return;

    auto& chord = musicSection->chords.getReference(chordIndex);
    chord.barNumber = bar;
    chord.startBeat = beat;
    chord.duration = duration;

    updateFromMusicSection();
    if (onSectionChanged)
        onSectionChanged();
}

void MusicSectionEditor::setupControls()
{
    // 标签
    addAndMakeVisible(keyLabel);
    keyLabel.setText("调式:", juce::dontSendNotification);

    addAndMakeVisible(timeSignatureLabel);
    timeSignatureLabel.setText("拍号:", juce::dontSendNotification);

    addAndMakeVisible(barsLabel);
    barsLabel.setText("小节数:", juce::dontSendNotification);

    // 控件
    addAndMakeVisible(keySelector);
    keySelector.addListener(this);
    populateKeySelector();

    addAndMakeVisible(timeSignatureSelector);
    timeSignatureSelector.addListener(this);
    populateTimeSignatureSelector();

    addAndMakeVisible(barsSlider);
    barsSlider.setRange(1, 32, 1);
    barsSlider.setValue(8);
    barsSlider.addListener(this);

    // 按钮
    addAndMakeVisible(clearAllButton);
    clearAllButton.setButtonText("清空全部");
    clearAllButton.addListener(this);

    addAndMakeVisible(clearSelectionButton);
    clearSelectionButton.setButtonText("清空选区");
    clearSelectionButton.addListener(this);

    addAndMakeVisible(exportMidiButton);
    exportMidiButton.setButtonText("导出MIDI");
    exportMidiButton.addListener(this);
}

void MusicSectionEditor::populateKeySelector()
{
    keySelector.addItem("C大调", 1);
    keySelector.addItem("G大调", 2);
    keySelector.addItem("D大调", 3);
    keySelector.addItem("A大调", 4);
    keySelector.addItem("E大调", 5);
    keySelector.addItem("B大调", 6);
    keySelector.addItem("F#大调", 7);
    keySelector.addItem("C#大调", 8);
    keySelector.addItem("F大调", 9);
    keySelector.addItem("Bb大调", 10);
    keySelector.addItem("Eb大调", 11);
    keySelector.addItem("Ab大调", 12);
    keySelector.addItem("Db大调", 13);
    keySelector.addItem("Gb大调", 14);
    keySelector.addItem("Cb大调", 15);

    keySelector.addSeparator();

    keySelector.addItem("a小调", 16);
    keySelector.addItem("e小调", 17);
    keySelector.addItem("b小调", 18);
    keySelector.addItem("f#小调", 19);
    keySelector.addItem("c#小调", 20);
    keySelector.addItem("g#小调", 21);
    keySelector.addItem("d#小调", 22);
    keySelector.addItem("a#小调", 23);
    keySelector.addItem("d小调", 24);
    keySelector.addItem("g小调", 25);
    keySelector.addItem("c小调", 26);
    keySelector.addItem("f小调", 27);
    keySelector.addItem("bb小调", 28);
    keySelector.addItem("eb小调", 29);
    keySelector.addItem("ab小调", 30);

    keySelector.setSelectedId(1); // 默认C大调
}

void MusicSectionEditor::populateTimeSignatureSelector()
{
    timeSignatureSelector.addItem("4/4", 1);
    timeSignatureSelector.addItem("3/4", 2);
    timeSignatureSelector.addItem("2/4", 3);
    timeSignatureSelector.addItem("6/8", 4);
    timeSignatureSelector.addItem("9/8", 5);
    timeSignatureSelector.addItem("12/8", 6);
    timeSignatureSelector.addItem("5/4", 7);
    timeSignatureSelector.addItem("7/8", 8);

    timeSignatureSelector.setSelectedId(1); // 默认4/4
}

// 新增的事件处理方法
void MusicSectionEditor::handleExportMidi()
{
    if (!musicSection || musicSection->chords.isEmpty())
    {
        juce::AlertWindow::showMessageBoxAsync(
            juce::AlertWindow::InfoIcon,
            "导出MIDI",
            "没有和弦可以导出。请先添加一些和弦。");
        return;
    }

    juce::FileChooser chooser("保存MIDI文件",
                             juce::File::getSpecialLocation(juce::File::userDocumentsDirectory),
                             "*.mid");

    if (chooser.browseForFileToSave(true))
    {
        auto file = chooser.getResult();
        if (MidiExporter::exportToMidiFile(*musicSection, file))
        {
            juce::AlertWindow::showMessageBoxAsync(
                juce::AlertWindow::InfoIcon,
                "导出成功",
                "MIDI文件已保存到: " + file.getFullPathName());
        }
        else
        {
            juce::AlertWindow::showMessageBoxAsync(
                juce::AlertWindow::WarningIcon,
                "导出失败",
                "无法保存MIDI文件。请检查文件路径和权限。");
        }
    }
}

void MusicSectionEditor::handleMidiDragStarted()
{
    // MIDI拖拽开始时的处理
    // 可以在这里添加视觉反馈或其他逻辑
}

void MusicSectionEditor::handleProgressPositionChanged(double position)
{
    // 播放位置改变时的处理
    // 这个回调会传递给外部的播放引擎
    if (onSectionChanged)
        onSectionChanged();
}

//==============================================================================
// PlaybackControls Implementation
//==============================================================================

PlaybackControls::PlaybackControls()
    : isPlaying(false), syncWithDAW(true)
{
    // 播放控制
    addAndMakeVisible(playButton);
    playButton.setButtonText("播放");
    playButton.addListener(this);

    addAndMakeVisible(pauseButton);
    pauseButton.setButtonText("暂停");
    pauseButton.addListener(this);

    addAndMakeVisible(stopButton);
    stopButton.setButtonText("停止");
    stopButton.addListener(this);

    addAndMakeVisible(loopButton);
    loopButton.setButtonText("循环");
    loopButton.addListener(this);

    // BPM控制
    addAndMakeVisible(bpmSlider);
    bpmSlider.setRange(60, 200, 1);
    bpmSlider.setValue(120);
    bpmSlider.setTextBoxStyle(juce::Slider::TextBoxLeft, false, 50, 20);
    bpmSlider.addListener(this);

    addAndMakeVisible(bpmLabel);
    bpmLabel.setText("BPM:", juce::dontSendNotification);

    addAndMakeVisible(syncButton);
    syncButton.setButtonText("同步DAW");
    syncButton.setToggleState(true, juce::dontSendNotification);
    syncButton.addListener(this);

    // 音量控制
    addAndMakeVisible(volumeSlider);
    volumeSlider.setRange(0.0, 1.0, 0.01);
    volumeSlider.setValue(0.7);
    volumeSlider.setTextBoxStyle(juce::Slider::TextBoxLeft, false, 50, 20);
    volumeSlider.addListener(this);

    addAndMakeVisible(volumeLabel);
    volumeLabel.setText("音量:", juce::dontSendNotification);

    updatePlayButton();
    updateBPMControls();

    setSize(400, 80);
}

PlaybackControls::~PlaybackControls()
{
}

void PlaybackControls::paint(juce::Graphics& g)
{
    g.fillAll(juce::Colours::lightgrey);
    g.setColour(juce::Colours::black);
    g.drawRect(getLocalBounds(), 1);
}

void PlaybackControls::resized()
{
    auto bounds = getLocalBounds().reduced(10);
    auto row1 = bounds.removeFromTop(30);
    auto row2 = bounds.removeFromTop(30);

    // 第一行：播放控制
    playButton.setBounds(row1.removeFromLeft(50));
    row1.removeFromLeft(5);
    pauseButton.setBounds(row1.removeFromLeft(50));
    row1.removeFromLeft(5);
    stopButton.setBounds(row1.removeFromLeft(50));
    row1.removeFromLeft(10);
    loopButton.setBounds(row1.removeFromLeft(50));
    row1.removeFromLeft(15);

    syncButton.setBounds(row1.removeFromLeft(80));

    // 第二行：BPM和音量
    bpmLabel.setBounds(row2.removeFromLeft(40));
    bpmSlider.setBounds(row2.removeFromLeft(100));
    row2.removeFromLeft(20);

    volumeLabel.setBounds(row2.removeFromLeft(40));
    volumeSlider.setBounds(row2.removeFromLeft(100));
}

void PlaybackControls::setPlaying(bool playing)
{
    isPlaying = playing;
    updatePlayButton();
}

void PlaybackControls::setBPM(float bpm)
{
    bpmSlider.setValue(bpm, juce::dontSendNotification);
}

void PlaybackControls::setSyncWithDAW(bool sync)
{
    syncWithDAW = sync;
    syncButton.setToggleState(sync, juce::dontSendNotification);
    updateBPMControls();
}

void PlaybackControls::buttonClicked(juce::Button* button)
{
    if (button == &playButton)
    {
        if (onPlayClicked)
            onPlayClicked();
    }
    else if (button == &pauseButton)
    {
        if (onPauseClicked)
            onPauseClicked();
    }
    else if (button == &stopButton)
    {
        if (onStopClicked)
            onStopClicked();
    }
    else if (button == &loopButton)
    {
        if (onLoopChanged)
            onLoopChanged(loopButton.getToggleState());
    }
    else if (button == &syncButton)
    {
        syncWithDAW = syncButton.getToggleState();
        updateBPMControls();
        if (onSyncChanged)
            onSyncChanged(syncWithDAW);
    }
}

void PlaybackControls::sliderValueChanged(juce::Slider* slider)
{
    if (slider == &bpmSlider)
    {
        if (onBPMChanged)
            onBPMChanged((float)bpmSlider.getValue());
    }
    else if (slider == &volumeSlider)
    {
        if (onVolumeChanged)
            onVolumeChanged((float)volumeSlider.getValue());
    }
}

void PlaybackControls::updatePlayButton()
{
    playButton.setButtonText(isPlaying ? "暂停" : "播放");
}

void PlaybackControls::updateBPMControls()
{
    bpmSlider.setEnabled(!syncWithDAW);
    bpmLabel.setEnabled(!syncWithDAW);
}
