// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 46;
	objects = {

/* Begin PBXAggregateTarget section */
		6E4B51A85131C7C7E30AD586 /* Color Assistant - All */ = {
			isa = PBXAggregateTarget;
			buildConfigurationList = 436A3F4426EF0B829BF64EB0;
			buildPhases = (
			);
			dependencies = (
				6D722F3E40F57D0909F1275C,
				922F5A5769E1AC09D308C9E0,
				BFA91DCD1DD9A9A05645C38F,
				C1789E359A99F8E52E5A231F,
				E6EF53C20D1E8D347CA58B9D,
			);
			name = "Color Assistant - All";
			productName = "Color Assistant";
		};
/* End PBXAggregateTarget section */

/* Begin PBXBuildFile section */
		0A0637BF3E5F4694F345309E /* include_juce_audio_plugin_client_AU_1.mm */ = {isa = PBXBuildFile; fileRef = 845F096AA774C62328F1E727; };
		0B17D12D2428985D57D21678 /* AudioToolbox.framework */ = {isa = PBXBuildFile; fileRef = CA8247EAA900B5D0104B8F09; };
		10F678020464D79A41735800 /* DiscRecording.framework */ = {isa = PBXBuildFile; fileRef = 3451F6B8BC3C25D03BAD8264; };
		1AACBC2A039E1FD0C9FABBA2 /* include_juce_graphics_Sheenbidi.c */ = {isa = PBXBuildFile; fileRef = 79857B33D6E3E988F21C475C; };
		1C0ED2736AAC756289D43772 /* Shared Code */ = {isa = PBXBuildFile; fileRef = C72128CD83BAFC164C054E1B; };
		25B6706673F326FEEDAB43B3 /* include_juce_events.mm */ = {isa = PBXBuildFile; fileRef = 463C38A12EF2CF257D90DC2A; };
		25D9569F8838B1290B09B070 /* include_juce_audio_basics.mm */ = {isa = PBXBuildFile; fileRef = 65E920E9D33DD2B432BBB82C; };
		2F03B6D71B4B2C41729F9D6E /* PluginEditor.cpp */ = {isa = PBXBuildFile; fileRef = 37D85001CA6218BB6BB84201; };
		3424F160667C48625988EEA7 /* include_juce_audio_processors_lv2_libs.cpp */ = {isa = PBXBuildFile; fileRef = 86EF7CF7061CCCFC24CB4AE0; };
		42B0F08D4CD4B51E65808094 /* include_juce_audio_processors_ara.cpp */ = {isa = PBXBuildFile; fileRef = A7A08C2F20F0B860B4CD4B56; };
		442EDAF791C3056C71A2A806 /* Security.framework */ = {isa = PBXBuildFile; fileRef = 685B4C50764BA4210E7756EF; };
		45342DD43E53776252C10923 /* include_juce_audio_plugin_client_AU_2.mm */ = {isa = PBXBuildFile; fileRef = A06A73D334549FB137404F1E; };
		47ADC7538DA77EA692434D85 /* include_juce_audio_plugin_client_ARA.cpp */ = {isa = PBXBuildFile; fileRef = 6EF8F205FE6398022CFAC837; };
		4E492FF3A3D97C46A5B3C5CB /* AU */ = {isa = PBXBuildFile; fileRef = 47C4A60F1F69F694D0FF5694; };
		52F09D65D2C3211E0CC384B6 /* include_juce_midi_ci.cpp */ = {isa = PBXBuildFile; fileRef = 9B05671A3B989EFE4B1F08A9; };
		57E16CD8522D929AE7E23271 /* include_juce_core.mm */ = {isa = PBXBuildFile; fileRef = 44C3BF82C6F4A8AC496DAB9B; };
		6B9C320A57E4443A38E1EC74 /* Accelerate.framework */ = {isa = PBXBuildFile; fileRef = 7D6CAF4F855AE1E927225C9E; };
		6BB5F64F5D161E39ECBC257C /* include_juce_audio_devices.mm */ = {isa = PBXBuildFile; fileRef = AFE9F6AF41B8B4EA63AFE220; };
		730CAA560B4BE5D33A6AFDEF /* RecentFilesMenuTemplate.nib */ = {isa = PBXBuildFile; fileRef = 2725852D1C93F2DD429038E0; };
		749FF5B4553624853228E8A3 /* include_juce_analytics.cpp */ = {isa = PBXBuildFile; fileRef = 014E9AF5D76D018F6BAB20BE; };
		774D5ED02208F7AD2A94377B /* CoreAudio.framework */ = {isa = PBXBuildFile; fileRef = A02610C7BB82F09967E125D1; };
		787DEDF74ECE9CB8AA7CE204 /* Foundation.framework */ = {isa = PBXBuildFile; fileRef = 58EC98DB7096287C1D92DD67; };
		78C47D266AF0B83ECE0E091D /* include_juce_audio_plugin_client_VST3.mm */ = {isa = PBXBuildFile; fileRef = 3044619109982A1AD1252544; };
		7C4EE1897BF1C5E1EB8937EA /* Standalone Plugin */ = {isa = PBXBuildFile; fileRef = EE21D3146AC835CACDE8E5CC; };
		81BCC25803CC842EE0357CBB /* CoreMIDI.framework */ = {isa = PBXBuildFile; fileRef = 02D7CED48C8759794D30FA5A; };
		860F517283D5D32638DE40EA /* include_juce_gui_extra.mm */ = {isa = PBXBuildFile; fileRef = 889562836CB3049E1F8EA672; };
		864ACF0DF64A8C3DD612099F /* MetalKit.framework */ = {isa = PBXBuildFile; fileRef = 381261E585055F4CD5774C3F; settings = { ATTRIBUTES = (Weak, ); }; };
		8AA2DDD6376395F4640A7831 /* include_juce_audio_processors.mm */ = {isa = PBXBuildFile; fileRef = 08F04CC803E053B21B7D2446; };
		8B09D84A4746D24D18D8C5C9 /* include_juce_audio_utils.mm */ = {isa = PBXBuildFile; fileRef = 87312FCA91CFBD9D8CA251AF; };
		9322753E8F0EF56900D777CA /* CoreAudioKit.framework */ = {isa = PBXBuildFile; fileRef = 91C94675A45DFEEC8636E541; };
		9648F25F6D480BA883444F29 /* include_juce_data_structures.mm */ = {isa = PBXBuildFile; fileRef = 97C5BB958493CA411A43CB56; };
		9D7134766DA64B85DDB332F3 /* include_juce_dsp.mm */ = {isa = PBXBuildFile; fileRef = 35549C85EE0490623EF63371; };
		A75C693B478ACA9FBB18F709 /* VST3 Manifest Helper */ = {isa = PBXBuildFile; fileRef = 7A5D5E39D253FFBC84A16F78; };
		A9C12D4A95C00A521098655B /* Cocoa.framework */ = {isa = PBXBuildFile; fileRef = 5BA903104DD4BBF9E850F2DF; };
		AADBAD484A023EF2B3A07202 /* WebKit.framework */ = {isa = PBXBuildFile; fileRef = E1BD1BE8639D46DC8D426E70; };
		B4AB1DEED65553815617BA9E /* include_juce_osc.cpp */ = {isa = PBXBuildFile; fileRef = E7AAE346BC776A36BDF0B65F; };
		B7E06EEE0B1C8AC929266A10 /* include_juce_gui_basics.mm */ = {isa = PBXBuildFile; fileRef = 40CE1CB910D9AED26C2681A7; };
		BA2AB00BE8D40565EC756B9C /* PluginProcessor.cpp */ = {isa = PBXBuildFile; fileRef = BE72B8D14C71E8056DC2F684; };
		BA6C8E3AA4FCB52FDCF3C705 /* IOKit.framework */ = {isa = PBXBuildFile; fileRef = FD854BC0AFEE193D648CA81E; };
		BC993CA3DAB30D966F6A1AE4 /* include_juce_core_CompilationTime.cpp */ = {isa = PBXBuildFile; fileRef = 9A0CB92AA5215D6AC4F134C8; };
		C116443776A0F61ED6DE67E7 /* VST3 */ = {isa = PBXBuildFile; fileRef = E0E94162A2A6013CF8A62D87; };
		C23CB7A3202E25C7B2CB99BA /* include_juce_animation.cpp */ = {isa = PBXBuildFile; fileRef = 7C311D32391D8740586F6A14; };
		CAF6E3E3ED2217DDAE388270 /* Metal.framework */ = {isa = PBXBuildFile; fileRef = 2DCD8A35DA007957889CD902; settings = { ATTRIBUTES = (Weak, ); }; };
		D5235AFB8F551D6B6BF2048F /* AudioUnit.framework */ = {isa = PBXBuildFile; fileRef = 24F134899B7CF75A5AE53E22; };
		D7124CDC0C06A4FE5C960DC7 /* include_juce_audio_plugin_client_Standalone.cpp */ = {isa = PBXBuildFile; fileRef = CEE15D9DCF67A209ED08C383; };
		DEBFAF7EA6BDD91F9F1684E7 /* juce_VST3ManifestHelper.mm */ = {isa = PBXBuildFile; fileRef = C18E6A2A974A16F220819695; settings = { COMPILER_FLAGS = "-fobjc-arc -w -DJUCE_SKIP_PRECOMPILED_HEADER"; }; };
		EB256F5D318E13B1FBAFD6E6 /* include_juce_graphics_Harfbuzz.cpp */ = {isa = PBXBuildFile; fileRef = 5A1BFEFEC722AF0C1641C764; };
		F251D7E336DE786FAD9AF7B9 /* include_juce_audio_formats.mm */ = {isa = PBXBuildFile; fileRef = A69778E92012AF065D21A66B; };
		F49607D7BAD3A86A4BF3003A /* QuartzCore.framework */ = {isa = PBXBuildFile; fileRef = 4A26658E36120000EACFDF90; };
		FF2F0F778A1D6CE20C3CE348 /* include_juce_graphics.mm */ = {isa = PBXBuildFile; fileRef = 31D6ADFB61E8C2F47FDCD01C; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		014E9AF5D76D018F6BAB20BE /* include_juce_analytics.cpp */ /* include_juce_analytics.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = include_juce_analytics.cpp; path = ../../JuceLibraryCode/include_juce_analytics.cpp; sourceTree = SOURCE_ROOT; };
		02D7CED48C8759794D30FA5A /* CoreMIDI.framework */ /* CoreMIDI.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreMIDI.framework; path = System/Library/Frameworks/CoreMIDI.framework; sourceTree = SDKROOT; };
		0337A7C8DFF9F45655A28EAE /* juce_gui_basics */ /* juce_gui_basics */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_gui_basics; path = "~/JUCE/modules/juce_gui_basics"; sourceTree = "<absolute>"; };
		07EBDF94D9237B0C1C3316EE /* juce_osc */ /* juce_osc */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_osc; path = "~/JUCE/modules/juce_osc"; sourceTree = "<absolute>"; };
		08F04CC803E053B21B7D2446 /* include_juce_audio_processors.mm */ /* include_juce_audio_processors.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = include_juce_audio_processors.mm; path = ../../JuceLibraryCode/include_juce_audio_processors.mm; sourceTree = SOURCE_ROOT; };
		09F73D4F9174B02DA0CCEB5E /* juce_events */ /* juce_events */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_events; path = "~/JUCE/modules/juce_events"; sourceTree = "<absolute>"; };
		14D15479CC3E82105DE29877 /* Info-VST3_Manifest_Helper.plist */ /* Info-VST3_Manifest_Helper.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; name = "Info-VST3_Manifest_Helper.plist"; path = "Info-VST3_Manifest_Helper.plist"; sourceTree = SOURCE_ROOT; };
		24F134899B7CF75A5AE53E22 /* AudioUnit.framework */ /* AudioUnit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AudioUnit.framework; path = System/Library/Frameworks/AudioUnit.framework; sourceTree = SDKROOT; };
		2725852D1C93F2DD429038E0 /* RecentFilesMenuTemplate.nib */ /* RecentFilesMenuTemplate.nib */ = {isa = PBXFileReference; lastKnownFileType = file.nib; name = RecentFilesMenuTemplate.nib; path = RecentFilesMenuTemplate.nib; sourceTree = SOURCE_ROOT; };
		2DCD8A35DA007957889CD902 /* Metal.framework */ /* Metal.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Metal.framework; path = System/Library/Frameworks/Metal.framework; sourceTree = SDKROOT; };
		3044619109982A1AD1252544 /* include_juce_audio_plugin_client_VST3.mm */ /* include_juce_audio_plugin_client_VST3.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = include_juce_audio_plugin_client_VST3.mm; path = ../../JuceLibraryCode/include_juce_audio_plugin_client_VST3.mm; sourceTree = SOURCE_ROOT; };
		3093B061C9454A6CDA0DBB03 /* juce_gui_extra */ /* juce_gui_extra */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_gui_extra; path = "~/JUCE/modules/juce_gui_extra"; sourceTree = "<absolute>"; };
		31D6ADFB61E8C2F47FDCD01C /* include_juce_graphics.mm */ /* include_juce_graphics.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = include_juce_graphics.mm; path = ../../JuceLibraryCode/include_juce_graphics.mm; sourceTree = SOURCE_ROOT; };
		3451F6B8BC3C25D03BAD8264 /* DiscRecording.framework */ /* DiscRecording.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = DiscRecording.framework; path = System/Library/Frameworks/DiscRecording.framework; sourceTree = SDKROOT; };
		35549C85EE0490623EF63371 /* include_juce_dsp.mm */ /* include_juce_dsp.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = include_juce_dsp.mm; path = ../../JuceLibraryCode/include_juce_dsp.mm; sourceTree = SOURCE_ROOT; };
		3750C4E4C7414B9987496E2F /* Info-Standalone_Plugin.plist */ /* Info-Standalone_Plugin.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; name = "Info-Standalone_Plugin.plist"; path = "Info-Standalone_Plugin.plist"; sourceTree = SOURCE_ROOT; };
		37D85001CA6218BB6BB84201 /* PluginEditor.cpp */ /* PluginEditor.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = PluginEditor.cpp; path = ../../Source/PluginEditor.cpp; sourceTree = SOURCE_ROOT; };
		381261E585055F4CD5774C3F /* MetalKit.framework */ /* MetalKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = MetalKit.framework; path = System/Library/Frameworks/MetalKit.framework; sourceTree = SDKROOT; };
		40CE1CB910D9AED26C2681A7 /* include_juce_gui_basics.mm */ /* include_juce_gui_basics.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = include_juce_gui_basics.mm; path = ../../JuceLibraryCode/include_juce_gui_basics.mm; sourceTree = SOURCE_ROOT; };
		44C3BF82C6F4A8AC496DAB9B /* include_juce_core.mm */ /* include_juce_core.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = include_juce_core.mm; path = ../../JuceLibraryCode/include_juce_core.mm; sourceTree = SOURCE_ROOT; };
		463C38A12EF2CF257D90DC2A /* include_juce_events.mm */ /* include_juce_events.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = include_juce_events.mm; path = ../../JuceLibraryCode/include_juce_events.mm; sourceTree = SOURCE_ROOT; };
		47C4A60F1F69F694D0FF5694 /* AU */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = "Color Assistant.component"; sourceTree = BUILT_PRODUCTS_DIR; };
		4A26658E36120000EACFDF90 /* QuartzCore.framework */ /* QuartzCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = QuartzCore.framework; path = System/Library/Frameworks/QuartzCore.framework; sourceTree = SDKROOT; };
		4B38E0F5075317ADF6343EC8 /* juce_analytics */ /* juce_analytics */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_analytics; path = "~/JUCE/modules/juce_analytics"; sourceTree = "<absolute>"; };
		506C41BEE312D3ADD6A65750 /* juce_animation */ /* juce_animation */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_animation; path = "~/JUCE/modules/juce_animation"; sourceTree = "<absolute>"; };
		5074E138FC8C2FA754CFDAB3 /* juce_graphics */ /* juce_graphics */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_graphics; path = "~/JUCE/modules/juce_graphics"; sourceTree = "<absolute>"; };
		54ABB47C0A242C7444B4C14D /* Info-VST3.plist */ /* Info-VST3.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; name = "Info-VST3.plist"; path = "Info-VST3.plist"; sourceTree = SOURCE_ROOT; };
		58EC98DB7096287C1D92DD67 /* Foundation.framework */ /* Foundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Foundation.framework; path = System/Library/Frameworks/Foundation.framework; sourceTree = SDKROOT; };
		5A1BFEFEC722AF0C1641C764 /* include_juce_graphics_Harfbuzz.cpp */ /* include_juce_graphics_Harfbuzz.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = include_juce_graphics_Harfbuzz.cpp; path = ../../JuceLibraryCode/include_juce_graphics_Harfbuzz.cpp; sourceTree = SOURCE_ROOT; };
		5BA903104DD4BBF9E850F2DF /* Cocoa.framework */ /* Cocoa.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Cocoa.framework; path = System/Library/Frameworks/Cocoa.framework; sourceTree = SDKROOT; };
		65E920E9D33DD2B432BBB82C /* include_juce_audio_basics.mm */ /* include_juce_audio_basics.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = include_juce_audio_basics.mm; path = ../../JuceLibraryCode/include_juce_audio_basics.mm; sourceTree = SOURCE_ROOT; };
		685B4C50764BA4210E7756EF /* Security.framework */ /* Security.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Security.framework; path = System/Library/Frameworks/Security.framework; sourceTree = SDKROOT; };
		6EF8F205FE6398022CFAC837 /* include_juce_audio_plugin_client_ARA.cpp */ /* include_juce_audio_plugin_client_ARA.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = include_juce_audio_plugin_client_ARA.cpp; path = ../../JuceLibraryCode/include_juce_audio_plugin_client_ARA.cpp; sourceTree = SOURCE_ROOT; };
		6F1B002F80090E086E6ECDCD /* juce_audio_basics */ /* juce_audio_basics */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_audio_basics; path = "~/JUCE/modules/juce_audio_basics"; sourceTree = "<absolute>"; };
		79857B33D6E3E988F21C475C /* include_juce_graphics_Sheenbidi.c */ /* include_juce_graphics_Sheenbidi.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = include_juce_graphics_Sheenbidi.c; path = ../../JuceLibraryCode/include_juce_graphics_Sheenbidi.c; sourceTree = SOURCE_ROOT; };
		7A5D5E39D253FFBC84A16F78 /* VST3 Manifest Helper */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = juce_vst3_helper; sourceTree = BUILT_PRODUCTS_DIR; };
		7C311D32391D8740586F6A14 /* include_juce_animation.cpp */ /* include_juce_animation.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = include_juce_animation.cpp; path = ../../JuceLibraryCode/include_juce_animation.cpp; sourceTree = SOURCE_ROOT; };
		7D6CAF4F855AE1E927225C9E /* Accelerate.framework */ /* Accelerate.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Accelerate.framework; path = System/Library/Frameworks/Accelerate.framework; sourceTree = SDKROOT; };
		7DE84D92CC718D682B6A4EDE /* Info-AU.plist */ /* Info-AU.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; name = "Info-AU.plist"; path = "Info-AU.plist"; sourceTree = SOURCE_ROOT; };
		80783ACD05C82D16B450DFC8 /* juce_audio_utils */ /* juce_audio_utils */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_audio_utils; path = "~/JUCE/modules/juce_audio_utils"; sourceTree = "<absolute>"; };
		845F096AA774C62328F1E727 /* include_juce_audio_plugin_client_AU_1.mm */ /* include_juce_audio_plugin_client_AU_1.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = include_juce_audio_plugin_client_AU_1.mm; path = ../../JuceLibraryCode/include_juce_audio_plugin_client_AU_1.mm; sourceTree = SOURCE_ROOT; };
		86EF7CF7061CCCFC24CB4AE0 /* include_juce_audio_processors_lv2_libs.cpp */ /* include_juce_audio_processors_lv2_libs.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = include_juce_audio_processors_lv2_libs.cpp; path = ../../JuceLibraryCode/include_juce_audio_processors_lv2_libs.cpp; sourceTree = SOURCE_ROOT; };
		87312FCA91CFBD9D8CA251AF /* include_juce_audio_utils.mm */ /* include_juce_audio_utils.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = include_juce_audio_utils.mm; path = ../../JuceLibraryCode/include_juce_audio_utils.mm; sourceTree = SOURCE_ROOT; };
		889562836CB3049E1F8EA672 /* include_juce_gui_extra.mm */ /* include_juce_gui_extra.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = include_juce_gui_extra.mm; path = ../../JuceLibraryCode/include_juce_gui_extra.mm; sourceTree = SOURCE_ROOT; };
		91C94675A45DFEEC8636E541 /* CoreAudioKit.framework */ /* CoreAudioKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreAudioKit.framework; path = System/Library/Frameworks/CoreAudioKit.framework; sourceTree = SDKROOT; };
		97C5BB958493CA411A43CB56 /* include_juce_data_structures.mm */ /* include_juce_data_structures.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = include_juce_data_structures.mm; path = ../../JuceLibraryCode/include_juce_data_structures.mm; sourceTree = SOURCE_ROOT; };
		9A0CB92AA5215D6AC4F134C8 /* include_juce_core_CompilationTime.cpp */ /* include_juce_core_CompilationTime.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = include_juce_core_CompilationTime.cpp; path = ../../JuceLibraryCode/include_juce_core_CompilationTime.cpp; sourceTree = SOURCE_ROOT; };
		9B05671A3B989EFE4B1F08A9 /* include_juce_midi_ci.cpp */ /* include_juce_midi_ci.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = include_juce_midi_ci.cpp; path = ../../JuceLibraryCode/include_juce_midi_ci.cpp; sourceTree = SOURCE_ROOT; };
		A02610C7BB82F09967E125D1 /* CoreAudio.framework */ /* CoreAudio.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreAudio.framework; path = System/Library/Frameworks/CoreAudio.framework; sourceTree = SDKROOT; };
		A06A73D334549FB137404F1E /* include_juce_audio_plugin_client_AU_2.mm */ /* include_juce_audio_plugin_client_AU_2.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = include_juce_audio_plugin_client_AU_2.mm; path = ../../JuceLibraryCode/include_juce_audio_plugin_client_AU_2.mm; sourceTree = SOURCE_ROOT; };
		A69778E92012AF065D21A66B /* include_juce_audio_formats.mm */ /* include_juce_audio_formats.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = include_juce_audio_formats.mm; path = ../../JuceLibraryCode/include_juce_audio_formats.mm; sourceTree = SOURCE_ROOT; };
		A7A08C2F20F0B860B4CD4B56 /* include_juce_audio_processors_ara.cpp */ /* include_juce_audio_processors_ara.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = include_juce_audio_processors_ara.cpp; path = ../../JuceLibraryCode/include_juce_audio_processors_ara.cpp; sourceTree = SOURCE_ROOT; };
		A82639EF942D36F0926DF839 /* PluginEditor.h */ /* PluginEditor.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = PluginEditor.h; path = ../../Source/PluginEditor.h; sourceTree = SOURCE_ROOT; };
		AFE9F6AF41B8B4EA63AFE220 /* include_juce_audio_devices.mm */ /* include_juce_audio_devices.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = include_juce_audio_devices.mm; path = ../../JuceLibraryCode/include_juce_audio_devices.mm; sourceTree = SOURCE_ROOT; };
		B0B54D716EC5A8A440491DA8 /* juce_midi_ci */ /* juce_midi_ci */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_midi_ci; path = "~/JUCE/modules/juce_midi_ci"; sourceTree = "<absolute>"; };
		B36A6F303F23FD76D0ED9695 /* JucePluginDefines.h */ /* JucePluginDefines.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = JucePluginDefines.h; path = ../../JuceLibraryCode/JucePluginDefines.h; sourceTree = SOURCE_ROOT; };
		B53D1B0A8275B5CE7FE225A1 /* juce_dsp */ /* juce_dsp */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_dsp; path = "~/JUCE/modules/juce_dsp"; sourceTree = "<absolute>"; };
		B8195326CEC85A395553F239 /* PluginProcessor.h */ /* PluginProcessor.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = PluginProcessor.h; path = ../../Source/PluginProcessor.h; sourceTree = SOURCE_ROOT; };
		BE72B8D14C71E8056DC2F684 /* PluginProcessor.cpp */ /* PluginProcessor.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = PluginProcessor.cpp; path = ../../Source/PluginProcessor.cpp; sourceTree = SOURCE_ROOT; };
		C18E6A2A974A16F220819695 /* juce_VST3ManifestHelper.mm */ /* juce_VST3ManifestHelper.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = juce_VST3ManifestHelper.mm; path = "$(HOME)/JUCE/modules/juce_audio_plugin_client/VST3/juce_VST3ManifestHelper.mm"; sourceTree = "<absolute>"; };
		C72128CD83BAFC164C054E1B /* Shared Code */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libColor Assistant.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		CA8247EAA900B5D0104B8F09 /* AudioToolbox.framework */ /* AudioToolbox.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AudioToolbox.framework; path = System/Library/Frameworks/AudioToolbox.framework; sourceTree = SDKROOT; };
		CCBA29715F353C835ABCAA09 /* juce_data_structures */ /* juce_data_structures */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_data_structures; path = "~/JUCE/modules/juce_data_structures"; sourceTree = "<absolute>"; };
		CEE15D9DCF67A209ED08C383 /* include_juce_audio_plugin_client_Standalone.cpp */ /* include_juce_audio_plugin_client_Standalone.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = include_juce_audio_plugin_client_Standalone.cpp; path = ../../JuceLibraryCode/include_juce_audio_plugin_client_Standalone.cpp; sourceTree = SOURCE_ROOT; };
		D2CF50B9052DCC83565E7C4C /* juce_audio_processors */ /* juce_audio_processors */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_audio_processors; path = "~/JUCE/modules/juce_audio_processors"; sourceTree = "<absolute>"; };
		DC8A3BB0BF7E37FF4C721D4E /* juce_audio_plugin_client */ /* juce_audio_plugin_client */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_audio_plugin_client; path = "~/JUCE/modules/juce_audio_plugin_client"; sourceTree = "<absolute>"; };
		E0E94162A2A6013CF8A62D87 /* VST3 */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = "Color Assistant.vst3"; sourceTree = BUILT_PRODUCTS_DIR; };
		E1BD1BE8639D46DC8D426E70 /* WebKit.framework */ /* WebKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = WebKit.framework; path = System/Library/Frameworks/WebKit.framework; sourceTree = SDKROOT; };
		E3A40CB5C2ED3EC123EF6350 /* juce_core */ /* juce_core */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_core; path = "~/JUCE/modules/juce_core"; sourceTree = "<absolute>"; };
		E4A5A71F593CD6AAE25D2087 /* JuceHeader.h */ /* JuceHeader.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = JuceHeader.h; path = ../../JuceLibraryCode/JuceHeader.h; sourceTree = SOURCE_ROOT; };
		E7AAE346BC776A36BDF0B65F /* include_juce_osc.cpp */ /* include_juce_osc.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = include_juce_osc.cpp; path = ../../JuceLibraryCode/include_juce_osc.cpp; sourceTree = SOURCE_ROOT; };
		EDD7BD1CD3B80FC36B70C00C /* juce_audio_devices */ /* juce_audio_devices */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_audio_devices; path = "~/JUCE/modules/juce_audio_devices"; sourceTree = "<absolute>"; };
		EE21D3146AC835CACDE8E5CC /* Standalone Plugin */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "Color Assistant.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		F3247AA79DBF950A2023E057 /* juce_audio_formats */ /* juce_audio_formats */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_audio_formats; path = "~/JUCE/modules/juce_audio_formats"; sourceTree = "<absolute>"; };
		FD854BC0AFEE193D648CA81E /* IOKit.framework */ /* IOKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = IOKit.framework; path = System/Library/Frameworks/IOKit.framework; sourceTree = SDKROOT; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		31A50FE87C82E9BD1FA3C907 = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				6B9C320A57E4443A38E1EC74,
				0B17D12D2428985D57D21678,
				A9C12D4A95C00A521098655B,
				774D5ED02208F7AD2A94377B,
				9322753E8F0EF56900D777CA,
				81BCC25803CC842EE0357CBB,
				10F678020464D79A41735800,
				787DEDF74ECE9CB8AA7CE204,
				BA6C8E3AA4FCB52FDCF3C705,
				F49607D7BAD3A86A4BF3003A,
				442EDAF791C3056C71A2A806,
				AADBAD484A023EF2B3A07202,
				CAF6E3E3ED2217DDAE388270,
				864ACF0DF64A8C3DD612099F,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		39E11F5A2B5E11E82871F334 = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				6B9C320A57E4443A38E1EC74,
				0B17D12D2428985D57D21678,
				A9C12D4A95C00A521098655B,
				774D5ED02208F7AD2A94377B,
				9322753E8F0EF56900D777CA,
				81BCC25803CC842EE0357CBB,
				10F678020464D79A41735800,
				787DEDF74ECE9CB8AA7CE204,
				BA6C8E3AA4FCB52FDCF3C705,
				F49607D7BAD3A86A4BF3003A,
				442EDAF791C3056C71A2A806,
				AADBAD484A023EF2B3A07202,
				CAF6E3E3ED2217DDAE388270,
				864ACF0DF64A8C3DD612099F,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		69CE84BBB665D49F7294DE22 = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				6B9C320A57E4443A38E1EC74,
				0B17D12D2428985D57D21678,
				A9C12D4A95C00A521098655B,
				774D5ED02208F7AD2A94377B,
				9322753E8F0EF56900D777CA,
				81BCC25803CC842EE0357CBB,
				10F678020464D79A41735800,
				787DEDF74ECE9CB8AA7CE204,
				BA6C8E3AA4FCB52FDCF3C705,
				F49607D7BAD3A86A4BF3003A,
				442EDAF791C3056C71A2A806,
				AADBAD484A023EF2B3A07202,
				CAF6E3E3ED2217DDAE388270,
				864ACF0DF64A8C3DD612099F,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B07E4A9B665FD7374B12F4F3 = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				D5235AFB8F551D6B6BF2048F,
				6B9C320A57E4443A38E1EC74,
				0B17D12D2428985D57D21678,
				A9C12D4A95C00A521098655B,
				774D5ED02208F7AD2A94377B,
				9322753E8F0EF56900D777CA,
				81BCC25803CC842EE0357CBB,
				10F678020464D79A41735800,
				787DEDF74ECE9CB8AA7CE204,
				BA6C8E3AA4FCB52FDCF3C705,
				F49607D7BAD3A86A4BF3003A,
				442EDAF791C3056C71A2A806,
				AADBAD484A023EF2B3A07202,
				CAF6E3E3ED2217DDAE388270,
				864ACF0DF64A8C3DD612099F,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		2514328A14843166437C6931 /* Color Assistant */ = {
			isa = PBXGroup;
			children = (
				AB86ABEFDEE1CBC5B0FF6CC8,
			);
			name = "Color Assistant";
			sourceTree = "<group>";
		};
		345774711EE3962F8EE72973 /* Source */ = {
			isa = PBXGroup;
			children = (
				2514328A14843166437C6931,
				AE93DFE8AF9090F6EB1303EB,
				60EB97AC9C57BC905A3014EA,
				8165CC2FD64483BC77157205,
				4F17EA8DEDDA10037B563E67,
				FCD6A76A3FFF5F4E3B8C6F1D,
			);
			name = Source;
			sourceTree = "<group>";
		};
		4F17EA8DEDDA10037B563E67 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				24F134899B7CF75A5AE53E22,
				7D6CAF4F855AE1E927225C9E,
				CA8247EAA900B5D0104B8F09,
				5BA903104DD4BBF9E850F2DF,
				A02610C7BB82F09967E125D1,
				91C94675A45DFEEC8636E541,
				02D7CED48C8759794D30FA5A,
				3451F6B8BC3C25D03BAD8264,
				58EC98DB7096287C1D92DD67,
				FD854BC0AFEE193D648CA81E,
				4A26658E36120000EACFDF90,
				685B4C50764BA4210E7756EF,
				E1BD1BE8639D46DC8D426E70,
				2DCD8A35DA007957889CD902,
				381261E585055F4CD5774C3F,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		60EB97AC9C57BC905A3014EA /* JUCE Library Code */ = {
			isa = PBXGroup;
			children = (
				014E9AF5D76D018F6BAB20BE,
				7C311D32391D8740586F6A14,
				65E920E9D33DD2B432BBB82C,
				AFE9F6AF41B8B4EA63AFE220,
				A69778E92012AF065D21A66B,
				6EF8F205FE6398022CFAC837,
				845F096AA774C62328F1E727,
				A06A73D334549FB137404F1E,
				CEE15D9DCF67A209ED08C383,
				3044619109982A1AD1252544,
				08F04CC803E053B21B7D2446,
				A7A08C2F20F0B860B4CD4B56,
				86EF7CF7061CCCFC24CB4AE0,
				87312FCA91CFBD9D8CA251AF,
				44C3BF82C6F4A8AC496DAB9B,
				9A0CB92AA5215D6AC4F134C8,
				97C5BB958493CA411A43CB56,
				35549C85EE0490623EF63371,
				463C38A12EF2CF257D90DC2A,
				31D6ADFB61E8C2F47FDCD01C,
				5A1BFEFEC722AF0C1641C764,
				79857B33D6E3E988F21C475C,
				40CE1CB910D9AED26C2681A7,
				889562836CB3049E1F8EA672,
				9B05671A3B989EFE4B1F08A9,
				E7AAE346BC776A36BDF0B65F,
				E4A5A71F593CD6AAE25D2087,
				B36A6F303F23FD76D0ED9695,
			);
			name = "JUCE Library Code";
			sourceTree = "<group>";
		};
		8165CC2FD64483BC77157205 /* Resources */ = {
			isa = PBXGroup;
			children = (
				54ABB47C0A242C7444B4C14D,
				7DE84D92CC718D682B6A4EDE,
				3750C4E4C7414B9987496E2F,
				14D15479CC3E82105DE29877,
				2725852D1C93F2DD429038E0,
			);
			name = Resources;
			sourceTree = "<group>";
		};
		AB86ABEFDEE1CBC5B0FF6CC8 /* Source */ = {
			isa = PBXGroup;
			children = (
				BE72B8D14C71E8056DC2F684,
				B8195326CEC85A395553F239,
				37D85001CA6218BB6BB84201,
				A82639EF942D36F0926DF839,
			);
			name = Source;
			sourceTree = "<group>";
		};
		AE93DFE8AF9090F6EB1303EB /* JUCE Modules */ = {
			isa = PBXGroup;
			children = (
				4B38E0F5075317ADF6343EC8,
				506C41BEE312D3ADD6A65750,
				6F1B002F80090E086E6ECDCD,
				EDD7BD1CD3B80FC36B70C00C,
				F3247AA79DBF950A2023E057,
				DC8A3BB0BF7E37FF4C721D4E,
				D2CF50B9052DCC83565E7C4C,
				80783ACD05C82D16B450DFC8,
				E3A40CB5C2ED3EC123EF6350,
				CCBA29715F353C835ABCAA09,
				B53D1B0A8275B5CE7FE225A1,
				09F73D4F9174B02DA0CCEB5E,
				5074E138FC8C2FA754CFDAB3,
				0337A7C8DFF9F45655A28EAE,
				3093B061C9454A6CDA0DBB03,
				B0B54D716EC5A8A440491DA8,
				07EBDF94D9237B0C1C3316EE,
			);
			name = "JUCE Modules";
			sourceTree = "<group>";
		};
		FCD6A76A3FFF5F4E3B8C6F1D /* Products */ = {
			isa = PBXGroup;
			children = (
				E0E94162A2A6013CF8A62D87,
				47C4A60F1F69F694D0FF5694,
				EE21D3146AC835CACDE8E5CC,
				C72128CD83BAFC164C054E1B,
				7A5D5E39D253FFBC84A16F78,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		61F4B34FF62B7B8CDBB4F6D2 /* Color Assistant - Standalone Plugin */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 6405337589F571975C2A8C49;
			buildPhases = (
				786C6CDEE820D3A968D138FD,
				8D8447C535B9F292F7A56DC3,
				31A50FE87C82E9BD1FA3C907,
			);
			buildRules = (
			);
			dependencies = (
				FA9ED53100B175870756B6DB,
			);
			name = "Color Assistant - Standalone Plugin";
			productName = "Color Assistant";
			productReference = EE21D3146AC835CACDE8E5CC;
			productType = "com.apple.product-type.application";
		};
		CCAE7C3DBFA37EF4BD3518AA /* Color Assistant - Shared Code */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 136A6AB069C18963C78AAB00;
			buildPhases = (
				DA62D032C71C964EAEA7F41E,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "Color Assistant - Shared Code";
			productName = "Color Assistant";
			productReference = C72128CD83BAFC164C054E1B;
			productType = "com.apple.product-type.library.static";
		};
		D63B037CAB88D5BCB9AD8AD8 /* Color Assistant - VST3 Manifest Helper */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 21C348F4B78151F883D70623;
			buildPhases = (
				6F79BA843261E029827097D6,
				69CE84BBB665D49F7294DE22,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "Color Assistant - VST3 Manifest Helper";
			productName = "Color Assistant";
			productReference = 7A5D5E39D253FFBC84A16F78;
			productType = "com.apple.product-type.tool";
		};
		DE02EDE44B33FE42311F56AB /* Color Assistant - VST3 */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 810B8D8D31198509E3AE34F7;
			buildPhases = (
				A772E0C7BD51A3ED05B47FFF,
				45E9AA4C90E76BE279464394,
				39E11F5A2B5E11E82871F334,
				C218985CADB59BC3B86C328C,
				56B66C69BCC03CBE44066FFC,
			);
			buildRules = (
			);
			dependencies = (
				76E6D75629DBAA6D37497D59,
				A97F01DAC8246C8EEB478B93,
			);
			name = "Color Assistant - VST3";
			productName = "Color Assistant";
			productReference = E0E94162A2A6013CF8A62D87;
			productType = "com.apple.product-type.bundle";
		};
		F7E74B408C0981C5613366E8 /* Color Assistant - AU */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 97186D2724F29C49130A7AD0;
			buildPhases = (
				81B9E9132E8A4CA41610C19B,
				5E0DA9FF53FE3242F0841E40,
				B07E4A9B665FD7374B12F4F3,
				DC297C2C2B9FECE57245445F,
			);
			buildRules = (
			);
			dependencies = (
				E5D95A47319333EEC2FD9E96,
			);
			name = "Color Assistant - AU";
			productName = "Color Assistant";
			productReference = 47C4A60F1F69F694D0FF5694;
			productType = "com.apple.product-type.bundle";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		723D454276EAF0AFEC307D20 = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = YES;
				LastUpgradeCheck = 1340;
				ORGANIZATIONNAME = "";
				TargetAttributes = {
					61F4B34FF62B7B8CDBB4F6D2 = {
						SystemCapabilities = {
							com.apple.ApplicationGroups.iOS = {
								enabled = 0;
							};
							com.apple.HardenedRuntime = {
								enabled = 0;
							};
							com.apple.InAppPurchase = {
								enabled = 0;
							};
							com.apple.InterAppAudio = {
								enabled = 0;
							};
							com.apple.Push = {
								enabled = 0;
							};
							com.apple.Sandbox = {
								enabled = 0;
							};
						};
					};
					6E4B51A85131C7C7E30AD586 = {
						SystemCapabilities = {
							com.apple.ApplicationGroups.iOS = {
								enabled = 0;
							};
							com.apple.HardenedRuntime = {
								enabled = 0;
							};
							com.apple.InAppPurchase = {
								enabled = 0;
							};
							com.apple.InterAppAudio = {
								enabled = 0;
							};
							com.apple.Push = {
								enabled = 0;
							};
							com.apple.Sandbox = {
								enabled = 0;
							};
						};
					};
					CCAE7C3DBFA37EF4BD3518AA = {
						SystemCapabilities = {
							com.apple.ApplicationGroups.iOS = {
								enabled = 0;
							};
							com.apple.HardenedRuntime = {
								enabled = 0;
							};
							com.apple.InAppPurchase = {
								enabled = 0;
							};
							com.apple.InterAppAudio = {
								enabled = 0;
							};
							com.apple.Push = {
								enabled = 0;
							};
							com.apple.Sandbox = {
								enabled = 0;
							};
						};
					};
					D63B037CAB88D5BCB9AD8AD8 = {
						SystemCapabilities = {
							com.apple.ApplicationGroups.iOS = {
								enabled = 0;
							};
							com.apple.HardenedRuntime = {
								enabled = 0;
							};
							com.apple.InAppPurchase = {
								enabled = 0;
							};
							com.apple.InterAppAudio = {
								enabled = 0;
							};
							com.apple.Push = {
								enabled = 0;
							};
							com.apple.Sandbox = {
								enabled = 0;
							};
						};
					};
					DE02EDE44B33FE42311F56AB = {
						SystemCapabilities = {
							com.apple.ApplicationGroups.iOS = {
								enabled = 0;
							};
							com.apple.HardenedRuntime = {
								enabled = 0;
							};
							com.apple.InAppPurchase = {
								enabled = 0;
							};
							com.apple.InterAppAudio = {
								enabled = 0;
							};
							com.apple.Push = {
								enabled = 0;
							};
							com.apple.Sandbox = {
								enabled = 0;
							};
						};
					};
					F7E74B408C0981C5613366E8 = {
						SystemCapabilities = {
							com.apple.ApplicationGroups.iOS = {
								enabled = 0;
							};
							com.apple.HardenedRuntime = {
								enabled = 0;
							};
							com.apple.InAppPurchase = {
								enabled = 0;
							};
							com.apple.InterAppAudio = {
								enabled = 0;
							};
							com.apple.Push = {
								enabled = 0;
							};
							com.apple.Sandbox = {
								enabled = 0;
							};
						};
					};
				};
			};
			buildConfigurationList = 3B1935D311C0CB5802628D60;
			compatibilityVersion = "Xcode 3.2";
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 345774711EE3962F8EE72973;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				6E4B51A85131C7C7E30AD586,
				DE02EDE44B33FE42311F56AB,
				F7E74B408C0981C5613366E8,
				61F4B34FF62B7B8CDBB4F6D2,
				CCAE7C3DBFA37EF4BD3518AA,
				D63B037CAB88D5BCB9AD8AD8,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		786C6CDEE820D3A968D138FD = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				730CAA560B4BE5D33A6AFDEF,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		81B9E9132E8A4CA41610C19B = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				730CAA560B4BE5D33A6AFDEF,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A772E0C7BD51A3ED05B47FFF = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				730CAA560B4BE5D33A6AFDEF,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		56B66C69BCC03CBE44066FFC /* Plugin Copy Step */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			name = "Plugin Copy Step";
			alwaysOutOfDate = 1;
			shellPath = /bin/sh;
			shellScript = "set -euo pipefail\n\nif [[ \"${CONFIGURATION}\" == \"Debug\" ]]; then\n  destinationPlugin=\"${HOME}/Library/Audio/Plug-Ins/VST3//$(basename \"${TARGET_BUILD_DIR}/${WRAPPER_NAME}\")\"\n  echo Running rm -rf \\\"${destinationPlugin}\\\"\n  rm -rf \"${destinationPlugin}\"\n  echo Running ditto \\\"${TARGET_BUILD_DIR}/${WRAPPER_NAME}\\\" \\\"${destinationPlugin}\\\"\n  ditto \"${TARGET_BUILD_DIR}/${WRAPPER_NAME}\" \"${destinationPlugin}\"\n\n  if [[ -n \"${EXPANDED_CODE_SIGN_IDENTITY-}\" ]]; then\n    if [[ -n \"${CODE_SIGN_ENTITLEMENTS-}\" ]]; then\n      entitlementsArg=(--entitlements \"${CODE_SIGN_ENTITLEMENTS}\")\n    fi\n\n    echo Signing Identity: \\\"${EXPANDED_CODE_SIGN_IDENTITY_NAME}\\\"\n    echo Running codesign --verbose=4 --force --sign \\\"${EXPANDED_CODE_SIGN_IDENTITY}\\\" ${entitlementsArg[*]-} ${OTHER_CODE_SIGN_FLAGS-} \\\"${HOME}/Library/Audio/Plug-Ins/VST3//${WRAPPER_NAME}\\\"\n    codesign --verbose=4 --force --sign \"${EXPANDED_CODE_SIGN_IDENTITY}\" ${entitlementsArg[*]-} ${OTHER_CODE_SIGN_FLAGS-} \"${HOME}/Library/Audio/Plug-Ins/VST3//${WRAPPER_NAME}\"\n  fi\nfi\n\nif [[ \"${CONFIGURATION}\" == \"Release\" ]]; then\n  destinationPlugin=\"${HOME}/Library/Audio/Plug-Ins/VST3//$(basename \"${TARGET_BUILD_DIR}/${WRAPPER_NAME}\")\"\n  echo Running rm -rf \\\"${destinationPlugin}\\\"\n  rm -rf \"${destinationPlugin}\"\n  echo Running ditto \\\"${TARGET_BUILD_DIR}/${WRAPPER_NAME}\\\" \\\"${destinationPlugin}\\\"\n  ditto \"${TARGET_BUILD_DIR}/${WRAPPER_NAME}\" \"${destinationPlugin}\"\n\n  if [[ -n \"${EXPANDED_CODE_SIGN_IDENTITY-}\" ]]; then\n    if [[ -n \"${CODE_SIGN_ENTITLEMENTS-}\" ]]; then\n      entitlementsArg=(--entitlements \"${CODE_SIGN_ENTITLEMENTS}\")\n    fi\n\n    echo Signing Identity: \\\"${EXPANDED_CODE_SIGN_IDENTITY_NAME}\\\"\n    echo Running codesign --verbose=4 --force --sign \\\"${EXPANDED_CODE_SIGN_IDENTITY}\\\" ${entitlementsArg[*]-} ${OTHER_CODE_SIGN_FLAGS-} \\\"${HOME}/Library/Audio/Plug-Ins/VST3//${WRAPPER_NAME}\\\"\n    codesign --verbose=4 --force --sign \"${EXPANDED_CODE_SIGN_IDENTITY}\" ${entitlementsArg[*]-} ${OTHER_CODE_SIGN_FLAGS-} \"${HOME}/Library/Audio/Plug-Ins/VST3//${WRAPPER_NAME}\"\n  fi\nfi\n";
		};
		C218985CADB59BC3B86C328C /* Update manifest */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			name = "Update manifest";
			alwaysOutOfDate = 1;
			shellPath = /bin/sh;
			shellScript = "set -euo pipefail\n\necho Running codesign --verbose=4 --force --sign - \\\"${CONFIGURATION_BUILD_DIR}/${WRAPPER_NAME}\\\"\ncodesign --verbose=4 --force --sign - \"${CONFIGURATION_BUILD_DIR}/${WRAPPER_NAME}\"\n\necho Running \\\"${CONFIGURATION_BUILD_DIR}/juce_vst3_helper\\\" -create -version \\\"1.0.0\\\" -path \\\"${CONFIGURATION_BUILD_DIR}/${WRAPPER_NAME}\\\" -output \\\"${CONFIGURATION_BUILD_DIR}/${WRAPPER_NAME}/Contents/Resources/moduleinfo.json\\\"\n\"${CONFIGURATION_BUILD_DIR}/juce_vst3_helper\" -create -version \"1.0.0\" -path \"${CONFIGURATION_BUILD_DIR}/${WRAPPER_NAME}\" -output \"${CONFIGURATION_BUILD_DIR}/${WRAPPER_NAME}/Contents/Resources/moduleinfo.json\"\n";
		};
		DC297C2C2B9FECE57245445F /* Plugin Copy Step */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			name = "Plugin Copy Step";
			alwaysOutOfDate = 1;
			shellPath = /bin/sh;
			shellScript = "set -euo pipefail\n\nif [[ \"${CONFIGURATION}\" == \"Debug\" ]]; then\n  destinationPlugin=\"${HOME}/Library/Audio/Plug-Ins/Components//$(basename \"${TARGET_BUILD_DIR}/${WRAPPER_NAME}\")\"\n  echo Running rm -rf \\\"${destinationPlugin}\\\"\n  rm -rf \"${destinationPlugin}\"\n  echo Running ditto \\\"${TARGET_BUILD_DIR}/${WRAPPER_NAME}\\\" \\\"${destinationPlugin}\\\"\n  ditto \"${TARGET_BUILD_DIR}/${WRAPPER_NAME}\" \"${destinationPlugin}\"\n\n  if [[ -n \"${EXPANDED_CODE_SIGN_IDENTITY-}\" ]]; then\n    if [[ -n \"${CODE_SIGN_ENTITLEMENTS-}\" ]]; then\n      entitlementsArg=(--entitlements \"${CODE_SIGN_ENTITLEMENTS}\")\n    fi\n\n    echo Signing Identity: \\\"${EXPANDED_CODE_SIGN_IDENTITY_NAME}\\\"\n    echo Running codesign --verbose=4 --force --sign \\\"${EXPANDED_CODE_SIGN_IDENTITY}\\\" ${entitlementsArg[*]-} ${OTHER_CODE_SIGN_FLAGS-} \\\"${HOME}/Library/Audio/Plug-Ins/Components//${WRAPPER_NAME}\\\"\n    codesign --verbose=4 --force --sign \"${EXPANDED_CODE_SIGN_IDENTITY}\" ${entitlementsArg[*]-} ${OTHER_CODE_SIGN_FLAGS-} \"${HOME}/Library/Audio/Plug-Ins/Components//${WRAPPER_NAME}\"\n  fi\nfi\n\nif [[ \"${CONFIGURATION}\" == \"Release\" ]]; then\n  destinationPlugin=\"${HOME}/Library/Audio/Plug-Ins/Components//$(basename \"${TARGET_BUILD_DIR}/${WRAPPER_NAME}\")\"\n  echo Running rm -rf \\\"${destinationPlugin}\\\"\n  rm -rf \"${destinationPlugin}\"\n  echo Running ditto \\\"${TARGET_BUILD_DIR}/${WRAPPER_NAME}\\\" \\\"${destinationPlugin}\\\"\n  ditto \"${TARGET_BUILD_DIR}/${WRAPPER_NAME}\" \"${destinationPlugin}\"\n\n  if [[ -n \"${EXPANDED_CODE_SIGN_IDENTITY-}\" ]]; then\n    if [[ -n \"${CODE_SIGN_ENTITLEMENTS-}\" ]]; then\n      entitlementsArg=(--entitlements \"${CODE_SIGN_ENTITLEMENTS}\")\n    fi\n\n    echo Signing Identity: \\\"${EXPANDED_CODE_SIGN_IDENTITY_NAME}\\\"\n    echo Running codesign --verbose=4 --force --sign \\\"${EXPANDED_CODE_SIGN_IDENTITY}\\\" ${entitlementsArg[*]-} ${OTHER_CODE_SIGN_FLAGS-} \\\"${HOME}/Library/Audio/Plug-Ins/Components//${WRAPPER_NAME}\\\"\n    codesign --verbose=4 --force --sign \"${EXPANDED_CODE_SIGN_IDENTITY}\" ${entitlementsArg[*]-} ${OTHER_CODE_SIGN_FLAGS-} \"${HOME}/Library/Audio/Plug-Ins/Components//${WRAPPER_NAME}\"\n  fi\nfi\n";
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		45E9AA4C90E76BE279464394 = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				78C47D266AF0B83ECE0E091D,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		5E0DA9FF53FE3242F0841E40 = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				0A0637BF3E5F4694F345309E,
				45342DD43E53776252C10923,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		6F79BA843261E029827097D6 = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				DEBFAF7EA6BDD91F9F1684E7,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8D8447C535B9F292F7A56DC3 = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				D7124CDC0C06A4FE5C960DC7,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DA62D032C71C964EAEA7F41E = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				BA2AB00BE8D40565EC756B9C,
				2F03B6D71B4B2C41729F9D6E,
				749FF5B4553624853228E8A3,
				C23CB7A3202E25C7B2CB99BA,
				25D9569F8838B1290B09B070,
				6BB5F64F5D161E39ECBC257C,
				F251D7E336DE786FAD9AF7B9,
				47ADC7538DA77EA692434D85,
				8AA2DDD6376395F4640A7831,
				42B0F08D4CD4B51E65808094,
				3424F160667C48625988EEA7,
				8B09D84A4746D24D18D8C5C9,
				57E16CD8522D929AE7E23271,
				BC993CA3DAB30D966F6A1AE4,
				9648F25F6D480BA883444F29,
				9D7134766DA64B85DDB332F3,
				25B6706673F326FEEDAB43B3,
				FF2F0F778A1D6CE20C3CE348,
				EB256F5D318E13B1FBAFD6E6,
				1AACBC2A039E1FD0C9FABBA2,
				B7E06EEE0B1C8AC929266A10,
				860F517283D5D32638DE40EA,
				52F09D65D2C3211E0CC384B6,
				B4AB1DEED65553815617BA9E,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		6D722F3E40F57D0909F1275C = {
			isa = PBXTargetDependency;
			target = DE02EDE44B33FE42311F56AB;
		};
		76E6D75629DBAA6D37497D59 = {
			isa = PBXTargetDependency;
			target = CCAE7C3DBFA37EF4BD3518AA;
		};
		922F5A5769E1AC09D308C9E0 = {
			isa = PBXTargetDependency;
			target = F7E74B408C0981C5613366E8;
		};
		A97F01DAC8246C8EEB478B93 = {
			isa = PBXTargetDependency;
			target = D63B037CAB88D5BCB9AD8AD8;
		};
		BFA91DCD1DD9A9A05645C38F = {
			isa = PBXTargetDependency;
			target = 61F4B34FF62B7B8CDBB4F6D2;
		};
		C1789E359A99F8E52E5A231F = {
			isa = PBXTargetDependency;
			target = CCAE7C3DBFA37EF4BD3518AA;
		};
		E5D95A47319333EEC2FD9E96 = {
			isa = PBXTargetDependency;
			target = CCAE7C3DBFA37EF4BD3518AA;
		};
		E6EF53C20D1E8D347CA58B9D = {
			isa = PBXTargetDependency;
			target = D63B037CAB88D5BCB9AD8AD8;
		};
		FA9ED53100B175870756B6DB = {
			isa = PBXTargetDependency;
			target = CCAE7C3DBFA37EF4BD3518AA;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		0FD1AB7C40F73424A1B759D5 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "c++17";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_LINK_OBJC_RUNTIME = NO;
				COMBINE_HIDPI_IMAGES = YES;
				CONFIGURATION_BUILD_DIR = "$(PROJECT_DIR)/build/$(CONFIGURATION)";
				COPY_PHASE_STRIP = NO;
				EXCLUDED_ARCHS = "";
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"_DEBUG=1",
					"DEBUG=1",
					"JUCE_PROJUCER_VERSION=0x80007",
					"JUCE_MODULE_AVAILABLE_juce_analytics=1",
					"JUCE_MODULE_AVAILABLE_juce_animation=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_basics=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_devices=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_formats=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_plugin_client=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_processors=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_utils=1",
					"JUCE_MODULE_AVAILABLE_juce_core=1",
					"JUCE_MODULE_AVAILABLE_juce_data_structures=1",
					"JUCE_MODULE_AVAILABLE_juce_dsp=1",
					"JUCE_MODULE_AVAILABLE_juce_events=1",
					"JUCE_MODULE_AVAILABLE_juce_graphics=1",
					"JUCE_MODULE_AVAILABLE_juce_gui_basics=1",
					"JUCE_MODULE_AVAILABLE_juce_gui_extra=1",
					"JUCE_MODULE_AVAILABLE_juce_midi_ci=1",
					"JUCE_MODULE_AVAILABLE_juce_osc=1",
					"JUCE_GLOBAL_MODULE_SETTINGS_INCLUDED=1",
					"JUCE_VST3_CAN_REPLACE_VST2=0",
					"JUCE_STRICT_REFCOUNTEDPOINTER=1",
					"JucePlugin_Build_VST=0",
					"JucePlugin_Build_VST3=0",
					"JucePlugin_Build_AU=0",
					"JucePlugin_Build_AUv3=0",
					"JucePlugin_Build_AAX=0",
					"JucePlugin_Build_Standalone=1",
					"JucePlugin_Build_Unity=0",
					"JucePlugin_Build_LV2=0",
					"JucePlugin_Enable_IAA=0",
					"JucePlugin_Enable_ARA=0",
					"JucePlugin_Name=\\\"Color\\ Assistant\\\"",
					"JucePlugin_Desc=\\\"Color\\ Assistant\\\"",
					"JucePlugin_Manufacturer=\\\"EGURT\\\"",
					"JucePlugin_ManufacturerWebsite=\\\"www.yourcompany.com\\\"",
					"JucePlugin_ManufacturerEmail=\\\"\\\"",
					"JucePlugin_ManufacturerCode=0x4d616e75",
					"JucePlugin_PluginCode=0x4b74617a",
					"JucePlugin_IsSynth=0",
					"JucePlugin_WantsMidiInput=0",
					"JucePlugin_ProducesMidiOutput=0",
					"JucePlugin_IsMidiEffect=0",
					"JucePlugin_EditorRequiresKeyboardFocus=0",
					"JucePlugin_Version=1.0.0",
					"JucePlugin_VersionCode=0x10000",
					"JucePlugin_VersionString=\\\"1.0.0\\\"",
					"JucePlugin_VSTUniqueID=JucePlugin_PluginCode",
					"JucePlugin_VSTCategory=kPlugCategEffect",
					"JucePlugin_Vst3Category=\\\"Fx\\\"",
					"JucePlugin_AUMainType=\\'aufx\\'",
					"JucePlugin_AUSubType=JucePlugin_PluginCode",
					"JucePlugin_AUExportPrefix=ColorAssistantAU",
					"JucePlugin_AUExportPrefixQuoted=\\\"ColorAssistantAU\\\"",
					"JucePlugin_AUManufacturerCode=JucePlugin_ManufacturerCode",
					"JucePlugin_CFBundleIdentifier=com.yourcompany.ColorAssistant",
					"JucePlugin_AAXIdentifier=com.yourcompany.ColorAssistant",
					"JucePlugin_AAXManufacturerCode=JucePlugin_ManufacturerCode",
					"JucePlugin_AAXProductId=JucePlugin_PluginCode",
					"JucePlugin_AAXCategory=0",
					"JucePlugin_AAXDisableBypass=0",
					"JucePlugin_AAXDisableMultiMono=0",
					"JucePlugin_IAAType=0x61757278",
					"JucePlugin_IAASubType=JucePlugin_PluginCode",
					"JucePlugin_IAAName=\\\"EGURT:\\ Color\\ Assistant\\\"",
					"JucePlugin_VSTNumMidiInputs=16",
					"JucePlugin_VSTNumMidiOutputs=16",
					"JucePlugin_ARAContentTypes=0",
					"JucePlugin_ARATransformationFlags=0",
					"JucePlugin_ARAFactoryID=\\\"com.yourcompany.ColorAssistant.factory\\\"",
					"JucePlugin_ARADocumentArchiveID=\\\"com.yourcompany.ColorAssistant.aradocumentarchive.1.0.0\\\"",
					"JucePlugin_ARACompatibleArchiveIDs=\\\"\\\"",
					"JUCE_STANDALONE_APPLICATION=JucePlugin_Build_Standalone",
					"JUCER_XCODE_MAC_F6D2F4CF=1",
					"JUCE_APP_VERSION=1.0.0",
					"JUCE_APP_VERSION_HEX=0x10000",
				);
				GCC_VERSION = com.apple.compilers.llvm.clang.1_0;
				HEADER_SEARCH_PATHS = (
					"$(HOME)/JUCE/modules/juce_audio_processors/format_types/VST3_SDK",
					"$(SRCROOT)/../../JuceLibraryCode",
					"$(HOME)/JUCE/modules",
					"$(HOME)/JUCE/modules/juce_audio_plugin_client/AU",
					"$(inherited)",
				);
				INFOPLIST_FILE = Info-Standalone_Plugin.plist;
				INFOPLIST_PREPROCESS = NO;
				MACOSX_DEPLOYMENT_TARGET = 10.13;
				MTL_HEADER_SEARCH_PATHS = "$(HOME)/JUCE/modules/juce_audio_processors/format_types/VST3_SDK $(SRCROOT)/../../JuceLibraryCode $(HOME)/JUCE/modules $(HOME)/JUCE/modules/juce_audio_plugin_client/AU";
				OTHER_LDFLAGS = "-lColor\\ Assistant";
				PRODUCT_BUNDLE_IDENTIFIER = com.yourcompany.ColorAssistant;
				PRODUCT_NAME = "Color Assistant";
				USE_HEADERMAP = NO;
				VALID_ARCHS = "i386 x86_64 arm64 arm64e";
			};
			name = Debug;
		};
		232F5FF35CF08D93860E7A6A /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = NO;
				GCC_C_LANGUAGE_STANDARD = c11;
				GCC_INLINES_ARE_PRIVATE_EXTERN = YES;
				GCC_MODEL_TUNING = G5;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_SYMBOLS_PRIVATE_EXTERN = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES;
				GCC_WARN_CHECK_SWITCH_STATEMENTS = YES;
				GCC_WARN_MISSING_PARENTHESES = YES;
				GCC_WARN_NON_VIRTUAL_DESTRUCTOR = YES;
				GCC_WARN_TYPECHECK_CALLS_TO_PRINTF = YES;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				OTHER_CODE_SIGN_FLAGS = --timestamp;
				PRODUCT_NAME = "Color Assistant";
				SDKROOT = macosx;
				WARNING_CFLAGS = "-Wreorder";
				ZERO_LINK = NO;
			};
			name = Release;
		};
		2457CA5B066CE2999767784B /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "c++17";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_LINK_OBJC_RUNTIME = NO;
				COMBINE_HIDPI_IMAGES = YES;
				CONFIGURATION_BUILD_DIR = "$(PROJECT_DIR)/build/$(CONFIGURATION)";
				DEAD_CODE_STRIPPING = YES;
				EXCLUDED_ARCHS = "";
				GCC_GENERATE_DEBUGGING_SYMBOLS = NO;
				GCC_OPTIMIZATION_LEVEL = 3;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"_NDEBUG=1",
					"NDEBUG=1",
					"JUCE_PROJUCER_VERSION=0x80007",
					"JUCE_MODULE_AVAILABLE_juce_analytics=1",
					"JUCE_MODULE_AVAILABLE_juce_animation=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_basics=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_devices=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_formats=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_plugin_client=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_processors=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_utils=1",
					"JUCE_MODULE_AVAILABLE_juce_core=1",
					"JUCE_MODULE_AVAILABLE_juce_data_structures=1",
					"JUCE_MODULE_AVAILABLE_juce_dsp=1",
					"JUCE_MODULE_AVAILABLE_juce_events=1",
					"JUCE_MODULE_AVAILABLE_juce_graphics=1",
					"JUCE_MODULE_AVAILABLE_juce_gui_basics=1",
					"JUCE_MODULE_AVAILABLE_juce_gui_extra=1",
					"JUCE_MODULE_AVAILABLE_juce_midi_ci=1",
					"JUCE_MODULE_AVAILABLE_juce_osc=1",
					"JUCE_GLOBAL_MODULE_SETTINGS_INCLUDED=1",
					"JUCE_VST3_CAN_REPLACE_VST2=0",
					"JUCE_STRICT_REFCOUNTEDPOINTER=1",
					"JucePlugin_Build_VST=0",
					"JucePlugin_Build_VST3=0",
					"JucePlugin_Build_AU=1",
					"JucePlugin_Build_AUv3=0",
					"JucePlugin_Build_AAX=0",
					"JucePlugin_Build_Standalone=0",
					"JucePlugin_Build_Unity=0",
					"JucePlugin_Build_LV2=0",
					"JucePlugin_Enable_IAA=0",
					"JucePlugin_Enable_ARA=0",
					"JucePlugin_Name=\\\"Color\\ Assistant\\\"",
					"JucePlugin_Desc=\\\"Color\\ Assistant\\\"",
					"JucePlugin_Manufacturer=\\\"EGURT\\\"",
					"JucePlugin_ManufacturerWebsite=\\\"www.yourcompany.com\\\"",
					"JucePlugin_ManufacturerEmail=\\\"\\\"",
					"JucePlugin_ManufacturerCode=0x4d616e75",
					"JucePlugin_PluginCode=0x4b74617a",
					"JucePlugin_IsSynth=0",
					"JucePlugin_WantsMidiInput=0",
					"JucePlugin_ProducesMidiOutput=0",
					"JucePlugin_IsMidiEffect=0",
					"JucePlugin_EditorRequiresKeyboardFocus=0",
					"JucePlugin_Version=1.0.0",
					"JucePlugin_VersionCode=0x10000",
					"JucePlugin_VersionString=\\\"1.0.0\\\"",
					"JucePlugin_VSTUniqueID=JucePlugin_PluginCode",
					"JucePlugin_VSTCategory=kPlugCategEffect",
					"JucePlugin_Vst3Category=\\\"Fx\\\"",
					"JucePlugin_AUMainType=\\'aufx\\'",
					"JucePlugin_AUSubType=JucePlugin_PluginCode",
					"JucePlugin_AUExportPrefix=ColorAssistantAU",
					"JucePlugin_AUExportPrefixQuoted=\\\"ColorAssistantAU\\\"",
					"JucePlugin_AUManufacturerCode=JucePlugin_ManufacturerCode",
					"JucePlugin_CFBundleIdentifier=com.yourcompany.ColorAssistant",
					"JucePlugin_AAXIdentifier=com.yourcompany.ColorAssistant",
					"JucePlugin_AAXManufacturerCode=JucePlugin_ManufacturerCode",
					"JucePlugin_AAXProductId=JucePlugin_PluginCode",
					"JucePlugin_AAXCategory=0",
					"JucePlugin_AAXDisableBypass=0",
					"JucePlugin_AAXDisableMultiMono=0",
					"JucePlugin_IAAType=0x61757278",
					"JucePlugin_IAASubType=JucePlugin_PluginCode",
					"JucePlugin_IAAName=\\\"EGURT:\\ Color\\ Assistant\\\"",
					"JucePlugin_VSTNumMidiInputs=16",
					"JucePlugin_VSTNumMidiOutputs=16",
					"JucePlugin_ARAContentTypes=0",
					"JucePlugin_ARATransformationFlags=0",
					"JucePlugin_ARAFactoryID=\\\"com.yourcompany.ColorAssistant.factory\\\"",
					"JucePlugin_ARADocumentArchiveID=\\\"com.yourcompany.ColorAssistant.aradocumentarchive.1.0.0\\\"",
					"JucePlugin_ARACompatibleArchiveIDs=\\\"\\\"",
					"JUCE_STANDALONE_APPLICATION=JucePlugin_Build_Standalone",
					"JUCER_XCODE_MAC_F6D2F4CF=1",
					"JUCE_APP_VERSION=1.0.0",
					"JUCE_APP_VERSION_HEX=0x10000",
				);
				GCC_VERSION = com.apple.compilers.llvm.clang.1_0;
				GENERATE_PKGINFO_FILE = YES;
				HEADER_SEARCH_PATHS = (
					"$(HOME)/JUCE/modules/juce_audio_processors/format_types/VST3_SDK",
					"$(SRCROOT)/../../JuceLibraryCode",
					"$(HOME)/JUCE/modules",
					"$(HOME)/JUCE/modules/juce_audio_plugin_client/AU",
					"$(inherited)",
				);
				INFOPLIST_FILE = Info-AU.plist;
				INFOPLIST_PREPROCESS = NO;
				INSTALL_PATH = "$(HOME)/Library/Audio/Plug-Ins/Components/";
				LIBRARY_STYLE = Bundle;
				LLVM_LTO = YES;
				MACOSX_DEPLOYMENT_TARGET = 10.13;
				MTL_HEADER_SEARCH_PATHS = "$(HOME)/JUCE/modules/juce_audio_processors/format_types/VST3_SDK $(SRCROOT)/../../JuceLibraryCode $(HOME)/JUCE/modules $(HOME)/JUCE/modules/juce_audio_plugin_client/AU";
				OTHER_LDFLAGS = "-bundle -lColor\\ Assistant";
				OTHER_REZFLAGS = "-d ppc_$ppc -d i386_$i386 -d ppc64_$ppc64 -d x86_64_$x86_64 -d arm64_$arm64 -I /System/Library/Frameworks/CoreServices.framework/Frameworks/CarbonCore.framework/Versions/A/Headers -I \"$(DEVELOPER_DIR)/Extras/CoreAudio/AudioUnits/AUPublic/AUBase\" -I \"$(DEVELOPER_DIR)/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks/AudioUnit.framework/Headers\"";
				PRODUCT_BUNDLE_IDENTIFIER = com.yourcompany.ColorAssistant;
				PRODUCT_NAME = "Color Assistant";
				USE_HEADERMAP = NO;
				VALID_ARCHS = "i386 x86_64 arm64 arm64e";
				WRAPPER_EXTENSION = component;
			};
			name = Release;
		};
		4DC5A80FFBBFE358FE3712D8 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "c++17";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_LINK_OBJC_RUNTIME = NO;
				COMBINE_HIDPI_IMAGES = YES;
				CONFIGURATION_BUILD_DIR = "$(PROJECT_DIR)/build/$(CONFIGURATION)";
				DEAD_CODE_STRIPPING = YES;
				EXCLUDED_ARCHS = "";
				GCC_GENERATE_DEBUGGING_SYMBOLS = NO;
				GCC_OPTIMIZATION_LEVEL = 3;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"_NDEBUG=1",
					"NDEBUG=1",
					"JUCE_PROJUCER_VERSION=0x80007",
					"JUCE_MODULE_AVAILABLE_juce_analytics=1",
					"JUCE_MODULE_AVAILABLE_juce_animation=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_basics=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_devices=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_formats=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_plugin_client=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_processors=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_utils=1",
					"JUCE_MODULE_AVAILABLE_juce_core=1",
					"JUCE_MODULE_AVAILABLE_juce_data_structures=1",
					"JUCE_MODULE_AVAILABLE_juce_dsp=1",
					"JUCE_MODULE_AVAILABLE_juce_events=1",
					"JUCE_MODULE_AVAILABLE_juce_graphics=1",
					"JUCE_MODULE_AVAILABLE_juce_gui_basics=1",
					"JUCE_MODULE_AVAILABLE_juce_gui_extra=1",
					"JUCE_MODULE_AVAILABLE_juce_midi_ci=1",
					"JUCE_MODULE_AVAILABLE_juce_osc=1",
					"JUCE_GLOBAL_MODULE_SETTINGS_INCLUDED=1",
					"JUCE_VST3_CAN_REPLACE_VST2=0",
					"JUCE_STRICT_REFCOUNTEDPOINTER=1",
					"JucePlugin_Build_VST=0",
					"JucePlugin_Build_VST3=1",
					"JucePlugin_Build_AU=1",
					"JucePlugin_Build_AUv3=0",
					"JucePlugin_Build_AAX=0",
					"JucePlugin_Build_Standalone=1",
					"JucePlugin_Build_Unity=0",
					"JucePlugin_Build_LV2=0",
					"JucePlugin_Enable_IAA=0",
					"JucePlugin_Enable_ARA=0",
					"JucePlugin_Name=\\\"Color\\ Assistant\\\"",
					"JucePlugin_Desc=\\\"Color\\ Assistant\\\"",
					"JucePlugin_Manufacturer=\\\"EGURT\\\"",
					"JucePlugin_ManufacturerWebsite=\\\"www.yourcompany.com\\\"",
					"JucePlugin_ManufacturerEmail=\\\"\\\"",
					"JucePlugin_ManufacturerCode=0x4d616e75",
					"JucePlugin_PluginCode=0x4b74617a",
					"JucePlugin_IsSynth=0",
					"JucePlugin_WantsMidiInput=0",
					"JucePlugin_ProducesMidiOutput=0",
					"JucePlugin_IsMidiEffect=0",
					"JucePlugin_EditorRequiresKeyboardFocus=0",
					"JucePlugin_Version=1.0.0",
					"JucePlugin_VersionCode=0x10000",
					"JucePlugin_VersionString=\\\"1.0.0\\\"",
					"JucePlugin_VSTUniqueID=JucePlugin_PluginCode",
					"JucePlugin_VSTCategory=kPlugCategEffect",
					"JucePlugin_Vst3Category=\\\"Fx\\\"",
					"JucePlugin_AUMainType=\\'aufx\\'",
					"JucePlugin_AUSubType=JucePlugin_PluginCode",
					"JucePlugin_AUExportPrefix=ColorAssistantAU",
					"JucePlugin_AUExportPrefixQuoted=\\\"ColorAssistantAU\\\"",
					"JucePlugin_AUManufacturerCode=JucePlugin_ManufacturerCode",
					"JucePlugin_CFBundleIdentifier=com.yourcompany.ColorAssistant",
					"JucePlugin_AAXIdentifier=com.yourcompany.ColorAssistant",
					"JucePlugin_AAXManufacturerCode=JucePlugin_ManufacturerCode",
					"JucePlugin_AAXProductId=JucePlugin_PluginCode",
					"JucePlugin_AAXCategory=0",
					"JucePlugin_AAXDisableBypass=0",
					"JucePlugin_AAXDisableMultiMono=0",
					"JucePlugin_IAAType=0x61757278",
					"JucePlugin_IAASubType=JucePlugin_PluginCode",
					"JucePlugin_IAAName=\\\"EGURT:\\ Color\\ Assistant\\\"",
					"JucePlugin_VSTNumMidiInputs=16",
					"JucePlugin_VSTNumMidiOutputs=16",
					"JucePlugin_ARAContentTypes=0",
					"JucePlugin_ARATransformationFlags=0",
					"JucePlugin_ARAFactoryID=\\\"com.yourcompany.ColorAssistant.factory\\\"",
					"JucePlugin_ARADocumentArchiveID=\\\"com.yourcompany.ColorAssistant.aradocumentarchive.1.0.0\\\"",
					"JucePlugin_ARACompatibleArchiveIDs=\\\"\\\"",
					"JUCE_STANDALONE_APPLICATION=JucePlugin_Build_Standalone",
					"JUCER_XCODE_MAC_F6D2F4CF=1",
					"JUCE_APP_VERSION=1.0.0",
					"JUCE_APP_VERSION_HEX=0x10000",
					"JUCE_SHARED_CODE=1",
				);
				GCC_VERSION = com.apple.compilers.llvm.clang.1_0;
				HEADER_SEARCH_PATHS = (
					"$(HOME)/JUCE/modules/juce_audio_processors/format_types/VST3_SDK",
					"$(SRCROOT)/../../JuceLibraryCode",
					"$(HOME)/JUCE/modules",
					"$(HOME)/JUCE/modules/juce_audio_plugin_client/AU",
					"$(inherited)",
				);
				INSTALL_PATH = "@executable_path/../Frameworks";
				LLVM_LTO = YES;
				MACOSX_DEPLOYMENT_TARGET = 10.13;
				MTL_HEADER_SEARCH_PATHS = "$(HOME)/JUCE/modules/juce_audio_processors/format_types/VST3_SDK $(SRCROOT)/../../JuceLibraryCode $(HOME)/JUCE/modules $(HOME)/JUCE/modules/juce_audio_plugin_client/AU";
				PRODUCT_BUNDLE_IDENTIFIER = com.yourcompany.ColorAssistant;
				PRODUCT_NAME = "Color Assistant";
				SKIP_INSTALL = YES;
				USE_HEADERMAP = NO;
				VALID_ARCHS = "i386 x86_64 arm64 arm64e";
			};
			name = Release;
		};
		A5DC6FC5A0D26D82E10C2520 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "c++17";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_LINK_OBJC_RUNTIME = NO;
				COMBINE_HIDPI_IMAGES = YES;
				CONFIGURATION_BUILD_DIR = "$(PROJECT_DIR)/build/$(CONFIGURATION)";
				COPY_PHASE_STRIP = NO;
				EXCLUDED_ARCHS = "";
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"_DEBUG=1",
					"DEBUG=1",
					"JUCE_PROJUCER_VERSION=0x80007",
					"JUCE_MODULE_AVAILABLE_juce_analytics=1",
					"JUCE_MODULE_AVAILABLE_juce_animation=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_basics=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_devices=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_formats=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_plugin_client=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_processors=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_utils=1",
					"JUCE_MODULE_AVAILABLE_juce_core=1",
					"JUCE_MODULE_AVAILABLE_juce_data_structures=1",
					"JUCE_MODULE_AVAILABLE_juce_dsp=1",
					"JUCE_MODULE_AVAILABLE_juce_events=1",
					"JUCE_MODULE_AVAILABLE_juce_graphics=1",
					"JUCE_MODULE_AVAILABLE_juce_gui_basics=1",
					"JUCE_MODULE_AVAILABLE_juce_gui_extra=1",
					"JUCE_MODULE_AVAILABLE_juce_midi_ci=1",
					"JUCE_MODULE_AVAILABLE_juce_osc=1",
					"JUCE_GLOBAL_MODULE_SETTINGS_INCLUDED=1",
					"JUCE_VST3_CAN_REPLACE_VST2=0",
					"JUCE_STRICT_REFCOUNTEDPOINTER=1",
					"JucePlugin_Build_VST=0",
					"JucePlugin_Build_VST3=1",
					"JucePlugin_Build_AU=0",
					"JucePlugin_Build_AUv3=0",
					"JucePlugin_Build_AAX=0",
					"JucePlugin_Build_Standalone=0",
					"JucePlugin_Build_Unity=0",
					"JucePlugin_Build_LV2=0",
					"JucePlugin_Enable_IAA=0",
					"JucePlugin_Enable_ARA=0",
					"JucePlugin_Name=\\\"Color\\ Assistant\\\"",
					"JucePlugin_Desc=\\\"Color\\ Assistant\\\"",
					"JucePlugin_Manufacturer=\\\"EGURT\\\"",
					"JucePlugin_ManufacturerWebsite=\\\"www.yourcompany.com\\\"",
					"JucePlugin_ManufacturerEmail=\\\"\\\"",
					"JucePlugin_ManufacturerCode=0x4d616e75",
					"JucePlugin_PluginCode=0x4b74617a",
					"JucePlugin_IsSynth=0",
					"JucePlugin_WantsMidiInput=0",
					"JucePlugin_ProducesMidiOutput=0",
					"JucePlugin_IsMidiEffect=0",
					"JucePlugin_EditorRequiresKeyboardFocus=0",
					"JucePlugin_Version=1.0.0",
					"JucePlugin_VersionCode=0x10000",
					"JucePlugin_VersionString=\\\"1.0.0\\\"",
					"JucePlugin_VSTUniqueID=JucePlugin_PluginCode",
					"JucePlugin_VSTCategory=kPlugCategEffect",
					"JucePlugin_Vst3Category=\\\"Fx\\\"",
					"JucePlugin_AUMainType=\\'aufx\\'",
					"JucePlugin_AUSubType=JucePlugin_PluginCode",
					"JucePlugin_AUExportPrefix=ColorAssistantAU",
					"JucePlugin_AUExportPrefixQuoted=\\\"ColorAssistantAU\\\"",
					"JucePlugin_AUManufacturerCode=JucePlugin_ManufacturerCode",
					"JucePlugin_CFBundleIdentifier=com.yourcompany.ColorAssistant",
					"JucePlugin_AAXIdentifier=com.yourcompany.ColorAssistant",
					"JucePlugin_AAXManufacturerCode=JucePlugin_ManufacturerCode",
					"JucePlugin_AAXProductId=JucePlugin_PluginCode",
					"JucePlugin_AAXCategory=0",
					"JucePlugin_AAXDisableBypass=0",
					"JucePlugin_AAXDisableMultiMono=0",
					"JucePlugin_IAAType=0x61757278",
					"JucePlugin_IAASubType=JucePlugin_PluginCode",
					"JucePlugin_IAAName=\\\"EGURT:\\ Color\\ Assistant\\\"",
					"JucePlugin_VSTNumMidiInputs=16",
					"JucePlugin_VSTNumMidiOutputs=16",
					"JucePlugin_ARAContentTypes=0",
					"JucePlugin_ARATransformationFlags=0",
					"JucePlugin_ARAFactoryID=\\\"com.yourcompany.ColorAssistant.factory\\\"",
					"JucePlugin_ARADocumentArchiveID=\\\"com.yourcompany.ColorAssistant.aradocumentarchive.1.0.0\\\"",
					"JucePlugin_ARACompatibleArchiveIDs=\\\"\\\"",
					"JUCE_STANDALONE_APPLICATION=JucePlugin_Build_Standalone",
					"JUCER_XCODE_MAC_F6D2F4CF=1",
					"JUCE_APP_VERSION=1.0.0",
					"JUCE_APP_VERSION_HEX=0x10000",
				);
				GCC_VERSION = com.apple.compilers.llvm.clang.1_0;
				GENERATE_PKGINFO_FILE = YES;
				HEADER_SEARCH_PATHS = (
					"$(HOME)/JUCE/modules/juce_audio_processors/format_types/VST3_SDK",
					"$(SRCROOT)/../../JuceLibraryCode",
					"$(HOME)/JUCE/modules",
					"$(HOME)/JUCE/modules/juce_audio_plugin_client/AU",
					"$(inherited)",
				);
				INFOPLIST_FILE = Info-VST3.plist;
				INFOPLIST_PREPROCESS = NO;
				INSTALL_PATH = "$(HOME)/Library/Audio/Plug-Ins/VST3/";
				LIBRARY_STYLE = Bundle;
				MACOSX_DEPLOYMENT_TARGET = 10.13;
				MTL_HEADER_SEARCH_PATHS = "$(HOME)/JUCE/modules/juce_audio_processors/format_types/VST3_SDK $(SRCROOT)/../../JuceLibraryCode $(HOME)/JUCE/modules $(HOME)/JUCE/modules/juce_audio_plugin_client/AU";
				OTHER_LDFLAGS = "-bundle -lColor\\ Assistant";
				PRODUCT_BUNDLE_IDENTIFIER = com.yourcompany.ColorAssistant;
				PRODUCT_NAME = "Color Assistant";
				USE_HEADERMAP = NO;
				VALID_ARCHS = "i386 x86_64 arm64 arm64e";
				WRAPPER_EXTENSION = vst3;
			};
			name = Debug;
		};
		A606423C5EBC601E4E6EFA60 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "c++17";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_LINK_OBJC_RUNTIME = NO;
				COMBINE_HIDPI_IMAGES = YES;
				CONFIGURATION_BUILD_DIR = "$(PROJECT_DIR)/build/$(CONFIGURATION)";
				COPY_PHASE_STRIP = NO;
				EXCLUDED_ARCHS = "";
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"_DEBUG=1",
					"DEBUG=1",
					"JUCE_PROJUCER_VERSION=0x80007",
					"JUCE_MODULE_AVAILABLE_juce_analytics=1",
					"JUCE_MODULE_AVAILABLE_juce_animation=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_basics=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_devices=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_formats=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_plugin_client=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_processors=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_utils=1",
					"JUCE_MODULE_AVAILABLE_juce_core=1",
					"JUCE_MODULE_AVAILABLE_juce_data_structures=1",
					"JUCE_MODULE_AVAILABLE_juce_dsp=1",
					"JUCE_MODULE_AVAILABLE_juce_events=1",
					"JUCE_MODULE_AVAILABLE_juce_graphics=1",
					"JUCE_MODULE_AVAILABLE_juce_gui_basics=1",
					"JUCE_MODULE_AVAILABLE_juce_gui_extra=1",
					"JUCE_MODULE_AVAILABLE_juce_midi_ci=1",
					"JUCE_MODULE_AVAILABLE_juce_osc=1",
					"JUCE_GLOBAL_MODULE_SETTINGS_INCLUDED=1",
					"JUCE_VST3_CAN_REPLACE_VST2=0",
					"JUCE_STRICT_REFCOUNTEDPOINTER=1",
					"JucePlugin_Build_VST=0",
					"JucePlugin_Build_VST3=0",
					"JucePlugin_Build_AU=1",
					"JucePlugin_Build_AUv3=0",
					"JucePlugin_Build_AAX=0",
					"JucePlugin_Build_Standalone=0",
					"JucePlugin_Build_Unity=0",
					"JucePlugin_Build_LV2=0",
					"JucePlugin_Enable_IAA=0",
					"JucePlugin_Enable_ARA=0",
					"JucePlugin_Name=\\\"Color\\ Assistant\\\"",
					"JucePlugin_Desc=\\\"Color\\ Assistant\\\"",
					"JucePlugin_Manufacturer=\\\"EGURT\\\"",
					"JucePlugin_ManufacturerWebsite=\\\"www.yourcompany.com\\\"",
					"JucePlugin_ManufacturerEmail=\\\"\\\"",
					"JucePlugin_ManufacturerCode=0x4d616e75",
					"JucePlugin_PluginCode=0x4b74617a",
					"JucePlugin_IsSynth=0",
					"JucePlugin_WantsMidiInput=0",
					"JucePlugin_ProducesMidiOutput=0",
					"JucePlugin_IsMidiEffect=0",
					"JucePlugin_EditorRequiresKeyboardFocus=0",
					"JucePlugin_Version=1.0.0",
					"JucePlugin_VersionCode=0x10000",
					"JucePlugin_VersionString=\\\"1.0.0\\\"",
					"JucePlugin_VSTUniqueID=JucePlugin_PluginCode",
					"JucePlugin_VSTCategory=kPlugCategEffect",
					"JucePlugin_Vst3Category=\\\"Fx\\\"",
					"JucePlugin_AUMainType=\\'aufx\\'",
					"JucePlugin_AUSubType=JucePlugin_PluginCode",
					"JucePlugin_AUExportPrefix=ColorAssistantAU",
					"JucePlugin_AUExportPrefixQuoted=\\\"ColorAssistantAU\\\"",
					"JucePlugin_AUManufacturerCode=JucePlugin_ManufacturerCode",
					"JucePlugin_CFBundleIdentifier=com.yourcompany.ColorAssistant",
					"JucePlugin_AAXIdentifier=com.yourcompany.ColorAssistant",
					"JucePlugin_AAXManufacturerCode=JucePlugin_ManufacturerCode",
					"JucePlugin_AAXProductId=JucePlugin_PluginCode",
					"JucePlugin_AAXCategory=0",
					"JucePlugin_AAXDisableBypass=0",
					"JucePlugin_AAXDisableMultiMono=0",
					"JucePlugin_IAAType=0x61757278",
					"JucePlugin_IAASubType=JucePlugin_PluginCode",
					"JucePlugin_IAAName=\\\"EGURT:\\ Color\\ Assistant\\\"",
					"JucePlugin_VSTNumMidiInputs=16",
					"JucePlugin_VSTNumMidiOutputs=16",
					"JucePlugin_ARAContentTypes=0",
					"JucePlugin_ARATransformationFlags=0",
					"JucePlugin_ARAFactoryID=\\\"com.yourcompany.ColorAssistant.factory\\\"",
					"JucePlugin_ARADocumentArchiveID=\\\"com.yourcompany.ColorAssistant.aradocumentarchive.1.0.0\\\"",
					"JucePlugin_ARACompatibleArchiveIDs=\\\"\\\"",
					"JUCE_STANDALONE_APPLICATION=JucePlugin_Build_Standalone",
					"JUCER_XCODE_MAC_F6D2F4CF=1",
					"JUCE_APP_VERSION=1.0.0",
					"JUCE_APP_VERSION_HEX=0x10000",
				);
				GCC_VERSION = com.apple.compilers.llvm.clang.1_0;
				GENERATE_PKGINFO_FILE = YES;
				HEADER_SEARCH_PATHS = (
					"$(HOME)/JUCE/modules/juce_audio_processors/format_types/VST3_SDK",
					"$(SRCROOT)/../../JuceLibraryCode",
					"$(HOME)/JUCE/modules",
					"$(HOME)/JUCE/modules/juce_audio_plugin_client/AU",
					"$(inherited)",
				);
				INFOPLIST_FILE = Info-AU.plist;
				INFOPLIST_PREPROCESS = NO;
				INSTALL_PATH = "$(HOME)/Library/Audio/Plug-Ins/Components/";
				LIBRARY_STYLE = Bundle;
				MACOSX_DEPLOYMENT_TARGET = 10.13;
				MTL_HEADER_SEARCH_PATHS = "$(HOME)/JUCE/modules/juce_audio_processors/format_types/VST3_SDK $(SRCROOT)/../../JuceLibraryCode $(HOME)/JUCE/modules $(HOME)/JUCE/modules/juce_audio_plugin_client/AU";
				OTHER_LDFLAGS = "-bundle -lColor\\ Assistant";
				OTHER_REZFLAGS = "-d ppc_$ppc -d i386_$i386 -d ppc64_$ppc64 -d x86_64_$x86_64 -d arm64_$arm64 -I /System/Library/Frameworks/CoreServices.framework/Frameworks/CarbonCore.framework/Versions/A/Headers -I \"$(DEVELOPER_DIR)/Extras/CoreAudio/AudioUnits/AUPublic/AUBase\" -I \"$(DEVELOPER_DIR)/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks/AudioUnit.framework/Headers\"";
				PRODUCT_BUNDLE_IDENTIFIER = com.yourcompany.ColorAssistant;
				PRODUCT_NAME = "Color Assistant";
				USE_HEADERMAP = NO;
				VALID_ARCHS = "i386 x86_64 arm64 arm64e";
				WRAPPER_EXTENSION = component;
			};
			name = Debug;
		};
		B09E567E25F881CDA0DCD788 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "c++17";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_LINK_OBJC_RUNTIME = NO;
				COMBINE_HIDPI_IMAGES = YES;
				CONFIGURATION_BUILD_DIR = "$(PROJECT_DIR)/build/$(CONFIGURATION)";
				DEAD_CODE_STRIPPING = YES;
				EXCLUDED_ARCHS = "";
				GCC_GENERATE_DEBUGGING_SYMBOLS = NO;
				GCC_OPTIMIZATION_LEVEL = 3;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"_NDEBUG=1",
					"NDEBUG=1",
					"JUCE_PROJUCER_VERSION=0x80007",
					"JUCE_MODULE_AVAILABLE_juce_analytics=1",
					"JUCE_MODULE_AVAILABLE_juce_animation=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_basics=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_devices=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_formats=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_plugin_client=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_processors=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_utils=1",
					"JUCE_MODULE_AVAILABLE_juce_core=1",
					"JUCE_MODULE_AVAILABLE_juce_data_structures=1",
					"JUCE_MODULE_AVAILABLE_juce_dsp=1",
					"JUCE_MODULE_AVAILABLE_juce_events=1",
					"JUCE_MODULE_AVAILABLE_juce_graphics=1",
					"JUCE_MODULE_AVAILABLE_juce_gui_basics=1",
					"JUCE_MODULE_AVAILABLE_juce_gui_extra=1",
					"JUCE_MODULE_AVAILABLE_juce_midi_ci=1",
					"JUCE_MODULE_AVAILABLE_juce_osc=1",
					"JUCE_GLOBAL_MODULE_SETTINGS_INCLUDED=1",
					"JUCE_VST3_CAN_REPLACE_VST2=0",
					"JUCE_STRICT_REFCOUNTEDPOINTER=1",
					"JucePlugin_Build_VST=0",
					"JucePlugin_Build_VST3=0",
					"JucePlugin_Build_AU=0",
					"JucePlugin_Build_AUv3=0",
					"JucePlugin_Build_AAX=0",
					"JucePlugin_Build_Standalone=0",
					"JucePlugin_Build_Unity=0",
					"JucePlugin_Build_LV2=0",
					"JucePlugin_Enable_IAA=0",
					"JucePlugin_Enable_ARA=0",
					"JucePlugin_Name=\\\"Color\\ Assistant\\\"",
					"JucePlugin_Desc=\\\"Color\\ Assistant\\\"",
					"JucePlugin_Manufacturer=\\\"EGURT\\\"",
					"JucePlugin_ManufacturerWebsite=\\\"www.yourcompany.com\\\"",
					"JucePlugin_ManufacturerEmail=\\\"\\\"",
					"JucePlugin_ManufacturerCode=0x4d616e75",
					"JucePlugin_PluginCode=0x4b74617a",
					"JucePlugin_IsSynth=0",
					"JucePlugin_WantsMidiInput=0",
					"JucePlugin_ProducesMidiOutput=0",
					"JucePlugin_IsMidiEffect=0",
					"JucePlugin_EditorRequiresKeyboardFocus=0",
					"JucePlugin_Version=1.0.0",
					"JucePlugin_VersionCode=0x10000",
					"JucePlugin_VersionString=\\\"1.0.0\\\"",
					"JucePlugin_VSTUniqueID=JucePlugin_PluginCode",
					"JucePlugin_VSTCategory=kPlugCategEffect",
					"JucePlugin_Vst3Category=\\\"Fx\\\"",
					"JucePlugin_AUMainType=\\'aufx\\'",
					"JucePlugin_AUSubType=JucePlugin_PluginCode",
					"JucePlugin_AUExportPrefix=ColorAssistantAU",
					"JucePlugin_AUExportPrefixQuoted=\\\"ColorAssistantAU\\\"",
					"JucePlugin_AUManufacturerCode=JucePlugin_ManufacturerCode",
					"JucePlugin_CFBundleIdentifier=com.yourcompany.ColorAssistant",
					"JucePlugin_AAXIdentifier=com.yourcompany.ColorAssistant",
					"JucePlugin_AAXManufacturerCode=JucePlugin_ManufacturerCode",
					"JucePlugin_AAXProductId=JucePlugin_PluginCode",
					"JucePlugin_AAXCategory=0",
					"JucePlugin_AAXDisableBypass=0",
					"JucePlugin_AAXDisableMultiMono=0",
					"JucePlugin_IAAType=0x61757278",
					"JucePlugin_IAASubType=JucePlugin_PluginCode",
					"JucePlugin_IAAName=\\\"EGURT:\\ Color\\ Assistant\\\"",
					"JucePlugin_VSTNumMidiInputs=16",
					"JucePlugin_VSTNumMidiOutputs=16",
					"JucePlugin_ARAContentTypes=0",
					"JucePlugin_ARATransformationFlags=0",
					"JucePlugin_ARAFactoryID=\\\"com.yourcompany.ColorAssistant.factory\\\"",
					"JucePlugin_ARADocumentArchiveID=\\\"com.yourcompany.ColorAssistant.aradocumentarchive.1.0.0\\\"",
					"JucePlugin_ARACompatibleArchiveIDs=\\\"\\\"",
					"JUCE_STANDALONE_APPLICATION=JucePlugin_Build_Standalone",
					"JUCER_XCODE_MAC_F6D2F4CF=1",
					"JUCE_APP_VERSION=1.0.0",
					"JUCE_APP_VERSION_HEX=0x10000",
				);
				GCC_VERSION = com.apple.compilers.llvm.clang.1_0;
				HEADER_SEARCH_PATHS = (
					"$(HOME)/JUCE/modules/juce_audio_processors/format_types/VST3_SDK",
					"$(SRCROOT)/../../JuceLibraryCode",
					"$(HOME)/JUCE/modules",
					"$(HOME)/JUCE/modules/juce_audio_plugin_client/AU",
					"$(inherited)",
				);
				INFOPLIST_FILE = Info-VST3_Manifest_Helper.plist;
				INFOPLIST_PREPROCESS = NO;
				LLVM_LTO = YES;
				MACOSX_DEPLOYMENT_TARGET = 10.13;
				MTL_HEADER_SEARCH_PATHS = "$(HOME)/JUCE/modules/juce_audio_processors/format_types/VST3_SDK $(SRCROOT)/../../JuceLibraryCode $(HOME)/JUCE/modules $(HOME)/JUCE/modules/juce_audio_plugin_client/AU";
				PRODUCT_BUNDLE_IDENTIFIER = com.yourcompany.ColorAssistant;
				PRODUCT_NAME = "juce_vst3_helper";
				USE_HEADERMAP = NO;
				VALID_ARCHS = "i386 x86_64 arm64 arm64e";
			};
			name = Release;
		};
		B1987FCABF2B440B142CB53B /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = NO;
				GCC_C_LANGUAGE_STANDARD = c11;
				GCC_INLINES_ARE_PRIVATE_EXTERN = YES;
				GCC_MODEL_TUNING = G5;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_SYMBOLS_PRIVATE_EXTERN = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES;
				GCC_WARN_CHECK_SWITCH_STATEMENTS = YES;
				GCC_WARN_MISSING_PARENTHESES = YES;
				GCC_WARN_NON_VIRTUAL_DESTRUCTOR = YES;
				GCC_WARN_TYPECHECK_CALLS_TO_PRINTF = YES;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_CODE_SIGN_FLAGS = --timestamp;
				PRODUCT_NAME = "Color Assistant";
				SDKROOT = macosx;
				WARNING_CFLAGS = "-Wreorder";
				ZERO_LINK = NO;
			};
			name = Debug;
		};
		BAE10B927B432ED9C2E3B430 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "c++17";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_LINK_OBJC_RUNTIME = NO;
				COMBINE_HIDPI_IMAGES = YES;
				CONFIGURATION_BUILD_DIR = "$(PROJECT_DIR)/build/$(CONFIGURATION)";
				COPY_PHASE_STRIP = NO;
				EXCLUDED_ARCHS = "";
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"_DEBUG=1",
					"DEBUG=1",
					"JUCE_PROJUCER_VERSION=0x80007",
					"JUCE_MODULE_AVAILABLE_juce_analytics=1",
					"JUCE_MODULE_AVAILABLE_juce_animation=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_basics=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_devices=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_formats=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_plugin_client=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_processors=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_utils=1",
					"JUCE_MODULE_AVAILABLE_juce_core=1",
					"JUCE_MODULE_AVAILABLE_juce_data_structures=1",
					"JUCE_MODULE_AVAILABLE_juce_dsp=1",
					"JUCE_MODULE_AVAILABLE_juce_events=1",
					"JUCE_MODULE_AVAILABLE_juce_graphics=1",
					"JUCE_MODULE_AVAILABLE_juce_gui_basics=1",
					"JUCE_MODULE_AVAILABLE_juce_gui_extra=1",
					"JUCE_MODULE_AVAILABLE_juce_midi_ci=1",
					"JUCE_MODULE_AVAILABLE_juce_osc=1",
					"JUCE_GLOBAL_MODULE_SETTINGS_INCLUDED=1",
					"JUCE_VST3_CAN_REPLACE_VST2=0",
					"JUCE_STRICT_REFCOUNTEDPOINTER=1",
					"JucePlugin_Build_VST=0",
					"JucePlugin_Build_VST3=0",
					"JucePlugin_Build_AU=0",
					"JucePlugin_Build_AUv3=0",
					"JucePlugin_Build_AAX=0",
					"JucePlugin_Build_Standalone=0",
					"JucePlugin_Build_Unity=0",
					"JucePlugin_Build_LV2=0",
					"JucePlugin_Enable_IAA=0",
					"JucePlugin_Enable_ARA=0",
					"JucePlugin_Name=\\\"Color\\ Assistant\\\"",
					"JucePlugin_Desc=\\\"Color\\ Assistant\\\"",
					"JucePlugin_Manufacturer=\\\"EGURT\\\"",
					"JucePlugin_ManufacturerWebsite=\\\"www.yourcompany.com\\\"",
					"JucePlugin_ManufacturerEmail=\\\"\\\"",
					"JucePlugin_ManufacturerCode=0x4d616e75",
					"JucePlugin_PluginCode=0x4b74617a",
					"JucePlugin_IsSynth=0",
					"JucePlugin_WantsMidiInput=0",
					"JucePlugin_ProducesMidiOutput=0",
					"JucePlugin_IsMidiEffect=0",
					"JucePlugin_EditorRequiresKeyboardFocus=0",
					"JucePlugin_Version=1.0.0",
					"JucePlugin_VersionCode=0x10000",
					"JucePlugin_VersionString=\\\"1.0.0\\\"",
					"JucePlugin_VSTUniqueID=JucePlugin_PluginCode",
					"JucePlugin_VSTCategory=kPlugCategEffect",
					"JucePlugin_Vst3Category=\\\"Fx\\\"",
					"JucePlugin_AUMainType=\\'aufx\\'",
					"JucePlugin_AUSubType=JucePlugin_PluginCode",
					"JucePlugin_AUExportPrefix=ColorAssistantAU",
					"JucePlugin_AUExportPrefixQuoted=\\\"ColorAssistantAU\\\"",
					"JucePlugin_AUManufacturerCode=JucePlugin_ManufacturerCode",
					"JucePlugin_CFBundleIdentifier=com.yourcompany.ColorAssistant",
					"JucePlugin_AAXIdentifier=com.yourcompany.ColorAssistant",
					"JucePlugin_AAXManufacturerCode=JucePlugin_ManufacturerCode",
					"JucePlugin_AAXProductId=JucePlugin_PluginCode",
					"JucePlugin_AAXCategory=0",
					"JucePlugin_AAXDisableBypass=0",
					"JucePlugin_AAXDisableMultiMono=0",
					"JucePlugin_IAAType=0x61757278",
					"JucePlugin_IAASubType=JucePlugin_PluginCode",
					"JucePlugin_IAAName=\\\"EGURT:\\ Color\\ Assistant\\\"",
					"JucePlugin_VSTNumMidiInputs=16",
					"JucePlugin_VSTNumMidiOutputs=16",
					"JucePlugin_ARAContentTypes=0",
					"JucePlugin_ARATransformationFlags=0",
					"JucePlugin_ARAFactoryID=\\\"com.yourcompany.ColorAssistant.factory\\\"",
					"JucePlugin_ARADocumentArchiveID=\\\"com.yourcompany.ColorAssistant.aradocumentarchive.1.0.0\\\"",
					"JucePlugin_ARACompatibleArchiveIDs=\\\"\\\"",
					"JUCE_STANDALONE_APPLICATION=JucePlugin_Build_Standalone",
					"JUCER_XCODE_MAC_F6D2F4CF=1",
					"JUCE_APP_VERSION=1.0.0",
					"JUCE_APP_VERSION_HEX=0x10000",
				);
				GCC_VERSION = com.apple.compilers.llvm.clang.1_0;
				HEADER_SEARCH_PATHS = (
					"$(HOME)/JUCE/modules/juce_audio_processors/format_types/VST3_SDK",
					"$(SRCROOT)/../../JuceLibraryCode",
					"$(HOME)/JUCE/modules",
					"$(HOME)/JUCE/modules/juce_audio_plugin_client/AU",
					"$(inherited)",
				);
				INFOPLIST_FILE = Info-VST3_Manifest_Helper.plist;
				INFOPLIST_PREPROCESS = NO;
				MACOSX_DEPLOYMENT_TARGET = 10.13;
				MTL_HEADER_SEARCH_PATHS = "$(HOME)/JUCE/modules/juce_audio_processors/format_types/VST3_SDK $(SRCROOT)/../../JuceLibraryCode $(HOME)/JUCE/modules $(HOME)/JUCE/modules/juce_audio_plugin_client/AU";
				PRODUCT_BUNDLE_IDENTIFIER = com.yourcompany.ColorAssistant;
				PRODUCT_NAME = "juce_vst3_helper";
				USE_HEADERMAP = NO;
				VALID_ARCHS = "i386 x86_64 arm64 arm64e";
			};
			name = Debug;
		};
		D49462E704F17EDF12513065 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				MACOSX_DEPLOYMENT_TARGET = 10.13;
				SDKROOT = macosx;
			};
			name = Debug;
		};
		D79B84BF467DBBA096053474 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "c++17";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_LINK_OBJC_RUNTIME = NO;
				COMBINE_HIDPI_IMAGES = YES;
				CONFIGURATION_BUILD_DIR = "$(PROJECT_DIR)/build/$(CONFIGURATION)";
				COPY_PHASE_STRIP = NO;
				EXCLUDED_ARCHS = "";
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"_DEBUG=1",
					"DEBUG=1",
					"JUCE_PROJUCER_VERSION=0x80007",
					"JUCE_MODULE_AVAILABLE_juce_analytics=1",
					"JUCE_MODULE_AVAILABLE_juce_animation=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_basics=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_devices=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_formats=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_plugin_client=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_processors=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_utils=1",
					"JUCE_MODULE_AVAILABLE_juce_core=1",
					"JUCE_MODULE_AVAILABLE_juce_data_structures=1",
					"JUCE_MODULE_AVAILABLE_juce_dsp=1",
					"JUCE_MODULE_AVAILABLE_juce_events=1",
					"JUCE_MODULE_AVAILABLE_juce_graphics=1",
					"JUCE_MODULE_AVAILABLE_juce_gui_basics=1",
					"JUCE_MODULE_AVAILABLE_juce_gui_extra=1",
					"JUCE_MODULE_AVAILABLE_juce_midi_ci=1",
					"JUCE_MODULE_AVAILABLE_juce_osc=1",
					"JUCE_GLOBAL_MODULE_SETTINGS_INCLUDED=1",
					"JUCE_VST3_CAN_REPLACE_VST2=0",
					"JUCE_STRICT_REFCOUNTEDPOINTER=1",
					"JucePlugin_Build_VST=0",
					"JucePlugin_Build_VST3=1",
					"JucePlugin_Build_AU=1",
					"JucePlugin_Build_AUv3=0",
					"JucePlugin_Build_AAX=0",
					"JucePlugin_Build_Standalone=1",
					"JucePlugin_Build_Unity=0",
					"JucePlugin_Build_LV2=0",
					"JucePlugin_Enable_IAA=0",
					"JucePlugin_Enable_ARA=0",
					"JucePlugin_Name=\\\"Color\\ Assistant\\\"",
					"JucePlugin_Desc=\\\"Color\\ Assistant\\\"",
					"JucePlugin_Manufacturer=\\\"EGURT\\\"",
					"JucePlugin_ManufacturerWebsite=\\\"www.yourcompany.com\\\"",
					"JucePlugin_ManufacturerEmail=\\\"\\\"",
					"JucePlugin_ManufacturerCode=0x4d616e75",
					"JucePlugin_PluginCode=0x4b74617a",
					"JucePlugin_IsSynth=0",
					"JucePlugin_WantsMidiInput=0",
					"JucePlugin_ProducesMidiOutput=0",
					"JucePlugin_IsMidiEffect=0",
					"JucePlugin_EditorRequiresKeyboardFocus=0",
					"JucePlugin_Version=1.0.0",
					"JucePlugin_VersionCode=0x10000",
					"JucePlugin_VersionString=\\\"1.0.0\\\"",
					"JucePlugin_VSTUniqueID=JucePlugin_PluginCode",
					"JucePlugin_VSTCategory=kPlugCategEffect",
					"JucePlugin_Vst3Category=\\\"Fx\\\"",
					"JucePlugin_AUMainType=\\'aufx\\'",
					"JucePlugin_AUSubType=JucePlugin_PluginCode",
					"JucePlugin_AUExportPrefix=ColorAssistantAU",
					"JucePlugin_AUExportPrefixQuoted=\\\"ColorAssistantAU\\\"",
					"JucePlugin_AUManufacturerCode=JucePlugin_ManufacturerCode",
					"JucePlugin_CFBundleIdentifier=com.yourcompany.ColorAssistant",
					"JucePlugin_AAXIdentifier=com.yourcompany.ColorAssistant",
					"JucePlugin_AAXManufacturerCode=JucePlugin_ManufacturerCode",
					"JucePlugin_AAXProductId=JucePlugin_PluginCode",
					"JucePlugin_AAXCategory=0",
					"JucePlugin_AAXDisableBypass=0",
					"JucePlugin_AAXDisableMultiMono=0",
					"JucePlugin_IAAType=0x61757278",
					"JucePlugin_IAASubType=JucePlugin_PluginCode",
					"JucePlugin_IAAName=\\\"EGURT:\\ Color\\ Assistant\\\"",
					"JucePlugin_VSTNumMidiInputs=16",
					"JucePlugin_VSTNumMidiOutputs=16",
					"JucePlugin_ARAContentTypes=0",
					"JucePlugin_ARATransformationFlags=0",
					"JucePlugin_ARAFactoryID=\\\"com.yourcompany.ColorAssistant.factory\\\"",
					"JucePlugin_ARADocumentArchiveID=\\\"com.yourcompany.ColorAssistant.aradocumentarchive.1.0.0\\\"",
					"JucePlugin_ARACompatibleArchiveIDs=\\\"\\\"",
					"JUCE_STANDALONE_APPLICATION=JucePlugin_Build_Standalone",
					"JUCER_XCODE_MAC_F6D2F4CF=1",
					"JUCE_APP_VERSION=1.0.0",
					"JUCE_APP_VERSION_HEX=0x10000",
					"JUCE_SHARED_CODE=1",
				);
				GCC_VERSION = com.apple.compilers.llvm.clang.1_0;
				HEADER_SEARCH_PATHS = (
					"$(HOME)/JUCE/modules/juce_audio_processors/format_types/VST3_SDK",
					"$(SRCROOT)/../../JuceLibraryCode",
					"$(HOME)/JUCE/modules",
					"$(HOME)/JUCE/modules/juce_audio_plugin_client/AU",
					"$(inherited)",
				);
				INSTALL_PATH = "@executable_path/../Frameworks";
				MACOSX_DEPLOYMENT_TARGET = 10.13;
				MTL_HEADER_SEARCH_PATHS = "$(HOME)/JUCE/modules/juce_audio_processors/format_types/VST3_SDK $(SRCROOT)/../../JuceLibraryCode $(HOME)/JUCE/modules $(HOME)/JUCE/modules/juce_audio_plugin_client/AU";
				PRODUCT_BUNDLE_IDENTIFIER = com.yourcompany.ColorAssistant;
				PRODUCT_NAME = "Color Assistant";
				SKIP_INSTALL = YES;
				USE_HEADERMAP = NO;
				VALID_ARCHS = "i386 x86_64 arm64 arm64e";
			};
			name = Debug;
		};
		D7EBE7C8D339938611FB2835 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				MACOSX_DEPLOYMENT_TARGET = 10.13;
				SDKROOT = macosx;
			};
			name = Release;
		};
		DABD1C02F348C509A3A34490 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "c++17";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_LINK_OBJC_RUNTIME = NO;
				COMBINE_HIDPI_IMAGES = YES;
				CONFIGURATION_BUILD_DIR = "$(PROJECT_DIR)/build/$(CONFIGURATION)";
				DEAD_CODE_STRIPPING = YES;
				EXCLUDED_ARCHS = "";
				GCC_GENERATE_DEBUGGING_SYMBOLS = NO;
				GCC_OPTIMIZATION_LEVEL = 3;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"_NDEBUG=1",
					"NDEBUG=1",
					"JUCE_PROJUCER_VERSION=0x80007",
					"JUCE_MODULE_AVAILABLE_juce_analytics=1",
					"JUCE_MODULE_AVAILABLE_juce_animation=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_basics=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_devices=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_formats=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_plugin_client=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_processors=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_utils=1",
					"JUCE_MODULE_AVAILABLE_juce_core=1",
					"JUCE_MODULE_AVAILABLE_juce_data_structures=1",
					"JUCE_MODULE_AVAILABLE_juce_dsp=1",
					"JUCE_MODULE_AVAILABLE_juce_events=1",
					"JUCE_MODULE_AVAILABLE_juce_graphics=1",
					"JUCE_MODULE_AVAILABLE_juce_gui_basics=1",
					"JUCE_MODULE_AVAILABLE_juce_gui_extra=1",
					"JUCE_MODULE_AVAILABLE_juce_midi_ci=1",
					"JUCE_MODULE_AVAILABLE_juce_osc=1",
					"JUCE_GLOBAL_MODULE_SETTINGS_INCLUDED=1",
					"JUCE_VST3_CAN_REPLACE_VST2=0",
					"JUCE_STRICT_REFCOUNTEDPOINTER=1",
					"JucePlugin_Build_VST=0",
					"JucePlugin_Build_VST3=0",
					"JucePlugin_Build_AU=0",
					"JucePlugin_Build_AUv3=0",
					"JucePlugin_Build_AAX=0",
					"JucePlugin_Build_Standalone=1",
					"JucePlugin_Build_Unity=0",
					"JucePlugin_Build_LV2=0",
					"JucePlugin_Enable_IAA=0",
					"JucePlugin_Enable_ARA=0",
					"JucePlugin_Name=\\\"Color\\ Assistant\\\"",
					"JucePlugin_Desc=\\\"Color\\ Assistant\\\"",
					"JucePlugin_Manufacturer=\\\"EGURT\\\"",
					"JucePlugin_ManufacturerWebsite=\\\"www.yourcompany.com\\\"",
					"JucePlugin_ManufacturerEmail=\\\"\\\"",
					"JucePlugin_ManufacturerCode=0x4d616e75",
					"JucePlugin_PluginCode=0x4b74617a",
					"JucePlugin_IsSynth=0",
					"JucePlugin_WantsMidiInput=0",
					"JucePlugin_ProducesMidiOutput=0",
					"JucePlugin_IsMidiEffect=0",
					"JucePlugin_EditorRequiresKeyboardFocus=0",
					"JucePlugin_Version=1.0.0",
					"JucePlugin_VersionCode=0x10000",
					"JucePlugin_VersionString=\\\"1.0.0\\\"",
					"JucePlugin_VSTUniqueID=JucePlugin_PluginCode",
					"JucePlugin_VSTCategory=kPlugCategEffect",
					"JucePlugin_Vst3Category=\\\"Fx\\\"",
					"JucePlugin_AUMainType=\\'aufx\\'",
					"JucePlugin_AUSubType=JucePlugin_PluginCode",
					"JucePlugin_AUExportPrefix=ColorAssistantAU",
					"JucePlugin_AUExportPrefixQuoted=\\\"ColorAssistantAU\\\"",
					"JucePlugin_AUManufacturerCode=JucePlugin_ManufacturerCode",
					"JucePlugin_CFBundleIdentifier=com.yourcompany.ColorAssistant",
					"JucePlugin_AAXIdentifier=com.yourcompany.ColorAssistant",
					"JucePlugin_AAXManufacturerCode=JucePlugin_ManufacturerCode",
					"JucePlugin_AAXProductId=JucePlugin_PluginCode",
					"JucePlugin_AAXCategory=0",
					"JucePlugin_AAXDisableBypass=0",
					"JucePlugin_AAXDisableMultiMono=0",
					"JucePlugin_IAAType=0x61757278",
					"JucePlugin_IAASubType=JucePlugin_PluginCode",
					"JucePlugin_IAAName=\\\"EGURT:\\ Color\\ Assistant\\\"",
					"JucePlugin_VSTNumMidiInputs=16",
					"JucePlugin_VSTNumMidiOutputs=16",
					"JucePlugin_ARAContentTypes=0",
					"JucePlugin_ARATransformationFlags=0",
					"JucePlugin_ARAFactoryID=\\\"com.yourcompany.ColorAssistant.factory\\\"",
					"JucePlugin_ARADocumentArchiveID=\\\"com.yourcompany.ColorAssistant.aradocumentarchive.1.0.0\\\"",
					"JucePlugin_ARACompatibleArchiveIDs=\\\"\\\"",
					"JUCE_STANDALONE_APPLICATION=JucePlugin_Build_Standalone",
					"JUCER_XCODE_MAC_F6D2F4CF=1",
					"JUCE_APP_VERSION=1.0.0",
					"JUCE_APP_VERSION_HEX=0x10000",
				);
				GCC_VERSION = com.apple.compilers.llvm.clang.1_0;
				HEADER_SEARCH_PATHS = (
					"$(HOME)/JUCE/modules/juce_audio_processors/format_types/VST3_SDK",
					"$(SRCROOT)/../../JuceLibraryCode",
					"$(HOME)/JUCE/modules",
					"$(HOME)/JUCE/modules/juce_audio_plugin_client/AU",
					"$(inherited)",
				);
				INFOPLIST_FILE = Info-Standalone_Plugin.plist;
				INFOPLIST_PREPROCESS = NO;
				LLVM_LTO = YES;
				MACOSX_DEPLOYMENT_TARGET = 10.13;
				MTL_HEADER_SEARCH_PATHS = "$(HOME)/JUCE/modules/juce_audio_processors/format_types/VST3_SDK $(SRCROOT)/../../JuceLibraryCode $(HOME)/JUCE/modules $(HOME)/JUCE/modules/juce_audio_plugin_client/AU";
				OTHER_LDFLAGS = "-lColor\\ Assistant";
				PRODUCT_BUNDLE_IDENTIFIER = com.yourcompany.ColorAssistant;
				PRODUCT_NAME = "Color Assistant";
				USE_HEADERMAP = NO;
				VALID_ARCHS = "i386 x86_64 arm64 arm64e";
			};
			name = Release;
		};
		EE25FE2444774B598EF86D29 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "c++17";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_LINK_OBJC_RUNTIME = NO;
				COMBINE_HIDPI_IMAGES = YES;
				CONFIGURATION_BUILD_DIR = "$(PROJECT_DIR)/build/$(CONFIGURATION)";
				DEAD_CODE_STRIPPING = YES;
				EXCLUDED_ARCHS = "";
				GCC_GENERATE_DEBUGGING_SYMBOLS = NO;
				GCC_OPTIMIZATION_LEVEL = 3;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"_NDEBUG=1",
					"NDEBUG=1",
					"JUCE_PROJUCER_VERSION=0x80007",
					"JUCE_MODULE_AVAILABLE_juce_analytics=1",
					"JUCE_MODULE_AVAILABLE_juce_animation=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_basics=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_devices=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_formats=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_plugin_client=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_processors=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_utils=1",
					"JUCE_MODULE_AVAILABLE_juce_core=1",
					"JUCE_MODULE_AVAILABLE_juce_data_structures=1",
					"JUCE_MODULE_AVAILABLE_juce_dsp=1",
					"JUCE_MODULE_AVAILABLE_juce_events=1",
					"JUCE_MODULE_AVAILABLE_juce_graphics=1",
					"JUCE_MODULE_AVAILABLE_juce_gui_basics=1",
					"JUCE_MODULE_AVAILABLE_juce_gui_extra=1",
					"JUCE_MODULE_AVAILABLE_juce_midi_ci=1",
					"JUCE_MODULE_AVAILABLE_juce_osc=1",
					"JUCE_GLOBAL_MODULE_SETTINGS_INCLUDED=1",
					"JUCE_VST3_CAN_REPLACE_VST2=0",
					"JUCE_STRICT_REFCOUNTEDPOINTER=1",
					"JucePlugin_Build_VST=0",
					"JucePlugin_Build_VST3=1",
					"JucePlugin_Build_AU=0",
					"JucePlugin_Build_AUv3=0",
					"JucePlugin_Build_AAX=0",
					"JucePlugin_Build_Standalone=0",
					"JucePlugin_Build_Unity=0",
					"JucePlugin_Build_LV2=0",
					"JucePlugin_Enable_IAA=0",
					"JucePlugin_Enable_ARA=0",
					"JucePlugin_Name=\\\"Color\\ Assistant\\\"",
					"JucePlugin_Desc=\\\"Color\\ Assistant\\\"",
					"JucePlugin_Manufacturer=\\\"EGURT\\\"",
					"JucePlugin_ManufacturerWebsite=\\\"www.yourcompany.com\\\"",
					"JucePlugin_ManufacturerEmail=\\\"\\\"",
					"JucePlugin_ManufacturerCode=0x4d616e75",
					"JucePlugin_PluginCode=0x4b74617a",
					"JucePlugin_IsSynth=0",
					"JucePlugin_WantsMidiInput=0",
					"JucePlugin_ProducesMidiOutput=0",
					"JucePlugin_IsMidiEffect=0",
					"JucePlugin_EditorRequiresKeyboardFocus=0",
					"JucePlugin_Version=1.0.0",
					"JucePlugin_VersionCode=0x10000",
					"JucePlugin_VersionString=\\\"1.0.0\\\"",
					"JucePlugin_VSTUniqueID=JucePlugin_PluginCode",
					"JucePlugin_VSTCategory=kPlugCategEffect",
					"JucePlugin_Vst3Category=\\\"Fx\\\"",
					"JucePlugin_AUMainType=\\'aufx\\'",
					"JucePlugin_AUSubType=JucePlugin_PluginCode",
					"JucePlugin_AUExportPrefix=ColorAssistantAU",
					"JucePlugin_AUExportPrefixQuoted=\\\"ColorAssistantAU\\\"",
					"JucePlugin_AUManufacturerCode=JucePlugin_ManufacturerCode",
					"JucePlugin_CFBundleIdentifier=com.yourcompany.ColorAssistant",
					"JucePlugin_AAXIdentifier=com.yourcompany.ColorAssistant",
					"JucePlugin_AAXManufacturerCode=JucePlugin_ManufacturerCode",
					"JucePlugin_AAXProductId=JucePlugin_PluginCode",
					"JucePlugin_AAXCategory=0",
					"JucePlugin_AAXDisableBypass=0",
					"JucePlugin_AAXDisableMultiMono=0",
					"JucePlugin_IAAType=0x61757278",
					"JucePlugin_IAASubType=JucePlugin_PluginCode",
					"JucePlugin_IAAName=\\\"EGURT:\\ Color\\ Assistant\\\"",
					"JucePlugin_VSTNumMidiInputs=16",
					"JucePlugin_VSTNumMidiOutputs=16",
					"JucePlugin_ARAContentTypes=0",
					"JucePlugin_ARATransformationFlags=0",
					"JucePlugin_ARAFactoryID=\\\"com.yourcompany.ColorAssistant.factory\\\"",
					"JucePlugin_ARADocumentArchiveID=\\\"com.yourcompany.ColorAssistant.aradocumentarchive.1.0.0\\\"",
					"JucePlugin_ARACompatibleArchiveIDs=\\\"\\\"",
					"JUCE_STANDALONE_APPLICATION=JucePlugin_Build_Standalone",
					"JUCER_XCODE_MAC_F6D2F4CF=1",
					"JUCE_APP_VERSION=1.0.0",
					"JUCE_APP_VERSION_HEX=0x10000",
				);
				GCC_VERSION = com.apple.compilers.llvm.clang.1_0;
				GENERATE_PKGINFO_FILE = YES;
				HEADER_SEARCH_PATHS = (
					"$(HOME)/JUCE/modules/juce_audio_processors/format_types/VST3_SDK",
					"$(SRCROOT)/../../JuceLibraryCode",
					"$(HOME)/JUCE/modules",
					"$(HOME)/JUCE/modules/juce_audio_plugin_client/AU",
					"$(inherited)",
				);
				INFOPLIST_FILE = Info-VST3.plist;
				INFOPLIST_PREPROCESS = NO;
				INSTALL_PATH = "$(HOME)/Library/Audio/Plug-Ins/VST3/";
				LIBRARY_STYLE = Bundle;
				LLVM_LTO = YES;
				MACOSX_DEPLOYMENT_TARGET = 10.13;
				MTL_HEADER_SEARCH_PATHS = "$(HOME)/JUCE/modules/juce_audio_processors/format_types/VST3_SDK $(SRCROOT)/../../JuceLibraryCode $(HOME)/JUCE/modules $(HOME)/JUCE/modules/juce_audio_plugin_client/AU";
				OTHER_LDFLAGS = "-bundle -lColor\\ Assistant";
				PRODUCT_BUNDLE_IDENTIFIER = com.yourcompany.ColorAssistant;
				PRODUCT_NAME = "Color Assistant";
				USE_HEADERMAP = NO;
				VALID_ARCHS = "i386 x86_64 arm64 arm64e";
				WRAPPER_EXTENSION = vst3;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		136A6AB069C18963C78AAB00 = {
			isa = XCConfigurationList;
			buildConfigurations = (
				D79B84BF467DBBA096053474,
				4DC5A80FFBBFE358FE3712D8,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		21C348F4B78151F883D70623 = {
			isa = XCConfigurationList;
			buildConfigurations = (
				BAE10B927B432ED9C2E3B430,
				B09E567E25F881CDA0DCD788,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		3B1935D311C0CB5802628D60 = {
			isa = XCConfigurationList;
			buildConfigurations = (
				232F5FF35CF08D93860E7A6A,
				B1987FCABF2B440B142CB53B,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		436A3F4426EF0B829BF64EB0 = {
			isa = XCConfigurationList;
			buildConfigurations = (
				D49462E704F17EDF12513065,
				D7EBE7C8D339938611FB2835,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		6405337589F571975C2A8C49 = {
			isa = XCConfigurationList;
			buildConfigurations = (
				0FD1AB7C40F73424A1B759D5,
				DABD1C02F348C509A3A34490,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		810B8D8D31198509E3AE34F7 = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A5DC6FC5A0D26D82E10C2520,
				EE25FE2444774B598EF86D29,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		97186D2724F29C49130A7AD0 = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A606423C5EBC601E4E6EFA60,
				2457CA5B066CE2999767784B,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
/* End XCConfigurationList section */
	};
	rootObject = 723D454276EAF0AFEC307D20 /* Project object */;
}
