/*
  ==============================================================================

    MusicData.h
    Created: AI Chord Assistant Plugin
    Author:  AI Assistant

    This file contains the core music data structures for the AI Chord Assistant plugin.

  ==============================================================================
*/

#pragma once

#include <JuceHeader.h>

//==============================================================================
// 和弦类型枚举
enum class ChordType
{
    Major,          // 大三和弦
    Minor,          // 小三和弦
    Dominant7,      // 属七和弦
    Major7,         // 大七和弦
    Minor7,         // 小七和弦
    Diminished,     // 减三和弦
    Diminished7,    // 减七和弦
    HalfDiminished7,// 半减七和弦
    Augmented,      // 增三和弦
    Sus2,           // 挂二和弦
    Sus4,           // 挂四和弦
    Add9,           // 加九和弦
    Minor6,         // 小六和弦
    Major6,         // 大六和弦
    Ninth,          // 九和弦
    Eleventh,       // 十一和弦
    Thirteenth,     // 十三和弦
    Altered,        // 变化和弦
    Custom          // 自定义和弦
};

//==============================================================================
// 调式枚举
enum class KeySignature
{
    CMajor,         // C大调
    GMajor,         // G大调
    DMajor,         // D大调
    AMajor,         // A大调
    EMajor,         // E大调
    BMajor,         // B大调
    FSharpMajor,    // F#大调
    CSharpMajor,    // C#大调
    FMajor,         // F大调
    BFlatMajor,     // Bb大调
    EFlatMajor,     // Eb大调
    AFlatMajor,     // Ab大调
    DFlatMajor,     // Db大调
    GFlatMajor,     // Gb大调
    CFlatMajor,     // Cb大调
    
    AMinor,         // a小调
    EMinor,         // e小调
    BMinor,         // b小调
    FSharpMinor,    // f#小调
    CSharpMinor,    // c#小调
    GSharpMinor,    // g#小调
    DSharpMinor,    // d#小调
    ASharpMinor,    // a#小调
    DMinor,         // d小调
    GMinor,         // g小调
    CMinor,         // c小调
    FMinor,         // f小调
    BFlatMinor,     // bb小调
    EFlatMinor,     // eb小调
    AFlatMinor      // ab小调
};

//==============================================================================
// 拍号结构
struct TimeSignature
{
    int numerator;      // 分子 (每小节拍数)
    int denominator;    // 分母 (拍的单位)
    
    TimeSignature(int num = 4, int den = 4) : numerator(num), denominator(den) {}
    
    juce::String toString() const
    {
        return juce::String(numerator) + "/" + juce::String(denominator);
    }
    
    bool operator==(const TimeSignature& other) const
    {
        return numerator == other.numerator && denominator == other.denominator;
    }
};

//==============================================================================
// 和弦数据类
class ChordData
{
public:
    ChordData();
    ChordData(const juce::String& name, int root, ChordType type, int inversion = 0);
    
    // 基本属性
    juce::String chordName;     // 和弦名称 (如 "Cmaj7", "Am/G")
    int rootNote;               // 根音 (0-11, C=0)
    ChordType chordType;        // 和弦类型
    int inversion;              // 转位 (0=原位, 1=第一转位, 2=第二转位...)
    
    // 时间属性
    float startBeat;            // 起始拍 (从小节开始计算)
    float duration;             // 持续时长 (以拍为单位)
    int barNumber;              // 所在小节号 (从1开始)
    
    // 音符数据
    juce::Array<int> noteNumbers;   // MIDI音符号数组
    
    // 方法
    void generateNotes();           // 根据和弦类型生成音符
    juce::String toString() const;  // 转换为字符串表示
    bool isEmpty() const;           // 检查是否为空和弦
    
    // 静态方法
    static ChordData fromString(const juce::String& chordString);
    static juce::String chordTypeToString(ChordType type);
    static ChordType stringToChordType(const juce::String& typeString);
    
private:
    void updateChordName();         // 更新和弦名称
};

//==============================================================================
// 调式/拍号变化点
struct MusicChange
{
    int barNumber;              // 变化发生的小节
    float beatPosition;         // 变化发生的拍位置
    KeySignature newKey;        // 新调式 (可选)
    TimeSignature newTimeSignature; // 新拍号 (可选)
    bool hasKeyChange;          // 是否有调式变化
    bool hasTimeSignatureChange; // 是否有拍号变化
    
    MusicChange(int bar, float beat) 
        : barNumber(bar), beatPosition(beat), hasKeyChange(false), hasTimeSignatureChange(false) {}
};

//==============================================================================
// 音乐段落类
class MusicSection
{
public:
    MusicSection();
    ~MusicSection();
    
    // 基本属性
    int numberOfBars;               // 小节数
    KeySignature globalKey;         // 全局调式
    TimeSignature globalTimeSignature; // 全局拍号
    float bpm;                      // 速度
    bool syncWithDAW;               // 是否与DAW同步BPM
    
    // 和弦数据
    juce::Array<ChordData> chords;  // 和弦列表
    juce::Array<MusicChange> changes; // 调式/拍号变化列表
    
    // 方法
    void addChord(const ChordData& chord);
    void removeChord(int index);
    void updateChord(int index, const ChordData& newChord);
    ChordData* getChordAt(int bar, float beat);
    
    void addMusicChange(const MusicChange& change);
    KeySignature getKeyAt(int bar, float beat) const;
    TimeSignature getTimeSignatureAt(int bar, float beat) const;
    
    void clear();
    juce::String toJsonString() const;      // 序列化为JSON
    void fromJsonString(const juce::String& json); // 从JSON反序列化
    
    // MIDI相关
    juce::MidiMessageSequence generateMidiSequence() const;
    void exportToMidiFile(const juce::File& file) const;
    
private:
    void sortChords();              // 按时间顺序排序和弦
    void sortChanges();             // 按时间顺序排序变化点
};

//==============================================================================
// 音乐理论工具类
class MusicTheoryUtils
{
public:
    // 音符名称转换
    static juce::String noteNumberToName(int noteNumber);
    static int noteNameToNumber(const juce::String& noteName);
    
    // 调式相关
    static juce::String keySignatureToString(KeySignature key);
    static KeySignature stringToKeySignature(const juce::String& keyString);
    static juce::Array<int> getScaleNotes(KeySignature key);
    
    // 和弦相关
    static juce::Array<int> getChordIntervals(ChordType type);
    static juce::Array<int> buildChord(int root, ChordType type, int inversion = 0);
    static bool isChordInKey(const ChordData& chord, KeySignature key);
    
    // 音程计算
    static int getInterval(int note1, int note2);
    static juce::String getIntervalName(int semitones);
    
private:
    static const juce::StringArray noteNames;
    static const juce::StringArray keyNames;
};
