/*
  ==============================================================================

    This file contains the basic framework code for a JUCE plugin editor.

  ==============================================================================
*/

#include "PluginProcessor.h"
#include "PluginEditor.h"

//==============================================================================
ColorAssistantAudioProcessorEditor::ColorAssistantAudioProcessorEditor (ColorAssistantAudioProcessor& p)
    : AudioProcessorEditor (&p), audioProcessor (p)
{
    // Setup main components
    addAndMakeVisible(musicSectionEditor);
    addAndMakeVisible(playbackControls);

    // Initialize music section editor with processor data
    musicSectionEditor.setMusicSection(&audioProcessor.getMusicSection());

    // Setup callbacks
    musicSectionEditor.onSectionChanged = [this]() { handleSectionChanged(); };

    playbackControls.onPlayClicked = [this]() { handlePlayClicked(); };
    playbackControls.onPauseClicked = [this]() { handlePauseClicked(); };
    playbackControls.onStopClicked = [this]() { handleStopClicked(); };
    playbackControls.onLoopChanged = [this](bool loop) { handleLoopChanged(loop); };
    playbackControls.onBPMChanged = [this](float bpm) { handleBPMChanged(bpm); };
    playbackControls.onSyncChanged = [this](bool sync) { handleSyncChanged(sync); };
    playbackControls.onVolumeChanged = [this](float volume) { handleVolumeChanged(volume); };

    // Setup AI components
    setupAIComponents();

    // Set initial size
    setSize(1200, 800);

    // Update controls with current processor state
    playbackControls.setBPM(audioProcessor.getBPM());
    playbackControls.setSyncWithDAW(audioProcessor.isSyncWithDAW());
}

ColorAssistantAudioProcessorEditor::~ColorAssistantAudioProcessorEditor()
{
}

//==============================================================================
void ColorAssistantAudioProcessorEditor::paint (juce::Graphics& g)
{
    g.fillAll(juce::Colours::darkgrey);

    // Draw title
    g.setColour(juce::Colours::white);
    g.setFont(juce::FontOptions(20.0f, juce::Font::bold));
    g.drawText("AI和弦助手", 10, 10, 200, 30, juce::Justification::left);
}

void ColorAssistantAudioProcessorEditor::resized()
{
    layoutComponents();
}

void ColorAssistantAudioProcessorEditor::layoutComponents()
{
    auto bounds = getLocalBounds();

    // Title area
    bounds.removeFromTop(40);

    // AI Status indicator at top
    aiStatusIndicator.setBounds(bounds.removeFromTop(30));
    bounds.removeFromTop(5);

    // Playback controls
    playbackControls.setBounds(bounds.removeFromTop(80));
    bounds.removeFromTop(10);

    // Main content area - three columns
    auto contentArea = bounds;

    // AI suggestion panel on the left (300px)
    auto suggestionArea = contentArea.removeFromLeft(300);
    aiSuggestionPanel.setBounds(suggestionArea);
    contentArea.removeFromLeft(10); // spacing

    // Chat interface on the right (350px)
    auto chatArea = contentArea.removeFromRight(350);
    enhancedChatInterface.setBounds(chatArea);
    contentArea.removeFromRight(10); // spacing

    // Music section editor in the center
    musicSectionEditor.setBounds(contentArea);

    // AI config dialog (hidden by default)
    if (aiConfigDialog.isVisible())
    {
        auto dialogBounds = getLocalBounds();
        aiConfigDialog.setBounds(dialogBounds.withSizeKeepingCentre(500, 600));
    }
}

void ColorAssistantAudioProcessorEditor::setupAIComponents()
{
    // Initialize AI Manager
    aiManager = std::make_unique<AIManager>();
    auto config = aiConfigManager.loadConfig();
    aiManager->setConfig(config);

    // Setup AI components
    addAndMakeVisible(enhancedChatInterface);
    addChildComponent(aiConfigDialog);
    addAndMakeVisible(aiStatusIndicator);
    addChildComponent(aiSuggestionPanel);

    // Connect AI manager to components
    enhancedChatInterface.setAIManager(aiManager.get());
    enhancedChatInterface.setMusicSection(&audioProcessor.getMusicSection());
    aiStatusIndicator.setAIManager(aiManager.get());

    // Setup callbacks
    enhancedChatInterface.onChordsGenerated = [this](const juce::Array<ChordData>& chords) {
        handleChordsGenerated(chords);
    };
    enhancedChatInterface.onConfigRequested = [this]() {
        handleAIConfigRequested();
    };

    aiConfigDialog.onConfigChanged = [this](const AIConfig& config) {
        handleAIConfigChanged(config);
    };
    aiConfigDialog.onCancelled = [this]() {
        aiConfigDialog.setVisible(false);
    };

    aiSuggestionPanel.onChordSelected = [this](const ChordData& chord) {
        handleChordSelected(chord);
    };
    aiSuggestionPanel.onAllChordsAccepted = [this](const juce::Array<ChordData>& chords) {
        handleAllChordsAccepted(chords);
    };

    // Set initial status
    aiStatusIndicator.setStatus("AI就绪", juce::Colours::green);
}

// Event handlers
void ColorAssistantAudioProcessorEditor::handleSectionChanged()
{
    // Music section has been modified
    audioProcessor.updateMusicSectionInEngine();
}

void ColorAssistantAudioProcessorEditor::handlePlayClicked()
{
    if (audioProcessor.isPlaying())
    {
        audioProcessor.stopPlayback();
        playbackControls.setPlaying(false);
    }
    else
    {
        audioProcessor.startPlayback();
        playbackControls.setPlaying(true);
    }
}

void ColorAssistantAudioProcessorEditor::handlePauseClicked()
{
    audioProcessor.pausePlayback();
    playbackControls.setPlaying(false);
}

void ColorAssistantAudioProcessorEditor::handleStopClicked()
{
    audioProcessor.stopPlayback();
    playbackControls.setPlaying(false);
}

void ColorAssistantAudioProcessorEditor::handleLoopChanged(bool loop)
{
    audioProcessor.setLooping(loop);
}

void ColorAssistantAudioProcessorEditor::handleBPMChanged(float bpm)
{
    audioProcessor.setBPM(bpm);
}

void ColorAssistantAudioProcessorEditor::handleSyncChanged(bool sync)
{
    audioProcessor.setSyncWithDAW(sync);
    if (sync)
    {
        playbackControls.setBPM(audioProcessor.getBPM());
    }
}

void ColorAssistantAudioProcessorEditor::handleVolumeChanged(float volume)
{
    audioProcessor.setVolume(volume);
}

// AI Event handlers
void ColorAssistantAudioProcessorEditor::handleAIConfigRequested()
{
    auto currentConfig = aiManager->getConfig();
    aiConfigDialog.showDialog(currentConfig);
}

void ColorAssistantAudioProcessorEditor::handleAIConfigChanged(const AIConfig& config)
{
    aiManager->setConfig(config);
    aiConfigManager.saveConfig(config);
    aiStatusIndicator.setStatus("配置已更新", juce::Colours::green);
}

void ColorAssistantAudioProcessorEditor::handleChordsGenerated(const juce::Array<ChordData>& chords)
{
    if (!chords.isEmpty())
    {
        // 显示AI建议面板
        aiSuggestionPanel.showSuggestions(chords, "AI生成的和弦建议");
        aiStatusIndicator.setStatus("生成了 " + juce::String(chords.size()) + " 个和弦建议", juce::Colours::blue);
    }
}

void ColorAssistantAudioProcessorEditor::handleChordSelected(const ChordData& chord)
{
    // 将选中的和弦添加到音乐段落
    auto& section = audioProcessor.getMusicSection();
    section.addChord(chord);

    // 更新UI
    handleSectionChanged();
    aiStatusIndicator.setStatus("已添加和弦: " + chord.chordName, juce::Colours::green);
}

void ColorAssistantAudioProcessorEditor::handleAllChordsAccepted(const juce::Array<ChordData>& chords)
{
    // 将所有和弦添加到音乐段落
    auto& section = audioProcessor.getMusicSection();
    for (const auto& chord : chords)
    {
        section.addChord(chord);
    }

    // 更新UI
    handleSectionChanged();
    aiStatusIndicator.setStatus("已添加 " + juce::String(chords.size()) + " 个和弦", juce::Colours::green);
}
