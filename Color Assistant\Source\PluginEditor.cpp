/*
  ==============================================================================

    This file contains the basic framework code for a JUCE plugin editor.

  ==============================================================================
*/

#include "PluginProcessor.h"
#include "PluginEditor.h"

//==============================================================================
ColorAssistantAudioProcessorEditor::ColorAssistantAudioProcessorEditor (ColorAssistantAudioProcessor& p)
    : AudioProcessorEditor (&p), audioProcessor (p)
{
    // Setup main components
    addAndMakeVisible(musicSectionEditor);
    addAndMakeVisible(playbackControls);

    // Initialize music section editor with processor data
    musicSectionEditor.setMusicSection(&audioProcessor.getMusicSection());

    // Setup callbacks
    musicSectionEditor.onSectionChanged = [this]() { handleSectionChanged(); };

    playbackControls.onPlayClicked = [this]() { handlePlayClicked(); };
    playbackControls.onStopClicked = [this]() { handleStopClicked(); };
    playbackControls.onBPMChanged = [this](float bpm) { handleBPMChanged(bpm); };
    playbackControls.onSyncChanged = [this](bool sync) { handleSyncChanged(sync); };
    playbackControls.onVolumeChanged = [this](float volume) { handleVolumeChanged(volume); };

    // Setup chat interface
    setupChatInterface();

    // Set initial size
    setSize(1000, 700);

    // Update controls with current processor state
    playbackControls.setBPM(audioProcessor.getBPM());
    playbackControls.setSyncWithDAW(audioProcessor.isSyncWithDAW());
}

ColorAssistantAudioProcessorEditor::~ColorAssistantAudioProcessorEditor()
{
}

//==============================================================================
void ColorAssistantAudioProcessorEditor::paint (juce::Graphics& g)
{
    g.fillAll(juce::Colours::darkgrey);

    // Draw title
    g.setColour(juce::Colours::white);
    g.setFont(juce::FontOptions(20.0f, juce::Font::bold));
    g.drawText("AI和弦助手", 10, 10, 200, 30, juce::Justification::left);
}

void ColorAssistantAudioProcessorEditor::resized()
{
    layoutComponents();
}

void ColorAssistantAudioProcessorEditor::layoutComponents()
{
    auto bounds = getLocalBounds();

    // Title area
    bounds.removeFromTop(40);

    // Playback controls at top
    playbackControls.setBounds(bounds.removeFromTop(80));
    bounds.removeFromTop(10);

    // Main content area - split between music editor and chat
    auto contentArea = bounds;
    auto chatArea = contentArea.removeFromRight(300);
    contentArea.removeFromRight(10); // spacing

    // Music section editor takes the left side
    musicSectionEditor.setBounds(contentArea);

    // Chat interface on the right
    chatLabel.setBounds(chatArea.removeFromTop(25));
    chatArea.removeFromTop(5);

    auto chatInputArea = chatArea.removeFromBottom(100);
    generateButton.setBounds(chatInputArea.removeFromBottom(30));
    chatInputArea.removeFromBottom(5);
    chatInput.setBounds(chatInputArea);

    chatArea.removeFromBottom(10);
    chatOutput.setBounds(chatArea);
}

void ColorAssistantAudioProcessorEditor::setupChatInterface()
{
    addAndMakeVisible(chatLabel);
    chatLabel.setText("AI对话", juce::dontSendNotification);
    chatLabel.setFont(juce::Font(14.0f, juce::Font::bold));

    addAndMakeVisible(chatInput);
    chatInput.setMultiLine(true);
    chatInput.setReturnKeyStartsNewLine(true);
    chatInput.setTextToShowWhenEmpty("输入您的和弦要求...", juce::Colours::grey);

    addAndMakeVisible(chatOutput);
    chatOutput.setMultiLine(true);
    chatOutput.setReadOnly(true);
    chatOutput.setScrollbarsShown(true);

    addAndMakeVisible(generateButton);
    generateButton.setButtonText("生成和弦");
    generateButton.onClick = [this]() { handleGenerateClicked(); };
}

// Event handlers
void ColorAssistantAudioProcessorEditor::handleSectionChanged()
{
    // Music section has been modified
    // Could trigger auto-save or other updates here
}

void ColorAssistantAudioProcessorEditor::handlePlayClicked()
{
    if (audioProcessor.isPlaying())
    {
        audioProcessor.stopPlayback();
        playbackControls.setPlaying(false);
    }
    else
    {
        audioProcessor.startPlayback();
        playbackControls.setPlaying(true);
    }
}

void ColorAssistantAudioProcessorEditor::handleStopClicked()
{
    audioProcessor.stopPlayback();
    playbackControls.setPlaying(false);
}

void ColorAssistantAudioProcessorEditor::handleBPMChanged(float bpm)
{
    audioProcessor.setBPM(bpm);
}

void ColorAssistantAudioProcessorEditor::handleSyncChanged(bool sync)
{
    audioProcessor.setSyncWithDAW(sync);
    if (sync)
    {
        playbackControls.setBPM(audioProcessor.getBPM());
    }
}

void ColorAssistantAudioProcessorEditor::handleVolumeChanged(float volume)
{
    // TODO: Implement volume control
}

void ColorAssistantAudioProcessorEditor::handleGenerateClicked()
{
    auto userInput = chatInput.getText();
    if (userInput.isEmpty())
        return;

    // Add user input to chat output
    auto currentOutput = chatOutput.getText();
    currentOutput += "用户: " + userInput + "\n\n";

    // TODO: Send request to AI backend
    // For now, add a placeholder response
    currentOutput += "AI: 收到您的请求，正在生成和弦...\n";
    currentOutput += "建议使用 C-Am-F-G 进行，这是一个经典的流行音乐和弦进行。\n\n";

    chatOutput.setText(currentOutput);
    chatOutput.moveCaretToEnd();

    // Clear input
    chatInput.clear();
}
