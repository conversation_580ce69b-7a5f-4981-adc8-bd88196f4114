<?xml version="1.0" encoding="UTF-8"?>

<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist>
  <dict>
    <key>CFBundleExecutable</key>
    <string>${EXECUTABLE_NAME}</string>
    <key>CFBundleIconFile</key>
    <string></string>
    <key>CFBundleIdentifier</key>
    <string>com.yourcompany.ColorAssistant</string>
    <key>CFBundleName</key>
    <string>Color Assistant</string>
    <key>CFBundleDisplayName</key>
    <string>Color Assistant</string>
    <key>CFBundlePackageType</key>
    <string>BNDL</string>
    <key>CFBundleSignature</key>
    <string>????</string>
    <key>CFBundleShortVersionString</key>
    <string>1.0.0</string>
    <key>CFBundleVersion</key>
    <string>1.0.0</string>
    <key>NSHumanReadableCopyright</key>
    <string></string>
    <key>NSHighResolutionCapable</key>
    <true/>
    <key>AudioComponents</key>
    <array>
      <dict>
        <key>name</key>
        <string>EGURT: Color Assistant</string>
        <key>description</key>
        <string>Color Assistant</string>
        <key>factoryFunction</key>
        <string>ColorAssistantAUFactory</string>
        <key>manufacturer</key>
        <string>Manu</string>
        <key>type</key>
        <string>aufx</string>
        <key>subtype</key>
        <string>Ktaz</string>
        <key>version</key>
        <integer>65536</integer>
        <key>resourceUsage</key>
        <dict>
          <key>network.client</key>
          <true/>
          <key>temporary-exception.files.all.read-write</key>
          <true/>
        </dict>
      </dict>
    </array>
  </dict>
</plist>
