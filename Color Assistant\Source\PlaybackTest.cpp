/*
  ==============================================================================

    PlaybackTest.cpp
    Created: AI Chord Assistant Plugin - Stage 2
    Author:  AI Assistant

    This file contains test code for the new playback and MIDI functionality.

  ==============================================================================
*/

#include "AudioEngine.h"
#include "MusicData.h"
#include <iostream>

// 测试播放引擎功能
void testPlaybackEngine()
{
    std::cout << "=== 播放引擎测试 ===" << std::endl;
    
    // 创建播放引擎
    PlaybackEngine engine;
    engine.prepareToPlay(44100.0, 512);
    
    // 创建测试音乐段落
    MusicSection section;
    section.numberOfBars = 4;
    section.globalKey = KeySignature::CMajor;
    section.globalTimeSignature = TimeSignature(4, 4);
    section.bpm = 120.0f;
    
    // 添加测试和弦
    ChordData chord1("C", 0, ChordType::Major);
    chord1.barNumber = 1;
    chord1.startBeat = 0.0f;
    chord1.duration = 4.0f;
    
    ChordData chord2("Am", 9, ChordType::Minor);
    chord2.barNumber = 2;
    chord2.startBeat = 0.0f;
    chord2.duration = 4.0f;
    
    ChordData chord3("F", 5, ChordType::Major);
    chord3.barNumber = 3;
    chord3.startBeat = 0.0f;
    chord3.duration = 4.0f;
    
    ChordData chord4("G", 7, ChordType::Major);
    chord4.barNumber = 4;
    chord4.startBeat = 0.0f;
    chord4.duration = 4.0f;
    
    section.addChord(chord1);
    section.addChord(chord2);
    section.addChord(chord3);
    section.addChord(chord4);
    
    // 设置音乐段落
    engine.setMusicSection(&section);
    engine.setBPM(120.0f);
    
    std::cout << "创建了包含4个和弦的测试段落" << std::endl;
    std::cout << "BPM: " << engine.getBPM() << std::endl;
    std::cout << "播放状态: " << (engine.isPlaying() ? "播放中" : "停止") << std::endl;
    
    // 测试播放控制
    std::cout << "\n测试播放控制..." << std::endl;
    engine.startPlayback();
    std::cout << "开始播放 - 状态: " << (engine.isPlaying() ? "播放中" : "停止") << std::endl;
    
    engine.pausePlayback();
    std::cout << "暂停播放 - 状态: " << (engine.isPlaying() ? "播放中" : "暂停") << std::endl;
    std::cout << "暂停状态: " << (engine.isPaused() ? "是" : "否") << std::endl;
    
    engine.stopPlayback();
    std::cout << "停止播放 - 状态: " << (engine.isPlaying() ? "播放中" : "停止") << std::endl;
    
    // 测试循环播放
    engine.setLooping(true);
    std::cout << "循环播放: " << (engine.isLooping() ? "开启" : "关闭") << std::endl;
    
    // 测试音量控制
    engine.setVolume(0.5f);
    std::cout << "音量设置为: " << engine.getVolume() << std::endl;
    
    // 测试播放位置
    engine.setPlayPosition(8.0); // 设置到第3小节开始
    std::cout << "播放位置设置为: " << engine.getPlayPosition() << " 拍" << std::endl;
    
    engine.releaseResources();
    std::cout << "播放引擎测试完成" << std::endl;
}

// 测试MIDI导出功能
void testMidiExport()
{
    std::cout << "\n=== MIDI导出测试 ===" << std::endl;
    
    // 创建测试音乐段落
    MusicSection section;
    section.numberOfBars = 2;
    section.globalKey = KeySignature::CMajor;
    section.globalTimeSignature = TimeSignature(4, 4);
    section.bpm = 120.0f;
    
    // 添加测试和弦
    ChordData chord1("Cmaj7", 0, ChordType::Major7);
    chord1.barNumber = 1;
    chord1.startBeat = 0.0f;
    chord1.duration = 2.0f;
    
    ChordData chord2("Am7", 9, ChordType::Minor7);
    chord2.barNumber = 1;
    chord2.startBeat = 2.0f;
    chord2.duration = 2.0f;
    
    ChordData chord3("Dm7", 2, ChordType::Minor7);
    chord3.barNumber = 2;
    chord3.startBeat = 0.0f;
    chord3.duration = 2.0f;
    
    ChordData chord4("G7", 7, ChordType::Dominant7);
    chord4.barNumber = 2;
    chord4.startBeat = 2.0f;
    chord4.duration = 2.0f;
    
    section.addChord(chord1);
    section.addChord(chord2);
    section.addChord(chord3);
    section.addChord(chord4);
    
    std::cout << "创建了包含4个七和弦的测试段落" << std::endl;
    
    // 测试MIDI序列生成
    auto midiSequence = MidiExporter::generateMidiSequence(section);
    std::cout << "生成的MIDI序列包含 " << midiSequence.getNumEvents() << " 个事件" << std::endl;
    
    // 显示MIDI事件详情
    std::cout << "\nMIDI事件详情:" << std::endl;
    for (int i = 0; i < juce::jmin(10, midiSequence.getNumEvents()); ++i) // 只显示前10个事件
    {
        auto* event = midiSequence.getEventPointer(i);
        auto message = event->message;
        
        std::cout << "  事件 " << (i+1) << ": ";
        if (message.isNoteOn())
        {
            std::cout << "Note On - 音符:" << message.getNoteNumber() 
                      << ", 力度:" << message.getVelocity()
                      << ", 时间:" << event->getTimeStamp();
        }
        else if (message.isNoteOff())
        {
            std::cout << "Note Off - 音符:" << message.getNoteNumber()
                      << ", 时间:" << event->getTimeStamp();
        }
        else
        {
            std::cout << "其他事件";
        }
        std::cout << std::endl;
    }
    
    // 测试拖拽数据生成
    auto dragData = MidiExporter::generateDragData(section);
    std::cout << "\n生成的拖拽数据大小: " << dragData.getSize() << " 字节" << std::endl;
    
    // 测试实时MIDI生成
    auto realtimeMidi = MidiExporter::generateRealtimeMidi(section, 0.0, 4.0, 44100.0);
    std::cout << "实时MIDI缓冲区包含 " << realtimeMidi.getNumEvents() << " 个事件" << std::endl;
    
    std::cout << "MIDI导出测试完成" << std::endl;
}

// 测试音频合成器
void testSynthesiser()
{
    std::cout << "\n=== 音频合成器测试 ===" << std::endl;
    
    // 创建合成器
    juce::Synthesiser synth;
    
    // 添加声音
    for (int i = 0; i < 8; ++i)
    {
        synth.addVoice(new SineWaveVoice());
    }
    synth.addSound(new SineWaveSound());
    
    synth.setCurrentPlaybackSampleRate(44100.0);
    
    std::cout << "创建了包含8个声音的合成器" << std::endl;
    std::cout << "声音数量: " << synth.getNumVoices() << std::endl;
    std::cout << "音色数量: " << synth.getNumSounds() << std::endl;
    
    // 测试音符播放
    auto noteOnMessage = juce::MidiMessage::noteOn(1, 60, 0.8f); // C4
    auto noteOffMessage = juce::MidiMessage::noteOff(1, 60);
    
    std::cout << "测试音符: C4 (MIDI 60)" << std::endl;
    std::cout << "Note On消息: 通道=" << noteOnMessage.getChannel() 
              << ", 音符=" << noteOnMessage.getNoteNumber()
              << ", 力度=" << noteOnMessage.getVelocity() << std::endl;
    
    // 创建测试音频缓冲区
    juce::AudioBuffer<float> testBuffer(2, 1024); // 立体声，1024采样
    juce::MidiBuffer testMidiBuffer;
    
    testMidiBuffer.addEvent(noteOnMessage, 0);
    testMidiBuffer.addEvent(noteOffMessage, 512);
    
    // 渲染音频
    synth.renderNextBlock(testBuffer, testMidiBuffer, 0, 1024);
    
    // 检查音频输出
    bool hasAudio = false;
    for (int channel = 0; channel < testBuffer.getNumChannels(); ++channel)
    {
        auto* channelData = testBuffer.getReadPointer(channel);
        for (int sample = 0; sample < testBuffer.getNumSamples(); ++sample)
        {
            if (std::abs(channelData[sample]) > 0.001f)
            {
                hasAudio = true;
                break;
            }
        }
        if (hasAudio) break;
    }
    
    std::cout << "音频输出: " << (hasAudio ? "检测到音频信号" : "无音频信号") << std::endl;
    std::cout << "音频合成器测试完成" << std::endl;
}

/*
// 注意：这些测试函数不会被编译到插件中，
// 它们只是用来验证我们的播放引擎和MIDI功能是否正常工作。

int main()
{
    testPlaybackEngine();
    testMidiExport();
    testSynthesiser();
    return 0;
}
*/
