/*
  ==============================================================================

    This file contains the basic framework code for a JUCE plugin editor.

  ==============================================================================
*/

#pragma once

#include <JuceHeader.h>
#include "PluginProcessor.h"
#include "MusicSectionEditor.h"
#include "AIManager.h"
#include "AIConfigDialog.h"

//==============================================================================
/**
*/
class ColorAssistantAudioProcessorEditor  : public juce::AudioProcessorEditor
{
public:
    ColorAssistantAudioProcessorEditor (ColorAssistantAudioProcessor&);
    ~ColorAssistantAudioProcessorEditor() override;

    //==============================================================================
    void paint (juce::Graphics&) override;
    void resized() override;

private:
    // This reference is provided as a quick way for your editor to
    // access the processor object that created it.
    ColorAssistantAudioProcessor& audioProcessor;

    // UI Components
    MusicSectionEditor musicSectionEditor;
    PlaybackControls playbackControls;

    // AI Components
    std::unique_ptr<AIManager> aiManager;
    AIConfigManager aiConfigManager;
    EnhancedChatInterface enhancedChatInterface;
    AIConfigDialog aiConfigDialog;
    AIStatusIndicator aiStatusIndicator;
    AISuggestionPanel aiSuggestionPanel;

    // Layout
    void layoutComponents();
    void setupAIComponents();

    // Event handlers
    void handleSectionChanged();
    void handlePlayClicked();
    void handlePauseClicked();
    void handleStopClicked();
    void handleLoopChanged(bool loop);
    void handleBPMChanged(float bpm);
    void handleSyncChanged(bool sync);
    void handleVolumeChanged(float volume);

    // AI Event handlers
    void handleAIConfigRequested();
    void handleAIConfigChanged(const AIConfig& config);
    void handleChordsGenerated(const juce::Array<ChordData>& chords);
    void handleChordSelected(const ChordData& chord);
    void handleAllChordsAccepted(const juce::Array<ChordData>& chords);

    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR (ColorAssistantAudioProcessorEditor)
};
