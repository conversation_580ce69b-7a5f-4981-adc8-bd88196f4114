/*
  ==============================================================================

    This file contains the basic framework code for a JUCE plugin editor.

  ==============================================================================
*/

#pragma once

#include <JuceHeader.h>
#include "PluginProcessor.h"
#include "MusicSectionEditor.h"

//==============================================================================
/**
*/
class ColorAssistantAudioProcessorEditor  : public juce::AudioProcessorEditor
{
public:
    ColorAssistantAudioProcessorEditor (ColorAssistantAudioProcessor&);
    ~ColorAssistantAudioProcessorEditor() override;

    //==============================================================================
    void paint (juce::Graphics&) override;
    void resized() override;

private:
    // This reference is provided as a quick way for your editor to
    // access the processor object that created it.
    ColorAssistantAudioProcessor& audioProcessor;

    // UI Components
    MusicSectionEditor musicSectionEditor;
    PlaybackControls playbackControls;

    // Chat interface (placeholder for now)
    juce::TextEditor chatInput;
    juce::TextEditor chatOutput;
    juce::TextButton generateButton;
    juce::Label chatLabel;

    // Layout
    void layoutComponents();
    void setupChatInterface();

    // Event handlers
    void handleSectionChanged();
    void handlePlayClicked();
    void handleStopClicked();
    void handleBPMChanged(float bpm);
    void handleSyncChanged(bool sync);
    void handleVolumeChanged(float volume);
    void handleGenerateClicked();

    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR (ColorAssistantAudioProcessorEditor)
};
