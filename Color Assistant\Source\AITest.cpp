/*
  ==============================================================================

    AITest.cpp
    Created: AI Chord Assistant Plugin - Stage 3
    Author:  AI Assistant

    This file contains test code for the AI functionality.

  ==============================================================================
*/

#include "AIManager.h"
#include "AIConfigDialog.h"
#include <iostream>

// 测试Prompt模板管理器
void testPromptTemplateManager()
{
    std::cout << "=== Prompt模板管理器测试 ===" << std::endl;
    
    PromptTemplateManager promptManager;
    
    // 创建测试音乐段落
    MusicSection section;
    section.numberOfBars = 4;
    section.globalKey = KeySignature::CMajor;
    section.globalTimeSignature = TimeSignature(4, 4);
    section.bpm = 120.0f;
    
    // 添加一些和弦
    ChordData chord1("C", 0, ChordType::Major);
    chord1.barNumber = 1;
    chord1.startBeat = 0.0f;
    chord1.duration = 4.0f;
    
    ChordData chord2("Am", 9, ChordType::Minor);
    chord2.barNumber = 2;
    chord2.startBeat = 0.0f;
    chord2.duration = 4.0f;
    
    section.addChord(chord1);
    section.addChord(chord2);
    
    // 测试系统Prompt
    auto systemPrompt = promptManager.generateSystemPrompt();
    std::cout << "系统Prompt长度: " << systemPrompt.length() << " 字符" << std::endl;
    
    // 测试和弦生成Prompt
    auto chordPrompt = promptManager.generateChordGenerationPrompt(section, "请生成一个流行音乐风格的和弦进行");
    std::cout << "和弦生成Prompt长度: " << chordPrompt.length() << " 字符" << std::endl;
    
    // 测试分析Prompt
    auto analysisPrompt = promptManager.generateAnalysisPrompt(section);
    std::cout << "分析Prompt长度: " << analysisPrompt.length() << " 字符" << std::endl;
    
    // 测试建议Prompt
    auto suggestionPrompt = promptManager.generateSuggestionPrompt(section, 3, 0.0f);
    std::cout << "建议Prompt长度: " << suggestionPrompt.length() << " 字符" << std::endl;
    
    // 测试音乐上下文序列化
    auto context = promptManager.serializeMusicContext(section);
    std::cout << "\n音乐上下文序列化:" << std::endl;
    std::cout << context.toStdString() << std::endl;
    
    std::cout << "Prompt模板管理器测试完成\n" << std::endl;
}

// 测试AI响应解析器
void testAIResponseParser()
{
    std::cout << "=== AI响应解析器测试 ===" << std::endl;
    
    // 测试JSON响应解析
    juce::String jsonResponse = R"({
        "explanation": "这是一个经典的流行音乐和弦进行",
        "chords": [
            {"name": "C", "bar": 1, "beat": 0, "duration": 4},
            {"name": "Am", "bar": 2, "beat": 0, "duration": 4},
            {"name": "F", "bar": 3, "beat": 0, "duration": 4},
            {"name": "G", "bar": 4, "beat": 0, "duration": 4}
        ],
        "reasoning": "这个进行使用了I-vi-IV-V的经典模式",
        "confidence": 0.9
    })";
    
    auto response = AIResponseParser::parseJSONResponse(jsonResponse);
    
    std::cout << "JSON解析结果:" << std::endl;
    std::cout << "成功: " << (response.success ? "是" : "否") << std::endl;
    std::cout << "解释: " << response.explanation.toStdString() << std::endl;
    std::cout << "推理: " << response.reasoning.toStdString() << std::endl;
    std::cout << "置信度: " << response.confidence << std::endl;
    std::cout << "和弦数量: " << response.suggestedChords.size() << std::endl;
    
    for (int i = 0; i < response.suggestedChords.size(); ++i)
    {
        const auto& chord = response.suggestedChords.getReference(i);
        std::cout << "  和弦 " << (i+1) << ": " << chord.toString().toStdString() << std::endl;
    }
    
    // 测试文本响应解析
    juce::String textResponse = "建议使用 C Am F G 这个经典的和弦进行。这是一个非常流行的进行，适合大多数流行歌曲。";
    auto textParsed = AIResponseParser::parseTextResponse(textResponse);
    
    std::cout << "\n文本解析结果:" << std::endl;
    std::cout << "成功: " << (textParsed.success ? "是" : "否") << std::endl;
    std::cout << "解释: " << textParsed.explanation.toStdString() << std::endl;
    std::cout << "提取的和弦数量: " << textParsed.suggestedChords.size() << std::endl;
    
    // 测试响应验证
    bool isValid = AIResponseParser::validateResponse(response);
    std::cout << "响应验证: " << (isValid ? "通过" : "失败") << std::endl;
    
    std::cout << "AI响应解析器测试完成\n" << std::endl;
}

// 测试AI配置管理器
void testAIConfigManager()
{
    std::cout << "=== AI配置管理器测试 ===" << std::endl;
    
    // 测试预设配置
    auto openaiConfig = AIConfigManager::getOpenAIConfig("test-api-key");
    std::cout << "OpenAI配置:" << std::endl;
    std::cout << "  提供商: " << (int)openaiConfig.provider << std::endl;
    std::cout << "  模型: " << openaiConfig.modelName.toStdString() << std::endl;
    std::cout << "  API地址: " << openaiConfig.baseUrl.toStdString() << std::endl;
    
    auto groqConfig = AIConfigManager::getGroqConfig("test-api-key");
    std::cout << "\nGroq配置:" << std::endl;
    std::cout << "  提供商: " << (int)groqConfig.provider << std::endl;
    std::cout << "  模型: " << groqConfig.modelName.toStdString() << std::endl;
    std::cout << "  API地址: " << groqConfig.baseUrl.toStdString() << std::endl;
    
    auto claudeConfig = AIConfigManager::getClaudeConfig("test-api-key");
    std::cout << "\nClaude配置:" << std::endl;
    std::cout << "  提供商: " << (int)claudeConfig.provider << std::endl;
    std::cout << "  模型: " << claudeConfig.modelName.toStdString() << std::endl;
    std::cout << "  API地址: " << claudeConfig.baseUrl.toStdString() << std::endl;
    
    // 测试配置验证
    bool openaiValid = AIConfigManager::validateConfig(openaiConfig);
    bool groqValid = AIConfigManager::validateConfig(groqConfig);
    bool claudeValid = AIConfigManager::validateConfig(claudeConfig);
    
    std::cout << "\n配置验证结果:" << std::endl;
    std::cout << "  OpenAI: " << (openaiValid ? "有效" : "无效") << std::endl;
    std::cout << "  Groq: " << (groqValid ? "有效" : "无效") << std::endl;
    std::cout << "  Claude: " << (claudeValid ? "有效" : "无效") << std::endl;
    
    // 测试配置保存和加载
    AIConfigManager configManager;
    
    // 修改配置
    groqConfig.temperature = 0.8f;
    groqConfig.maxTokens = 1500;
    
    // 保存配置
    configManager.saveConfig(groqConfig);
    std::cout << "\n配置已保存" << std::endl;
    
    // 加载配置
    auto loadedConfig = configManager.loadConfig();
    std::cout << "加载的配置:" << std::endl;
    std::cout << "  温度: " << loadedConfig.temperature << std::endl;
    std::cout << "  最大Token: " << loadedConfig.maxTokens << std::endl;
    std::cout << "  模型: " << loadedConfig.modelName.toStdString() << std::endl;
    
    std::cout << "AI配置管理器测试完成\n" << std::endl;
}

// 测试AI管理器基础功能
void testAIManagerBasics()
{
    std::cout << "=== AI管理器基础功能测试 ===" << std::endl;
    
    // 创建AI管理器
    AIManager aiManager;
    
    // 设置测试配置
    auto config = AIConfigManager::getGroqConfig("test-key");
    aiManager.setConfig(config);
    
    std::cout << "AI管理器已创建并配置" << std::endl;
    std::cout << "当前状态: " << (aiManager.isBusy() ? "忙碌" : "空闲") << std::endl;
    
    // 创建测试音乐段落
    MusicSection section;
    section.numberOfBars = 4;
    section.globalKey = KeySignature::CMajor;
    section.bpm = 120.0f;
    
    ChordData chord("C", 0, ChordType::Major);
    chord.barNumber = 1;
    section.addChord(chord);
    
    std::cout << "测试音乐段落已创建" << std::endl;
    
    // 注意：实际的API调用需要有效的API密钥，这里只测试请求构建
    std::cout << "AI管理器基础功能测试完成\n" << std::endl;
}

// 测试UI组件基础功能
void testUIComponents()
{
    std::cout << "=== UI组件基础功能测试 ===" << std::endl;
    
    // 测试AI配置对话框
    AIConfigDialog configDialog;
    std::cout << "AI配置对话框已创建" << std::endl;
    
    // 测试增强Chat界面
    EnhancedChatInterface chatInterface;
    std::cout << "增强Chat界面已创建" << std::endl;
    
    // 测试AI状态指示器
    AIStatusIndicator statusIndicator;
    statusIndicator.setStatus("测试状态", juce::Colours::blue);
    std::cout << "AI状态指示器已创建并设置状态" << std::endl;
    
    // 测试AI建议面板
    AISuggestionPanel suggestionPanel;
    std::cout << "AI建议面板已创建" << std::endl;
    
    // 创建测试和弦数据
    juce::Array<ChordData> testChords;
    testChords.add(ChordData("C", 0, ChordType::Major));
    testChords.add(ChordData("Am", 9, ChordType::Minor));
    testChords.add(ChordData("F", 5, ChordType::Major));
    testChords.add(ChordData("G", 7, ChordType::Major));
    
    suggestionPanel.showSuggestions(testChords, "这是一个测试建议");
    std::cout << "AI建议面板已显示测试建议" << std::endl;
    
    std::cout << "UI组件基础功能测试完成\n" << std::endl;
}

/*
// 注意：这些测试函数不会被编译到插件中，
// 它们只是用来验证我们的AI功能是否正常工作。

int main()
{
    testPromptTemplateManager();
    testAIResponseParser();
    testAIConfigManager();
    testAIManagerBasics();
    testUIComponents();
    
    std::cout << "=== 所有AI功能测试完成 ===" << std::endl;
    return 0;
}
*/
