/*
  ==============================================================================

    AIManager.cpp
    Created: AI Chord Assistant Plugin - Stage 3
    Author:  AI Assistant

    Implementation of the AI communication manager.

  ==============================================================================
*/

#include "AIManager.h"

//==============================================================================
// PromptTemplateManager Implementation
//==============================================================================

PromptTemplateManager::PromptTemplateManager()
{
    initializeTemplates();
}

juce::String PromptTemplateManager::generateSystemPrompt() const
{
    return systemPromptTemplate;
}

juce::String PromptTemplateManager::generateChordGenerationPrompt(const MusicSection& section, 
                                                                 const juce::String& userRequest) const
{
    juce::String prompt = chordGenerationTemplate;
    
    // 替换模板变量
    prompt = prompt.replace("{{MUSIC_CONTEXT}}", serializeMusicContext(section));
    prompt = prompt.replace("{{USER_REQUEST}}", userRequest);
    prompt = prompt.replace("{{OUTPUT_FORMAT}}", getOutputFormatInstructions());
    
    return prompt;
}

juce::String PromptTemplateManager::generateAnalysisPrompt(const MusicSection& section) const
{
    juce::String prompt = analysisTemplate;
    prompt = prompt.replace("{{MUSIC_CONTEXT}}", serializeMusicContext(section));
    return prompt;
}

juce::String PromptTemplateManager::generateSuggestionPrompt(const MusicSection& section, 
                                                           int targetBar, float targetBeat) const
{
    juce::String prompt = suggestionTemplate;
    prompt = prompt.replace("{{MUSIC_CONTEXT}}", serializeMusicContext(section));
    prompt = prompt.replace("{{TARGET_POSITION}}", "第" + juce::String(targetBar) + "小节第" + juce::String(targetBeat, 1) + "拍");
    return prompt;
}

juce::String PromptTemplateManager::generateTheoryPrompt(const juce::String& question) const
{
    juce::String prompt = theoryTemplate;
    prompt = prompt.replace("{{QUESTION}}", question);
    return prompt;
}

juce::String PromptTemplateManager::serializeMusicContext(const MusicSection& section) const
{
    juce::String context;
    
    context += "调式: " + formatKeySignature(section.globalKey) + "\n";
    context += "拍号: " + formatTimeSignature(section.globalTimeSignature) + "\n";
    context += "BPM: " + juce::String(section.bpm, 1) + "\n";
    context += "小节数: " + juce::String(section.numberOfBars) + "\n\n";
    
    if (!section.chords.isEmpty())
    {
        context += "当前和弦进行:\n";
        context += serializeChordProgression(section.chords);
    }
    else
    {
        context += "当前没有和弦\n";
    }
    
    return context;
}

juce::String PromptTemplateManager::serializeChordProgression(const juce::Array<ChordData>& chords) const
{
    juce::String progression;
    
    for (const auto& chord : chords)
    {
        if (chord.isEmpty()) continue;
        
        progression += "小节" + juce::String(chord.barNumber) + 
                      ", 拍" + juce::String(chord.startBeat, 1) + 
                      ": " + chord.chordName + 
                      " (时值: " + juce::String(chord.duration, 1) + "拍)\n";
    }
    
    return progression;
}

juce::String PromptTemplateManager::getOutputFormatInstructions() const
{
    return R"(
请按以下JSON格式回复:
{
  "explanation": "你的解释和建议",
  "chords": [
    {
      "name": "和弦名称",
      "bar": 小节号,
      "beat": 拍位置,
      "duration": 时值
    }
  ],
  "reasoning": "选择这些和弦的原因",
  "confidence": 置信度(0.0-1.0)
}
)";
}

void PromptTemplateManager::initializeTemplates()
{
    systemPromptTemplate = R"(
你是一个专业的音乐理论专家和作曲助手。你精通和声学、作曲技巧和各种音乐风格。
你的任务是帮助用户创作和弦进行，分析音乐结构，并提供专业的音乐理论指导。

请始终：
1. 提供准确的音乐理论解释
2. 考虑和声功能和进行的逻辑性
3. 根据用户的音乐风格需求调整建议
4. 解释你的选择理由
5. 使用中文回复

你可以处理各种调式、拍号和音乐风格。
)";

    chordGenerationTemplate = R"(
基于以下音乐上下文和用户需求，请生成合适的和弦进行：

{{MUSIC_CONTEXT}}

用户需求：{{USER_REQUEST}}

请分析当前的音乐上下文，考虑和声功能、调式特点和风格要求，生成合适的和弦进行。

{{OUTPUT_FORMAT}}
)";

    analysisTemplate = R"(
请分析以下和弦进行：

{{MUSIC_CONTEXT}}

请从以下角度进行分析：
1. 和声功能分析
2. 调式特点
3. 进行的逻辑性
4. 可能的改进建议
5. 风格特征

请用中文详细解释。
)";

    suggestionTemplate = R"(
基于当前的和弦进行，请为指定位置建议合适的和弦：

{{MUSIC_CONTEXT}}

目标位置：{{TARGET_POSITION}}

请考虑：
1. 与前后和弦的连接
2. 和声功能的逻辑性
3. 调式内的和弦选择
4. 常见的进行模式

请提供2-3个选项并解释原因。
)";

    theoryTemplate = R"(
请回答以下音乐理论问题：

{{QUESTION}}

请提供详细、准确的解释，包括相关的理论知识和实际应用。
)";
}

juce::String PromptTemplateManager::formatKeySignature(KeySignature key) const
{
    switch (key)
    {
        case KeySignature::CMajor: return "C大调";
        case KeySignature::GMajor: return "G大调";
        case KeySignature::DMajor: return "D大调";
        case KeySignature::AMajor: return "A大调";
        case KeySignature::EMajor: return "E大调";
        case KeySignature::BMajor: return "B大调";
        case KeySignature::FSharpMajor: return "F#大调";
        case KeySignature::CSharpMajor: return "C#大调";
        case KeySignature::FMajor: return "F大调";
        case KeySignature::BFlatMajor: return "Bb大调";
        case KeySignature::EFlatMajor: return "Eb大调";
        case KeySignature::AFlatMajor: return "Ab大调";
        case KeySignature::DFlatMajor: return "Db大调";
        case KeySignature::GFlatMajor: return "Gb大调";
        case KeySignature::CFlatMajor: return "Cb大调";
        case KeySignature::AMinor: return "a小调";
        case KeySignature::EMinor: return "e小调";
        case KeySignature::BMinor: return "b小调";
        case KeySignature::FSharpMinor: return "f#小调";
        case KeySignature::CSharpMinor: return "c#小调";
        case KeySignature::GSharpMinor: return "g#小调";
        case KeySignature::DSharpMinor: return "d#小调";
        case KeySignature::ASharpMinor: return "a#小调";
        case KeySignature::DMinor: return "d小调";
        case KeySignature::GMinor: return "g小调";
        case KeySignature::CMinor: return "c小调";
        case KeySignature::FMinor: return "f小调";
        case KeySignature::BFlatMinor: return "bb小调";
        case KeySignature::EFlatMinor: return "eb小调";
        case KeySignature::AFlatMinor: return "ab小调";
        default: return "C大调";
    }
}

juce::String PromptTemplateManager::formatTimeSignature(const TimeSignature& timeSig) const
{
    return timeSig.toString();
}

//==============================================================================
// HTTPClientManager Implementation
//==============================================================================

HTTPClientManager::HTTPClientManager()
    : timeoutSeconds(30)
{
}

HTTPClientManager::~HTTPClientManager()
{
    cancelAllRequests();
}

void HTTPClientManager::sendRequest(const juce::String& url,
                                   const juce::String& headers,
                                   const juce::String& postData,
                                   std::function<void(bool success, const juce::String& response)> callback)
{
    // 取消之前的请求
    cancelAllRequests();
    
    // 创建新的异步请求
    juce::Thread::launch([this, url, headers, postData, callback]()
    {
        try
        {
            juce::URL requestUrl(url);
            
            // 设置请求头
            juce::StringPairArray headerPairs;
            auto headerLines = juce::StringArray::fromLines(headers);
            for (const auto& line : headerLines)
            {
                auto colonPos = line.indexOfChar(':');
                if (colonPos > 0)
                {
                    auto key = line.substring(0, colonPos).trim();
                    auto value = line.substring(colonPos + 1).trim();
                    headerPairs.set(key, value);
                }
            }
            
            // 发送POST请求
            auto stream = requestUrl.createInputStream(
                juce::URL::InputStreamOptions(juce::URL::ParameterHandling::inAddress)
                    .withExtraHeaders(headerPairs)
                    .withConnectionTimeoutMs(timeoutSeconds * 1000)
                    .withPostData(postData)
            );
            
            if (stream != nullptr)
            {
                juce::String response = stream->readEntireStreamAsString();
                
                // 在主线程中调用回调
                juce::MessageManager::callAsync([callback, response]()
                {
                    callback(true, response);
                });
            }
            else
            {
                juce::MessageManager::callAsync([callback]()
                {
                    callback(false, "Failed to create input stream");
                });
            }
        }
        catch (const std::exception& e)
        {
            juce::MessageManager::callAsync([callback, e]()
            {
                callback(false, "Exception: " + juce::String(e.what()));
            });
        }
    });
}

void HTTPClientManager::cancelAllRequests()
{
    if (currentStream != nullptr)
    {
        currentStream.reset();
    }
}

//==============================================================================
// AIResponseParser Implementation
//==============================================================================

AIResponseParser::AIResponseParser()
{
}

AIResponse AIResponseParser::parseJSONResponse(const juce::String& jsonResponse)
{
    AIResponse response;

    try
    {
        auto json = juce::JSON::parse(jsonResponse);
        if (json.isObject())
        {
            auto obj = json.getDynamicObject();

            response.success = true;
            response.explanation = obj->getProperty("explanation").toString();
            response.reasoning = obj->getProperty("reasoning").toString();
            response.confidence = (float)obj->getProperty("confidence");

            // 解析和弦数组
            auto chordsArray = obj->getProperty("chords");
            if (chordsArray.isArray())
            {
                auto* array = chordsArray.getArray();
                for (int i = 0; i < array->size(); ++i)
                {
                    auto chordObj = (*array)[i].getDynamicObject();
                    if (chordObj != nullptr)
                    {
                        ChordData chord;
                        chord.chordName = chordObj->getProperty("name").toString();
                        chord.barNumber = (int)chordObj->getProperty("bar");
                        chord.startBeat = (float)chordObj->getProperty("beat");
                        chord.duration = (float)chordObj->getProperty("duration");

                        // 解析和弦类型和根音
                        chord = ChordData::fromString(chord.chordName);
                        chord.barNumber = (int)chordObj->getProperty("bar");
                        chord.startBeat = (float)chordObj->getProperty("beat");
                        chord.duration = (float)chordObj->getProperty("duration");

                        response.suggestedChords.add(chord);
                    }
                }
            }
        }
        else
        {
            response.success = false;
            response.errorMessage = "Invalid JSON format";
        }
    }
    catch (...)
    {
        response.success = false;
        response.errorMessage = "JSON parsing error";

        // 尝试作为纯文本解析
        response = parseTextResponse(jsonResponse);
    }

    return response;
}

AIResponse AIResponseParser::parseTextResponse(const juce::String& textResponse)
{
    AIResponse response;
    response.success = true;
    response.explanation = textResponse;
    response.confidence = 0.5f; // 默认置信度

    // 尝试从文本中提取和弦
    response.suggestedChords = extractChordsFromText(textResponse);

    return response;
}

juce::Array<ChordData> AIResponseParser::extractChordsFromText(const juce::String& text)
{
    juce::Array<ChordData> chords;

    // 简单的和弦提取逻辑 - 查找常见的和弦模式
    juce::StringArray lines = juce::StringArray::fromLines(text);

    for (const auto& line : lines)
    {
        // 查找包含和弦名称的行
        if (line.containsIgnoreCase("和弦") || line.containsIgnoreCase("chord"))
        {
            // 使用正则表达式或简单字符串匹配提取和弦
            // 这里使用简化的实现
            auto words = juce::StringArray::fromTokens(line, " \t,，", "");

            for (const auto& word : words)
            {
                if (isValidChordName(word))
                {
                    auto chord = parseChordString(word, 1, 0.0f);
                    if (!chord.isEmpty())
                    {
                        chords.add(chord);
                    }
                }
            }
        }
    }

    return chords;
}

ChordData AIResponseParser::parseChordString(const juce::String& chordStr, int bar, float beat)
{
    auto cleanName = cleanChordName(chordStr);
    auto chord = ChordData::fromString(cleanName);
    chord.barNumber = bar;
    chord.startBeat = beat;
    chord.duration = 1.0f; // 默认时值

    return chord;
}

bool AIResponseParser::validateResponse(const AIResponse& response)
{
    if (!response.success)
        return false;

    // 检查基本字段
    if (response.explanation.isEmpty())
        return false;

    // 检查和弦数据的有效性
    for (const auto& chord : response.suggestedChords)
    {
        if (chord.isEmpty() || chord.barNumber <= 0 || chord.duration <= 0)
            return false;
    }

    return true;
}

juce::String AIResponseParser::cleanChordName(const juce::String& name)
{
    return name.trim().removeCharacters("()[]{}\"'");
}

bool AIResponseParser::isValidChordName(const juce::String& name)
{
    auto clean = cleanChordName(name);

    // 检查是否以音符名开头
    if (clean.isEmpty())
        return false;

    auto firstChar = clean[0];
    return (firstChar >= 'A' && firstChar <= 'G') ||
           (firstChar >= 'a' && firstChar <= 'g');
}

//==============================================================================
// AIManager Implementation
//==============================================================================

AIManager::AIManager()
    : juce::Thread("AIManager"), shouldStop(false)
{
    // 设置默认配置
    currentConfig = AIConfigManager::getGroqConfig("");
}

AIManager::~AIManager()
{
    shouldStop = true;
    stopThread(2000);
}

void AIManager::setConfig(const AIConfig& config)
{
    currentConfig = config;
}

void AIManager::generateChords(const MusicSection& section,
                              const juce::String& userRequest,
                              std::function<void(const AIResponse&)> callback)
{
    auto prompt = promptManager.generateChordGenerationPrompt(section, userRequest);

    AIRequest request;
    request.type = AIRequestType::GenerateChords;
    request.prompt = prompt;
    request.callback = callback;
    request.timestamp = juce::Time::getCurrentTime();

    juce::ScopedLock lock(queueLock);
    requestQueue.add(request);

    if (!isThreadRunning())
    {
        startThread();
    }
}

void AIManager::analyzeProgression(const MusicSection& section,
                                  std::function<void(const AIResponse&)> callback)
{
    auto prompt = promptManager.generateAnalysisPrompt(section);

    AIRequest request;
    request.type = AIRequestType::AnalyzeProgression;
    request.prompt = prompt;
    request.callback = callback;
    request.timestamp = juce::Time::getCurrentTime();

    juce::ScopedLock lock(queueLock);
    requestQueue.add(request);

    if (!isThreadRunning())
    {
        startThread();
    }
}

void AIManager::suggestNextChord(const MusicSection& section,
                                int targetBar, float targetBeat,
                                std::function<void(const AIResponse&)> callback)
{
    auto prompt = promptManager.generateSuggestionPrompt(section, targetBar, targetBeat);

    AIRequest request;
    request.type = AIRequestType::SuggestNext;
    request.prompt = prompt;
    request.callback = callback;
    request.timestamp = juce::Time::getCurrentTime();

    juce::ScopedLock lock(queueLock);
    requestQueue.add(request);

    if (!isThreadRunning())
    {
        startThread();
    }
}

void AIManager::explainTheory(const juce::String& question,
                             std::function<void(const AIResponse&)> callback)
{
    auto prompt = promptManager.generateTheoryPrompt(question);

    AIRequest request;
    request.type = AIRequestType::ExplainTheory;
    request.prompt = prompt;
    request.callback = callback;
    request.timestamp = juce::Time::getCurrentTime();

    juce::ScopedLock lock(queueLock);
    requestQueue.add(request);

    if (!isThreadRunning())
    {
        startThread();
    }
}

void AIManager::chatRequest(const juce::String& message,
                           const MusicSection* context,
                           std::function<void(const AIResponse&)> callback)
{
    juce::String prompt = promptManager.generateSystemPrompt() + "\n\n";

    if (context != nullptr)
    {
        prompt += "当前音乐上下文:\n";
        prompt += promptManager.serializeMusicContext(*context) + "\n\n";
    }

    prompt += "用户消息: " + message;

    AIRequest request;
    request.type = AIRequestType::Chat;
    request.prompt = prompt;
    request.callback = callback;
    request.timestamp = juce::Time::getCurrentTime();

    juce::ScopedLock lock(queueLock);
    requestQueue.add(request);

    if (!isThreadRunning())
    {
        startThread();
    }
}

void AIManager::cancelCurrentRequest()
{
    shouldStop = true;
    httpManager.cancelAllRequests();

    // 清空请求队列
    juce::ScopedLock lock(queueLock);
    requestQueue.clear();
}

void AIManager::run()
{
    while (!threadShouldExit() && !shouldStop)
    {
        AIRequest currentRequest;
        bool hasRequest = false;

        {
            juce::ScopedLock lock(queueLock);
            if (!requestQueue.isEmpty())
            {
                currentRequest = requestQueue.removeAndReturn(0);
                hasRequest = true;
            }
        }

        if (hasRequest)
        {
            processRequest(currentRequest);
        }
        else
        {
            wait(100); // 等待新请求
        }
    }
}

void AIManager::processRequest(const AIRequest& request)
{
    try
    {
        switch (currentConfig.provider)
        {
            case AIProvider::OpenAI:
                sendOpenAIRequest(request.prompt, request.callback);
                break;
            case AIProvider::Groq:
                sendGroqRequest(request.prompt, request.callback);
                break;
            case AIProvider::Claude:
                sendClaudeRequest(request.prompt, request.callback);
                break;
            default:
                // 默认使用Groq
                sendGroqRequest(request.prompt, request.callback);
                break;
        }
    }
    catch (const std::exception& e)
    {
        AIResponse errorResponse;
        errorResponse.success = false;
        errorResponse.errorMessage = "Request processing error: " + juce::String(e.what());

        juce::MessageManager::callAsync([callback = request.callback, errorResponse]()
        {
            callback(errorResponse);
        });
    }
}

juce::String AIManager::buildAPIRequest(const juce::String& prompt) const
{
    juce::DynamicObject requestObj;

    switch (currentConfig.provider)
    {
        case AIProvider::OpenAI:
        {
            juce::Array<juce::var> messages;

            juce::DynamicObject systemMessage;
            systemMessage.setProperty("role", "system");
            systemMessage.setProperty("content", promptManager.generateSystemPrompt());
            messages.add(juce::var(systemMessage.clone()));

            juce::DynamicObject userMessage;
            userMessage.setProperty("role", "user");
            userMessage.setProperty("content", prompt);
            messages.add(juce::var(userMessage.clone()));

            requestObj.setProperty("model", currentConfig.modelName.isEmpty() ? "gpt-3.5-turbo" : currentConfig.modelName);
            requestObj.setProperty("messages", messages);
            requestObj.setProperty("temperature", currentConfig.temperature);
            requestObj.setProperty("max_tokens", currentConfig.maxTokens);
            break;
        }
        case AIProvider::Groq:
        {
            juce::Array<juce::var> messages;

            juce::DynamicObject systemMessage;
            systemMessage.setProperty("role", "system");
            systemMessage.setProperty("content", promptManager.generateSystemPrompt());
            messages.add(juce::var(systemMessage.clone()));

            juce::DynamicObject userMessage;
            userMessage.setProperty("role", "user");
            userMessage.setProperty("content", prompt);
            messages.add(juce::var(userMessage.clone()));

            requestObj.setProperty("model", currentConfig.modelName.isEmpty() ? "llama3-8b-8192" : currentConfig.modelName);
            requestObj.setProperty("messages", messages);
            requestObj.setProperty("temperature", currentConfig.temperature);
            requestObj.setProperty("max_tokens", currentConfig.maxTokens);
            break;
        }
        case AIProvider::Claude:
        {
            requestObj.setProperty("model", currentConfig.modelName.isEmpty() ? "claude-3-sonnet-20240229" : currentConfig.modelName);
            requestObj.setProperty("max_tokens", currentConfig.maxTokens);
            requestObj.setProperty("system", promptManager.generateSystemPrompt());

            juce::Array<juce::var> messages;
            juce::DynamicObject userMessage;
            userMessage.setProperty("role", "user");
            userMessage.setProperty("content", prompt);
            messages.add(juce::var(userMessage.clone()));

            requestObj.setProperty("messages", messages);
            break;
        }
    }

    return juce::JSON::toString(juce::var(requestObj.clone()));
}

juce::String AIManager::getAPIEndpoint() const
{
    switch (currentConfig.provider)
    {
        case AIProvider::OpenAI:
            return currentConfig.baseUrl.isEmpty() ? "https://api.openai.com/v1/chat/completions" : currentConfig.baseUrl;
        case AIProvider::Groq:
            return currentConfig.baseUrl.isEmpty() ? "https://api.groq.com/openai/v1/chat/completions" : currentConfig.baseUrl;
        case AIProvider::Claude:
            return currentConfig.baseUrl.isEmpty() ? "https://api.anthropic.com/v1/messages" : currentConfig.baseUrl;
        default:
            return "https://api.groq.com/openai/v1/chat/completions";
    }
}

juce::String AIManager::getAuthHeaders() const
{
    juce::String headers = "Content-Type: application/json\n";

    switch (currentConfig.provider)
    {
        case AIProvider::OpenAI:
        case AIProvider::Groq:
            headers += "Authorization: Bearer " + currentConfig.apiKey + "\n";
            break;
        case AIProvider::Claude:
            headers += "x-api-key: " + currentConfig.apiKey + "\n";
            headers += "anthropic-version: 2023-06-01\n";
            break;
    }

    return headers;
}

void AIManager::sendOpenAIRequest(const juce::String& prompt, std::function<void(const AIResponse&)> callback)
{
    auto requestBody = buildAPIRequest(prompt);
    auto endpoint = getAPIEndpoint();
    auto headers = getAuthHeaders();

    httpManager.sendRequest(endpoint, headers, requestBody,
        [callback](bool success, const juce::String& response)
        {
            AIResponse aiResponse;

            if (success)
            {
                try
                {
                    auto json = juce::JSON::parse(response);
                    if (json.isObject())
                    {
                        auto obj = json.getDynamicObject();
                        auto choices = obj->getProperty("choices");

                        if (choices.isArray() && choices.getArray()->size() > 0)
                        {
                            auto firstChoice = (*choices.getArray())[0].getDynamicObject();
                            auto message = firstChoice->getProperty("message").getDynamicObject();
                            auto content = message->getProperty("content").toString();

                            aiResponse = AIResponseParser::parseJSONResponse(content);
                            if (!aiResponse.success)
                            {
                                aiResponse = AIResponseParser::parseTextResponse(content);
                            }
                        }
                        else
                        {
                            aiResponse.success = false;
                            aiResponse.errorMessage = "No choices in response";
                        }
                    }
                    else
                    {
                        aiResponse.success = false;
                        aiResponse.errorMessage = "Invalid response format";
                    }
                }
                catch (...)
                {
                    aiResponse.success = false;
                    aiResponse.errorMessage = "Response parsing error";
                }
            }
            else
            {
                aiResponse.success = false;
                aiResponse.errorMessage = "HTTP request failed: " + response;
            }

            callback(aiResponse);
        });
}

void AIManager::sendGroqRequest(const juce::String& prompt, std::function<void(const AIResponse&)> callback)
{
    // Groq使用与OpenAI兼容的API格式
    sendOpenAIRequest(prompt, callback);
}

void AIManager::sendClaudeRequest(const juce::String& prompt, std::function<void(const AIResponse&)> callback)
{
    auto requestBody = buildAPIRequest(prompt);
    auto endpoint = getAPIEndpoint();
    auto headers = getAuthHeaders();

    httpManager.sendRequest(endpoint, headers, requestBody,
        [callback](bool success, const juce::String& response)
        {
            AIResponse aiResponse;

            if (success)
            {
                try
                {
                    auto json = juce::JSON::parse(response);
                    if (json.isObject())
                    {
                        auto obj = json.getDynamicObject();
                        auto content = obj->getProperty("content");

                        if (content.isArray() && content.getArray()->size() > 0)
                        {
                            auto firstContent = (*content.getArray())[0].getDynamicObject();
                            auto text = firstContent->getProperty("text").toString();

                            aiResponse = AIResponseParser::parseJSONResponse(text);
                            if (!aiResponse.success)
                            {
                                aiResponse = AIResponseParser::parseTextResponse(text);
                            }
                        }
                        else
                        {
                            aiResponse.success = false;
                            aiResponse.errorMessage = "No content in response";
                        }
                    }
                    else
                    {
                        aiResponse.success = false;
                        aiResponse.errorMessage = "Invalid response format";
                    }
                }
                catch (...)
                {
                    aiResponse.success = false;
                    aiResponse.errorMessage = "Response parsing error";
                }
            }
            else
            {
                aiResponse.success = false;
                aiResponse.errorMessage = "HTTP request failed: " + response;
            }

            callback(aiResponse);
        });
}

//==============================================================================
// AIConfigManager Implementation
//==============================================================================

AIConfigManager::AIConfigManager()
{
}

AIConfig AIConfigManager::getOpenAIConfig(const juce::String& apiKey)
{
    AIConfig config;
    config.provider = AIProvider::OpenAI;
    config.apiKey = apiKey;
    config.baseUrl = "https://api.openai.com/v1/chat/completions";
    config.modelName = "gpt-3.5-turbo";
    config.temperature = 0.7f;
    config.maxTokens = 1000;
    config.timeoutSeconds = 30;

    return config;
}

AIConfig AIConfigManager::getGroqConfig(const juce::String& apiKey)
{
    AIConfig config;
    config.provider = AIProvider::Groq;
    config.apiKey = apiKey;
    config.baseUrl = "https://api.groq.com/openai/v1/chat/completions";
    config.modelName = "llama3-8b-8192";
    config.temperature = 0.7f;
    config.maxTokens = 1000;
    config.timeoutSeconds = 30;

    return config;
}

AIConfig AIConfigManager::getClaudeConfig(const juce::String& apiKey)
{
    AIConfig config;
    config.provider = AIProvider::Claude;
    config.apiKey = apiKey;
    config.baseUrl = "https://api.anthropic.com/v1/messages";
    config.modelName = "claude-3-sonnet-20240229";
    config.temperature = 0.7f;
    config.maxTokens = 1000;
    config.timeoutSeconds = 30;

    return config;
}

bool AIConfigManager::validateConfig(const AIConfig& config)
{
    if (config.apiKey.isEmpty())
        return false;

    if (config.temperature < 0.0f || config.temperature > 1.0f)
        return false;

    if (config.maxTokens <= 0 || config.maxTokens > 4000)
        return false;

    if (config.timeoutSeconds <= 0 || config.timeoutSeconds > 300)
        return false;

    return true;
}

void AIConfigManager::saveConfig(const AIConfig& config)
{
    auto configFile = getConfigFile();

    juce::DynamicObject configObj;
    configObj.setProperty("provider", (int)config.provider);
    configObj.setProperty("apiKey", config.apiKey);
    configObj.setProperty("baseUrl", config.baseUrl);
    configObj.setProperty("modelName", config.modelName);
    configObj.setProperty("temperature", config.temperature);
    configObj.setProperty("maxTokens", config.maxTokens);
    configObj.setProperty("useStreaming", config.useStreaming);
    configObj.setProperty("timeoutSeconds", config.timeoutSeconds);

    auto jsonString = juce::JSON::toString(juce::var(configObj.clone()));
    configFile.replaceWithText(jsonString);
}

AIConfig AIConfigManager::loadConfig()
{
    auto configFile = getConfigFile();

    if (configFile.existsAsFile())
    {
        auto jsonString = configFile.loadFileAsString();
        auto json = juce::JSON::parse(jsonString);

        if (json.isObject())
        {
            auto obj = json.getDynamicObject();

            AIConfig config;
            config.provider = (AIProvider)(int)obj->getProperty("provider");
            config.apiKey = obj->getProperty("apiKey").toString();
            config.baseUrl = obj->getProperty("baseUrl").toString();
            config.modelName = obj->getProperty("modelName").toString();
            config.temperature = (float)obj->getProperty("temperature");
            config.maxTokens = (int)obj->getProperty("maxTokens");
            config.useStreaming = (bool)obj->getProperty("useStreaming");
            config.timeoutSeconds = (int)obj->getProperty("timeoutSeconds");

            return config;
        }
    }

    // 返回默认配置
    return getGroqConfig("");
}

juce::File AIConfigManager::getConfigFile()
{
    auto appDataDir = juce::File::getSpecialLocation(juce::File::userApplicationDataDirectory);
    auto pluginDir = appDataDir.getChildFile("AI Chord Assistant");

    if (!pluginDir.exists())
        pluginDir.createDirectory();

    return pluginDir.getChildFile("ai_config.json");
}
