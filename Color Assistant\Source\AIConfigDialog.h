/*
  ==============================================================================

    AIConfigDialog.h
    Created: AI Chord Assistant Plugin - Stage 3
    Author:  AI Assistant

    This file contains the AI configuration dialog for setting up LLM providers.

  ==============================================================================
*/

#pragma once

#include <JuceHeader.h>
#include "AIManager.h"

//==============================================================================
// AI配置对话框
class AIConfigDialog : public juce::Component,
                      public juce::Button::Listener,
                      public juce::ComboBox::Listener
{
public:
    AIConfigDialog();
    ~AIConfigDialog() override;
    
    void paint(juce::Graphics& g) override;
    void resized() override;
    
    // 显示对话框
    void showDialog(const AIConfig& currentConfig);
    
    // 回调函数
    std::function<void(const AIConfig&)> onConfigChanged;
    std::function<void()> onCancelled;
    
    // Button::Listener
    void buttonClicked(juce::Button* button) override;
    
    // ComboBox::Listener
    void comboBoxChanged(juce::ComboBox* comboBoxThatHasChanged) override;
    
private:
    // UI组件
    juce::Label titleLabel;
    
    // 提供商选择
    juce::Label providerLabel;
    juce::ComboBox providerComboBox;
    
    // API配置
    juce::Label apiKeyLabel;
    juce::TextEditor apiKeyEditor;
    juce::Label baseUrlLabel;
    juce::TextEditor baseUrlEditor;
    juce::Label modelLabel;
    juce::TextEditor modelEditor;
    
    // 参数配置
    juce::Label temperatureLabel;
    juce::Slider temperatureSlider;
    juce::Label maxTokensLabel;
    juce::Slider maxTokensSlider;
    juce::Label timeoutLabel;
    juce::Slider timeoutSlider;
    
    // 按钮
    juce::TextButton testButton;
    juce::TextButton saveButton;
    juce::TextButton cancelButton;
    juce::TextButton resetButton;
    
    // 状态显示
    juce::Label statusLabel;
    
    // 数据
    AIConfig currentConfig;
    
    // 方法
    void setupComponents();
    void updateUIFromConfig();
    void updateConfigFromUI();
    void resetToDefaults();
    void testConnection();
    void updateProviderDefaults();
    
    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR(AIConfigDialog)
};

//==============================================================================
// 增强的Chat界面组件
class EnhancedChatInterface : public juce::Component,
                             public juce::Button::Listener,
                             public juce::TextEditor::Listener
{
public:
    EnhancedChatInterface();
    ~EnhancedChatInterface() override;
    
    void paint(juce::Graphics& g) override;
    void resized() override;
    
    // 设置AI管理器
    void setAIManager(AIManager* manager);
    void setMusicSection(const MusicSection* section);
    
    // 添加消息
    void addUserMessage(const juce::String& message);
    void addAIMessage(const AIResponse& response);
    void addSystemMessage(const juce::String& message);
    
    // 清空对话
    void clearChat();
    
    // 回调函数
    std::function<void(const juce::Array<ChordData>&)> onChordsGenerated;
    std::function<void()> onConfigRequested;
    
    // Button::Listener
    void buttonClicked(juce::Button* button) override;
    
    // TextEditor::Listener
    void textEditorReturnKeyPressed(juce::TextEditor& editor) override;
    
private:
    // AI管理器
    AIManager* aiManager;
    const MusicSection* musicSection;
    
    // UI组件
    juce::TextEditor chatDisplay;
    juce::TextEditor userInput;
    juce::TextButton sendButton;
    juce::TextButton generateButton;
    juce::TextButton analyzeButton;
    juce::TextButton configButton;
    juce::TextButton clearButton;
    
    // 快捷按钮
    juce::TextButton popButton;
    juce::TextButton jazzButton;
    juce::TextButton classicalButton;
    juce::TextButton bluesButton;
    
    // 状态
    bool isProcessing;
    juce::String conversationHistory;
    
    // 方法
    void setupComponents();
    void sendMessage();
    void generateChords();
    void analyzeProgression();
    void sendQuickRequest(const juce::String& request);
    void handleAIResponse(const AIResponse& response);
    void updateChatDisplay();
    void setProcessing(bool processing);
    
    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR(EnhancedChatInterface)
};

//==============================================================================
// AI状态指示器
class AIStatusIndicator : public juce::Component,
                         public juce::Timer
{
public:
    AIStatusIndicator();
    ~AIStatusIndicator() override;
    
    void paint(juce::Graphics& g) override;
    void resized() override;
    
    // 设置AI管理器
    void setAIManager(AIManager* manager);
    
    // 状态更新
    void setStatus(const juce::String& status, juce::Colour colour = juce::Colours::green);
    void setError(const juce::String& error);
    void setProcessing(bool processing);
    
    // Timer回调
    void timerCallback() override;
    
private:
    AIManager* aiManager;
    juce::String currentStatus;
    juce::Colour statusColour;
    bool isProcessing;
    float animationPhase;
    
    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR(AIStatusIndicator)
};

//==============================================================================
// AI建议面板
class AISuggestionPanel : public juce::Component,
                         public juce::Button::Listener
{
public:
    AISuggestionPanel();
    ~AISuggestionPanel() override;
    
    void paint(juce::Graphics& g) override;
    void resized() override;
    
    // 显示建议
    void showSuggestions(const juce::Array<ChordData>& suggestions, 
                        const juce::String& explanation);
    void clearSuggestions();
    
    // 回调函数
    std::function<void(const ChordData&)> onChordSelected;
    std::function<void(const juce::Array<ChordData>&)> onAllChordsAccepted;
    
    // Button::Listener
    void buttonClicked(juce::Button* button) override;
    
private:
    // UI组件
    juce::Label titleLabel;
    juce::TextEditor explanationEditor;
    juce::Viewport chordsViewport;
    juce::Component chordsContainer;
    juce::TextButton acceptAllButton;
    juce::TextButton clearButton;
    
    // 数据
    juce::Array<ChordData> currentSuggestions;
    juce::OwnedArray<juce::TextButton> chordButtons;
    
    // 方法
    void setupComponents();
    void updateChordButtons();
    void layoutChordButtons();
    
    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR(AISuggestionPanel)
};
